module.exports = {
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'import', 'react'],
  env: {
    node: true,
  },
  extends: [
    'eslint:recommended',
    'airbnb-base',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
  ],
  rules: {
    'react/jsx-indent': ['error', 2],
    'react/jsx-max-props-per-line': [2, { when: 'multiline' }],
    'no-console': 'error',
    'max-classes-per-file': 0,
    'react/jsx-indent-props': ['error', 2],
    'no-undef': 'off',
    'comma-dangle': ['error', 'always-multiline'],
    'react/no-unknown-property': ['error', { ignore: ['css'] }],
    'react/react-in-jsx-scope': 'off',
    'import/extensions': 0,
    'import/prefer-default-export': 0,
    'max-len': ['error', { code: 170 }],
    'no-shadow': 'off',
    'no-unused-vars': 'off',
    'import/no-unresolved': 'error',
    '@typescript-eslint/no-unused-vars': [
      'error',
      { ignoreRestSiblings: true },
    ],
    '@typescript-eslint/no-shadow': 'error',
    semi: 'off',
    '@typescript-eslint/semi': ['error'],
    'import/order': [
      'error',
      {
        'newlines-between': 'always',
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        pathGroups: [
          {
            pattern: '#*/**',
            group: 'internal',
          },
        ],
      },
    ],
  },
  settings: {
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
    'import/resolver': {
      node: {
        extensions: ['.ts', '.js', '.json', '.jsx', '.tsx'],
      },
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.app.json',
      },
    },
    react: {
      version: 'detect',
    },
  },
  overrides: [
    {
      files: ['*.test.tsx'],
      rules: {
        'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
      },
    },
    {
      files: ['**/*.Worker.ts', '**/*.Worker.js'],
      env: {
        worker: true,
      },
      rules: {
        'no-restricted-globals': 'off',
      },
    },
  ],
};
