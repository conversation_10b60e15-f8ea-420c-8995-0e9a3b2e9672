# Pits Depot web repo

Frontend application of Genia. This is a submodule of pd-monorepo, more info visit [Pits Depot monorepo](https://bitbucket.org/pitsdepot/pd-monorepo).

## Build with

- [TypeScript](https://www.typescriptlang.org/)
- [React](https://react.dev/)
- [Tailwind](https://tailwindcss.com/)
- [Vite](https://vitejs.dev/)

## Prerequisites

PNPM

```BASH
npm install pnpm@latest -g
```

## Installation

### Clone

```BASH
<NAME_EMAIL>:pitsdepot/genia-web.git
cd genia-web
```

### Install packages

```BASH
pnpm install
```

### Start the server

```BASH
pnpm start:dev
```

### Build your first component
You can import the component from our custom Storybook UI library.
More info: [Storybook UI Library](https://bitbucket.org/pitsdepot/pd-storybook)

```BASH
import { But<PERSON> } from '@pitsdepot/ds-template';

function App() {
  const clicked = () => {
    console.log('I´m a button');
  };

  return (
    <div className='h-screen bg-red-100 flex justify-center'>
      <div className='my-auto flex flex-col items-center gap-8'>
        <h1 className='text-5xl mb-8'>Componente creado con Storybook</h1>
        <div className='mx-auto flex gap-8'>
          <Button children='I´m a Primary Button' color='primary' onClick={clicked} />
        </div>
      </div>
    </div>
  );
}

export default App;
```
