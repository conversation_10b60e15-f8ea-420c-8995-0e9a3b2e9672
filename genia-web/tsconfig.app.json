{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "#application/*": ["./src/lib/application/*"],
      "#appComponent/*": ["./src/lib/appComponent/*"],
      "#composition/*": ["./src/lib/composition/*"],
      "#domain/*": ["./src/lib/domain/*"],
      "#infrastructure/*": ["./src/lib/infrastructure/*"],
      "#layout/*": ["./src/lib/layout/*"],
      "#/*": ["./src/*"],
    },

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "experimentalDecorators": true,
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
}
