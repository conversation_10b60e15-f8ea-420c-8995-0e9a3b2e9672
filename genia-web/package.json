{"name": "genia-web", "private": true, "version": "1.7.2", "type": "module", "scripts": {"start:dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint --fix .", "prepare": "husky", "artifactregistry-login": "npx google-artifactregistry-auth --repo-config=./.npmrc"}, "dependencies": {"@ai-sdk/react": "^1.2.5", "@apollo/client": "^3.11.8", "@auth0/auth0-react": "^2.2.4", "@auth0/auth0-spa-js": "^2.3.0", "@pitsdepot/storybook": "^1.7.2", "ai": "^4.2.10", "axios": "^1.7.1", "dayjs": "^1.11.13", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.6.3", "graphql": "^16.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.26.2", "react-toastify": "^10.0.5", "remark-gfm": "^4.0.1", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.3.2", "assert": "^2.1.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.35.0", "globals": "^15.9.0", "husky": "^9.1.5", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "^5.4.9", "vite-plugin-environment": "^1.1.3"}}