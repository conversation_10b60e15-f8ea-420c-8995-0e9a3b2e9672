import './index.css';

import '@pitsdepot/storybook/styles/pd.css';
import {
  StrictMode,
} from 'react';
import { createRoot } from 'react-dom/client';

import MainRouter from '#infrastructure/Main.Router';
import { saveInvitationIdFromUrl } from '#infrastructure/utils/saveInvitationId';
import 'dayjs/locale/es-mx';

// Guardar invitationId de la URL si existe
saveInvitationIdFromUrl();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <MainRouter/>
  </StrictMode>,
);
