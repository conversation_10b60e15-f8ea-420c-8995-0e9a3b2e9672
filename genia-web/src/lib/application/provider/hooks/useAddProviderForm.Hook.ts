import { useEffect, useState } from 'react';

import { useCheckTributaryId } from '../../common/hooks/useCheckTributaryId.Hook';
import { Provider } from '../Provider.Type';
import { ProviderFormStep, ProviderFormStepsState } from '../types/AddProviderForm.Type';

export const useAddProviderForm = (
  initialState?: Partial<Provider>,
) => {
  const [formState, setFormState] = useState<ProviderFormStepsState>({
    currentStep: ProviderFormStep.TRIBUTARY_ID,
    tributaryId: initialState?.tributaryId || '',
    providerData: initialState || {},
    isExistingProvider: false,
  });

  const { validationResult, isValidating, validationError } = useCheckTributaryId(formState.tributaryId);

  // Constant for better readability and reusability
  const providerExists = validationResult?.exists ?? false;

  useEffect(() => {
    if (validationResult) {
      setFormState((prev) => ({
        ...prev,
        isExistingProvider: providerExists,
      }));
    }
  }, [validationResult, providerExists]);

  const updateTributaryId = (tributaryId: string) => {
    setFormState((prev) => ({
      ...prev,
      tributaryId,
    }));
  };

  const updateProviderData = (data: Partial<Provider & { inviteMode?: boolean }>) => {
    setFormState((prev) => ({
      ...prev,
      providerData: {
        ...prev.providerData,
        ...data,
      },
    }));
  };

  const goToNextStep = () => {
    if (formState.currentStep === ProviderFormStep.TRIBUTARY_ID) {
      setFormState((prev) => ({
        ...prev,
        currentStep: ProviderFormStep.PROVIDER_DETAILS,
      }));
    }
  };

  const goToPreviousStep = () => {
    if (formState.currentStep === ProviderFormStep.PROVIDER_DETAILS) {
      setFormState((prev) => ({
        ...prev,
        currentStep: ProviderFormStep.TRIBUTARY_ID,
      }));
    }
  };

  const resetForm = () => {
    setFormState({
      currentStep: ProviderFormStep.TRIBUTARY_ID,
      tributaryId: '',
      providerData: {},
      isExistingProvider: false,
    });
  };

  const canProceedToNextStep = () => {
    if (formState.currentStep === ProviderFormStep.TRIBUTARY_ID) {
      return formState.tributaryId.trim().length > 0
             && !isValidating
             && validationResult !== null;
    }
    return false;
  };

  const canSubmitForm = () => {
    if (formState.currentStep === ProviderFormStep.PROVIDER_DETAILS) {
      const {
        name, phone, address, notificationEmail, managerName, inviteMode,
      } = formState.providerData;

      if (formState.isExistingProvider) {
        return (name?.trim().length || 0) > 0;
      }

      if (inviteMode) {
        return (
          (notificationEmail?.trim().length || 0) > 0
          || (phone?.trim().length || 0) > 0
        );
      }

      return (
        (name?.trim().length || 0) > 0
        && (phone?.trim().length || 0) > 0
        && (address?.trim().length || 0) > 0
        && (notificationEmail?.trim().length || 0) > 0
        && (managerName?.trim().length || 0) > 0
      );
    }
    return false;
  };

  return {
    formState,
    validationResult,
    isValidating,
    validationError,
    providerExists,
    updateTributaryId,
    updateProviderData,
    goToNextStep,
    goToPreviousStep,
    resetForm,
    canProceedToNextStep,
    canSubmitForm,
  };
};
