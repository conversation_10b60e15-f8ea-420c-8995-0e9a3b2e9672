export interface Provider {
  id: string;
  name: string;
  tributaryId?: string | null;
  phone?: string | null;
  address?: string | null;
  notificationEmail?: string | null;
  managerName?: string | null;
  providerCompanyId?: string | null;
  companyId: string;
}

export interface ProviderResponse extends Provider {
  totalItemsProvided?: number;
   providerInventories_aggregate?: {
    aggregate: {
      count: number;
    };
  };
}
