import { Column, Row } from '@pitsdepot/storybook';

import { AnchorCell } from '#appComponent/table/AnchorCell.AppComponent';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { TotalItemsProvidedCell, TributaryIdCell } from '#appComponent/table/TableCells.Component';
import { ProviderResponse } from '#application/provider/Provider.Type';
import ApplicationRegistry from '#composition/Application.Registry';

const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: 'name',
    width: '40%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'ID Tributario',
    dataKey: 'tributaryId',
    width: '30%',
    renderer: TributaryIdCell,
  },
  {
    header: 'Total de artículos suministrados',
    dataKey: 'itemsProvided',
    width: '25%',
    renderer: TotalItemsProvidedCell,
  },
];

function mapper(items: ProviderResponse[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    name: { value: item.name, url: ApplicationRegistry.PathService.providers.viewProvider(item.id) },
    tributaryId: item.tributaryId,
    providerCompanyId: item.providerCompanyId,
    itemsProvided: item.providerInventories_aggregate?.aggregate?.count || 0,
  }));
}

export function ProvidersListModule() {
  return (
    <PaginatedListAppComponent
      title='Proveedores'
      columns={columns}
      useGetHook={ApplicationRegistry.ProviderService.useGetProviders}
      mapper={mapper}
      showHeader={false}
    />
  );
}
