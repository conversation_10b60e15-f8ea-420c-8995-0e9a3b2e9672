import { Column, FilterGroup, Row } from '@pitsdepot/storybook';
import { useMemo } from 'react';

import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import {
  AnchorCell, AttributesCell, DescriptionProductCell, PriceCellWithButton, ProductTypeCell,
} from '#appComponent/table/TableCells.Component';
import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';
import ApplicationRegistry from '#composition/Application.Registry';

type FilterType = 'company';

const columns: Column[] = [
  {
    header: 'Producto',
    dataKey: 'product',
    width: '15%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Descripción',
    dataKey: 'description',
    width: '20',
    renderer: DescriptionProductCell,
  },
  {
    header: 'Identificador',
    dataKey: 'readId',
    width: '10%',
  },
  {
    header: 'Precio',
    dataKey: 'price',
    width: '5%',
    renderer: PriceCellWithButton,
  },
  {
    header: 'Tipo de producto',
    dataKey: 'type',
    width: '10%',
    renderer: ProductTypeCell,
  },
  {
    header: 'Atributos',
    dataKey: 'attributes',
    width: '20%',
    renderer: AttributesCell,
  },
  {
    header: 'Proveedor',
    dataKey: 'providerCompanyName',
    width: '15%',
  },
];

function mapper(items: ProviderCatalogItem[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    product: { value: item.name, url: ApplicationRegistry.PathService.providers.viewProviderCatalog(item.id) },
    description: item.description,
    readId: item.readId,
    attributes: item.attributes,
    price: {
      price: item.price, name: item.name, readId: item.readId, description: item.description,
    },
    type: item.type,
    providerCompanyName: item.company.name,
  }));
}

function ProvidersCatalogListModule() {
  const { items } = ApplicationRegistry.CompanyService.useGetCompanies();

  const filters: FilterGroup<FilterType>[] = useMemo(() => ([
    {
      key: 'company',
      name: 'Proveedor',
      options: items?.map((client) => ({ id: client.id, label: client.name })) || [],
    },
  ]), [items]);

  return (
    <PaginatedListAppComponent
      title='Catálogo de Proveedores'
      columns={columns}
      useGetHook={ApplicationRegistry.ProviderService.useGetProvidersCatalog}
      mapper={mapper}
      filters={filters}
      searchTitle='Buscar por nombre, descripción, identificador...'
    />
  );
}

export default ProvidersCatalogListModule;
