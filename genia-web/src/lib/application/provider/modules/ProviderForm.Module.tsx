import {
  Button, FormComponent, IconImporter, Title,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import {
  useContext, useEffect, useState,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { Provider } from '#application/provider/Provider.Type';
import {
  CreateProviderResponse,
  UpdateProviderResponse,
} from '#application/provider/services/Provider.Service';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { ProvidersHttps, ProvidersPayload } from '#infrastructure/api/http/Providers.Https';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { useGetProvider } from '#infrastructure/implementation/application/hooks/useGetProvider.Hook';
import { useProviderFormInputs } from '#infrastructure/implementation/application/hooks/useProviderFormInputs.Hook';

import {
  EDIT_PROVIDER_TITLE,
  NEW_PROVIDER_TITLE,
  PROPERTIES_LABEL,
  PROVIDER_ADD_FAILED,
  PROVIDER_ADD_SUCCES,
  PROVIDER_UPDATE_FAILED,
  PROVIDER_UPDATE_SUCCES,
  SAVE_BTN_LABEL,
  providerFormInitialState,
} from './constants/ProviderForm.Constants';

const text = TextService.getText();

export function ProviderFormModule() {
  const { id } = useParams();
  const [isEditing, setIsEditing] = useState<boolean>(!id);
  const [defaultInputs, setDefaultInputs] = useState<Partial<Provider>>(providerFormInitialState);
  const { provider, loading, error } = useGetProvider(id);
  const { inputs, formState, resetForm } = useProviderFormInputs(defaultInputs, isEditing);
  const { getToken } = useContext(AuthContext);
  const [isLoading, setIsloading] = useState(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const navigate = useNavigate();
  const formHasErrors = (formError: boolean) => setHasError(formError);
  const providersPath = ApplicationRegistry.PathService.providers.base();

  useEffect(() => {
    if (id && provider) {
      setDefaultInputs({
        ...provider,
      });
    }
  }, [id, provider]);

  useEffect(() => {
    if (error) {
      Notification({ message: text.common.loadingErrorNotification, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [error]);

  const providerFormInputSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    setIsloading(true);
    if (hasError) {
      Notification({ message: text.common.formFieldsRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return;
    }

    const token = await getToken();

    const payload: ProvidersPayload = {
      name: formState.name?.trim(),
      tributaryId: formState.tributaryId === '' ? null : formState.tributaryId?.trim(),
    };

    try {
      const response: CreateProviderResponse | UpdateProviderResponse = !id
        ? await ProvidersHttps.postProviders(token, [payload])
        : await ProvidersHttps.updateProvider(token, id, payload);

      if (!id) {
        if (!response || !Array.isArray(response) || !response[0]?.id) {
          Notification({ message: PROVIDER_ADD_FAILED, type: MSG_ERROR_TYPES.ERROR });
          return;
        }
      }

      Notification({ message: !id ? PROVIDER_ADD_SUCCES : PROVIDER_UPDATE_SUCCES, type: MSG_ERROR_TYPES.SUCCESS });
      resetForm();
      navigate(providersPath);
    } catch (err) {
      Notification({ message: !id ? PROVIDER_ADD_FAILED : PROVIDER_UPDATE_FAILED, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleSubmitClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    providerFormInputSubmit(e);
  };

  const saveButton = isEditing
    && <Button onClick={handleSubmitClick} disabled={isLoading || hasError}>
      <div className='flex gap-2 items-center'>
        <IconImporter size={24} name='floppyDisk' />
        <span>{SAVE_BTN_LABEL}</span>
      </div>
    </Button>;

  const editButton = id && !isEditing
    && <IconImporter
      name='pencil'
      size={24}
      color={Theme.colors.dark[500]}
      onClick={() => setIsEditing(!isEditing)}
      className='text-dark-500 hover:text-dark-700 transitin-all cursor-pointer ease-in-out border border-dark-400 rounded-full p-1'
    />;

  return (
    <div className='flex flex-col w-full gap-4 p-7'>
      {loading && <p>{`${text.common.loading}...`}</p>}
      <div className='flex justify-between'>
        <Title as='h2'>{id ? EDIT_PROVIDER_TITLE : NEW_PROVIDER_TITLE}</Title>
        {saveButton}
      </div>
      <div className='bg-white rounded-2xl w-3/4 p-8 max-h-min shadow-lg flex flex-col gap-6'>
        <div className='flex justify-between'>
          <div>{PROPERTIES_LABEL}</div>
          {editButton}
        </div>
        <FormComponent
          inputs={inputs}
          formValidation={formHasErrors}
          orientation='horizontal'
          columnsNumber={2}
        />
      </div>
    </div>
  );
}
