import {
  Title,
} from '@pitsdepot/storybook';
import { useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Notification } from '../../../appComponent/common/Notification.Component';
import { StepIndicator } from '../../../appComponent/common/StepIndicator.Component';
import { AddProviderForm } from '../../../appComponent/provider/AddProviderForm.Component';
import { SearchProviderTributaryIdComponent } from '../../../appComponent/provider/SearchProviderTributaryId.Component';
import ApplicationRegistry from '../../../composition/Application.Registry';
import TextService from '../../../composition/textService/Text.Service';
import { NEW_PROVIDER_TITLE, PROVIDER_ADD_FAILED, PROVIDER_ADD_SUCCES } from '../../constants/texts/ProviderForm.Constants';
import { MSG_ERROR_TYPES } from '../../types/Notification.Type';
import { useAddProviderForm } from '../hooks/useAddProviderForm.Hook';
import { ProviderInvitationPayload, ProvidersPayload } from '../services/Provider.Service';
import { ProviderFormStep } from '../types/AddProviderForm.Type';

import {
  INVITATION_SENDED,
  PROVIDER_CONNECT_FAILED,
  PROVIDER_INVITE_FAILED,
  providerFormInitialState,
} from './constants/ProviderForm.Constants';

export function AddProviderFormModule() {
  const navigate = useNavigate();

  const [isLoading, setIsloading] = useState(false);

  const { getToken, systemUser } = useContext(ApplicationRegistry.AuthContext);

  const providersPath = ApplicationRegistry.PathService.providers.base();
  const providerService = ApplicationRegistry.ProviderService;
  const textService = TextService.getText();
  const providersText = textService.providers;

  const {
    formState: stepsFormState,
    validationResult,
    isValidating,
    validationError,
    providerExists,
    updateTributaryId,
    updateProviderData,
    goToNextStep,
    goToPreviousStep,
    resetForm: resetStepsForm,
    canSubmitForm,
  } = useAddProviderForm(providerFormInitialState);

  const { providerData } = stepsFormState;

  const handleTributaryIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateTributaryId(e.target.value);
  };

  const handleProviderDataChange = (field: string, value: string) => {
    updateProviderData({ [field]: value });
  };

  const handleConnectProvider = async () => {
    setIsloading(true);
    try {
      const token = await getToken();

      const invitations: ProviderInvitationPayload[] = [{
        invitedCompanyId: systemUser.companyId || '',
        type: 'provider',
      }];

      const response = await providerService.connectProvider(token, invitations);

      if (response[0]?.id) {
        Notification({ message: INVITATION_SENDED, type: MSG_ERROR_TYPES.SUCCESS });
        resetStepsForm();
        navigate(providersPath);
      } else {
        Notification({ message: PROVIDER_CONNECT_FAILED, type: MSG_ERROR_TYPES.ERROR });
      }
    } catch (err) {
      Notification({ message: PROVIDER_CONNECT_FAILED, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleCreateWithoutInvite = async () => {
    setIsloading(true);
    try {
      const token = await getToken();
      const payload: ProvidersPayload = {
        name: providerData.name?.trim(),
        tributaryId: stepsFormState.tributaryId?.trim(),
        phone: providerData.phone?.trim(),
        address: providerData.address?.trim(),
        notificationEmail: providerData.notificationEmail?.trim(),
        managerName: providerData.managerName?.trim(),
      };

      const response = await providerService.createProviders(token, [payload]);

      if (response[0]?.id) {
        Notification({ message: PROVIDER_ADD_SUCCES, type: MSG_ERROR_TYPES.SUCCESS });
        resetStepsForm();
        navigate(providersPath);
      } else {
        Notification({ message: PROVIDER_ADD_FAILED, type: MSG_ERROR_TYPES.ERROR });
      }
    } catch (err) {
      Notification({ message: PROVIDER_ADD_FAILED, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleInviteProvider = async () => {
    setIsloading(true);
    try {
      const token = await getToken();

      const invitations: ProviderInvitationPayload[] = [{
        type: 'provider',
        ...(providerData?.notificationEmail?.trim() && { email: providerData.notificationEmail.trim() }),
        ...(providerData?.phone?.trim() && { phoneNumber: providerData.phone.trim() }),
      }];

      const response = await providerService.inviteProvider(token, invitations);

      if (response[0]?.id) {
        Notification({ message: INVITATION_SENDED, type: MSG_ERROR_TYPES.SUCCESS });
        resetStepsForm();
        navigate(providersPath);
      } else {
        Notification({ message: PROVIDER_INVITE_FAILED, type: MSG_ERROR_TYPES.ERROR });
      }
    } catch (err) {
      Notification({ message: PROVIDER_INVITE_FAILED, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleCreateWithoutInviteAndGoNext = () => {
    updateProviderData({ inviteMode: false });
    goToNextStep();
  };

  const handleInviteProviderAndGoNext = (): void => {
    updateProviderData({ inviteMode: true });
    goToNextStep();
  };

  return (
    <div className='flex flex-col w-full gap-4 p-7 bg-dark-200 min-h-screen'>
      <div className='flex justify-between'>
        <Title as='h2'>{NEW_PROVIDER_TITLE}</Title>
      </div>

      <StepIndicator
        steps={[
          {
            id: ProviderFormStep.TRIBUTARY_ID,
            label: providersText.stepLabelTributaryId,
          },
          {
            id: ProviderFormStep.PROVIDER_DETAILS,
            label: providerData.inviteMode ? providersText.stepLabelContactInfo : providersText.stepLabelCommercialInfo,
          },
        ]}
        currentStepId={stepsFormState.currentStep}
      />

      {stepsFormState.currentStep === ProviderFormStep.TRIBUTARY_ID && (
        <SearchProviderTributaryIdComponent
          formState={{
            tributaryId: stepsFormState.tributaryId,
            isValidating,
            isLoading,
          }}
          validation={{
            error: validationError,
            result: validationResult ? {
              exists: providerExists,
            } : null,
            providerExists,
          }}
          actions={{
            onTributaryIdChange: handleTributaryIdChange,
            onConnectProvider: handleConnectProvider,
            onCreateWithoutInvite: handleCreateWithoutInviteAndGoNext,
            onInviteProvider: handleInviteProviderAndGoNext,
          }}
        />
      )}
      {stepsFormState.currentStep === ProviderFormStep.PROVIDER_DETAILS && (
        <AddProviderForm
          formData={{
            tributaryId: stepsFormState.tributaryId,
            providerData,
            inviteMode: providerData.inviteMode,
          }}
          formState={{
            isLoading,
            canSubmitForm: canSubmitForm(),
          }}
          actions={{
            onTributaryIdChange: handleTributaryIdChange,
            onProviderDataChange: handleProviderDataChange,
            onGoToPreviousStep: goToPreviousStep,
            onSubmit: providerData.inviteMode ? handleInviteProvider : handleCreateWithoutInvite,
          }}
        />
      )}
    </div>
  );
}
