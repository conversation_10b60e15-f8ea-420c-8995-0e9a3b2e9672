import { FormComponent, HierarchyComponent } from '@pitsdepot/storybook';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { useProviderCatalogInputs } from '#infrastructure/implementation/application/hooks/useProviderCatalogInputs.Hook';
import { transformData } from '#infrastructure/implementation/application/utils/transformAttributesData';

import { PROPERTIES } from '../../catalog/modules/Catalog.Constants';
import { LABELS, TOOLTIPS } from '../../constants/texts/InventoryForm.Constants';
import { TITLE } from '../../constants/texts/ProvidersCatalogDetails.Constants';

const text = TextService.getText();

const ProvidersCatalogDetailsModule = () => {
  const { id } = useParams();
  const { loading, items: catalogItems, error } = ApplicationRegistry.CatalogService.useGetCatalogItem(id);
  const catalogItem = catalogItems?.[0];
  const { inputs } = useProviderCatalogInputs(catalogItem);

  useEffect(() => {
    if (error) {
      Notification({ message: text.common.loadingErrorNotification, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [error]);

  return (
    <div className='gap-8 pl-7 w-full'>
      {loading && <p>{text.common.loading}</p>}
      <div className='flex justify-between items-center mb-2'>
        <h1 className='font-semibold text-xl my-4'>
          {TITLE}
        </h1>
      </div>
      <div className='flex bg-white rounded-2xl p-8 max-h-min shadow-lg mb-4'>
        <div className='rounded-2xl max-h-min w-full flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <h2 className='text-lg'>{PROPERTIES}</h2>
          </div>
          <div className='flex gap-8'>
            <div className='w-3/4'>
              <FormComponent
                inputs={inputs}
                orientation='horizontal'
                columnsNumber={2}
              />
            </div>
            <div className='w-1/4'>
              <HierarchyComponent
                hierarchies={transformData(catalogItem?.attributes) || []}
                labelProps={{
                  title: LABELS.ATTRIBUTES,
                  tooltip: {
                    content: TOOLTIPS.ATTRIBUTES,
                  },
                }}
                className='!p-0'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProvidersCatalogDetailsModule;
