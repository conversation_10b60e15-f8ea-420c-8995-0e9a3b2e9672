import { ProviderResponse } from '#application/provider/Provider.Type';

export const providerFormInitialState: Partial<ProviderResponse> = {
  id: '',
  name: '',
  tributaryId: '',
  phone: '',
  address: '',
  notificationEmail: '',
  managerName: '',
  providerCompanyId: '',
  providerInventories_aggregate: {
    aggregate: {
      count: 0,
    },
  },
};

export const TITLE = 'Proveedor';
export const NEW_PROVIDER_TITLE = 'Nuevo Proveedor';
export const EDIT_PROVIDER_TITLE = 'Editar Proveedor';
export const PROPERTIES_LABEL = 'Propiedades';
export const SAVE_BTN_LABEL = 'Guardar';
export const CONNECT_BTN_LABEL = 'Conectar';
export const INVITE_BTN_LABEL = 'Invitar';
export const CONTINUE_BTN_LABEL = 'Continuar';

export const DUPLICATE_PROVIDER_NAME_ERROR = 'Ya existe un proveedor con ese nombre, por favor ingresa uno diferente';

export const PROVIDER_ADD_SUCCES = 'Proveedor agregado con éxito';
export const PROVIDER_ADD_FAILED = 'Error al agregar el proveedor';
export const PROVIDER_UPDATE_SUCCES = 'Proveedor actualizado con éxito';
export const PROVIDER_UPDATE_FAILED = 'Error al actualizar proveedor';

export const PROVIDER_CONNECT_SUCCESS = 'Proveedor conectado con éxito';
export const PROVIDER_CONNECT_FAILED = 'Error al conectar con el proveedor';
export const PROVIDER_INVITE_SUCCESS = 'Invitación enviada con éxito';
export const PROVIDER_INVITE_FAILED = 'Error al enviar la invitación';

export const INVITATION_SENDED = 'Invitación enviada a su proveedor';

export const STEP_TITLES = {
  TRIBUTARY_ID: 'Identificación del Proveedor',
  PROVIDER_DETAILS: 'Detalles del Proveedor',
};

export const STEP_DESCRIPTIONS = {
  TRIBUTARY_ID: 'Ingresa el ID tributario del proveedor para verificar si ya está registrado en la plataforma',
  PROVIDER_DETAILS: 'Completa la información comercial y de contacto del proveedor',
};

export const LABELS = {
  NAME: 'Nombre de la Empresa',
  TRIBUTARY_ID: 'ID Tributario',
  PHONE: 'Teléfono',
  ADDRESS: 'Dirección',
  NOTIFICATION_EMAIL: 'Email de notificaciones',
  MANAGER_NAME: 'Nombre del encargado',
  MEDIA: 'Logo de la Empresa',
};

export const INPUT_NAME = {
  NAME: 'name',
  TRIBUTARY_ID: 'tributaryId',
  PHONE: 'phone',
  ADDRESS: 'address',
  NOTIFICATION_EMAIL: 'notificationEmail',
  MANAGER_NAME: 'managerName',
  MEDIA: 'image',
};

export const PLACEHOLDERS = {
  NAME: 'Ej: Distribuidora ABC S.A. de C.V.',
  TRIBUTARY_ID: 'Ej: ABC123456789 (sin espacios)',
  PHONE: 'Ej: +52 55 1234 5678',
  ADDRESS: 'Ej: Av. Insurgentes Sur 123, Col. Roma Norte, CDMX, CP 06700',
  NOTIFICATION_EMAIL: 'Ej: <EMAIL>',
  MANAGER_NAME: 'Ej: María González López',
};

export const TOOLTIPS = {
  NAME: 'Razón social o nombre comercial completo de la empresa proveedora',
  TRIBUTARY_ID: 'ID tributario o identificador fiscal según las leyes de tu país. Sin espacios ni guiones. Ejemplo: ABC123456789',
  PHONE: 'Número telefónico principal para contacto comercial. Incluye código de país si es internacional',
  ADDRESS: 'Dirección fiscal completa donde está registrada la empresa. Incluye calle, número, colonia, ciudad y código postal',
  NOTIFICATION_EMAIL: 'Correo electrónico donde se enviarán facturas, órdenes de compra y notificaciones importantes del sistema',
  MANAGER_NAME: 'Nombre completo del representante legal o persona autorizada para tomar decisiones comerciales',
  MEDIA: 'Logo oficial de la empresa en formato PNG o JPG. Se mostrará en documentos y reportes',
};
