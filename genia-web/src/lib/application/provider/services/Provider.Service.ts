import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { Provider } from '#application/provider/Provider.Type';
import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';

export interface ProvidersPayload {
  name: string | undefined;
  tributaryId: string | null | undefined;
  phone?: string | null | undefined;
  address?: string | null | undefined;
  notificationEmail?: string | null | undefined;
  managerName?: string | null | undefined;
}

export interface ProviderInvitationPayload {
  email?: string;
  phoneNumber?: string;
  invitedCompanyId?: string;
  type?: string;
}

export interface CreateProviderResponse extends Array<Provider> {}

export interface UpdateProviderResponse {
  status: number;
  data?: Provider;
  message?: string;
}

export interface ConnectProviderResponse extends Array<{
  id: string;
  companyId: string;
  invitedCompanyId: string;
  email: string | null;
  phoneNumber: string | null;
  type: string;
  state: string;
  createdAt: string;
  updatedAt: string;
}> {}

export interface InviteProviderResponse extends Array<{
  id: string;
  companyId: string;
  invitedCompanyId: string;
  email: string | null;
  phoneNumber: string | null;
  type: string;
  state: string;
  createdAt: string;
  updatedAt: string;
}> {}

export interface CheckTributaryIdResponse {
  status: number;
  data?: {
    exists: boolean;
    providerData?: {
      name: string;
      notificationEmail: string;
      phone: string;
    };
  };
  message?: string;
}

export interface ProviderService {
  useGetProviders: (props: GetTableDataProps) => GetTableDataResponse<Provider>
  useGetProvidersCatalog: (props: GetTableDataProps) => GetTableDataResponse<ProviderCatalogItem>
  useGetProvidersWithCompanyId: (props: GetTableDataProps) => GetTableDataResponse<Provider>
  createProviders: (token: string | void, payload: ProvidersPayload[]) => Promise<CreateProviderResponse>
  updateProvider: (token: string | void, id: string, payload: ProvidersPayload) => Promise<UpdateProviderResponse>
  connectProvider: (token: string | void, invitations: ProviderInvitationPayload[]) => Promise<ConnectProviderResponse>
  inviteProvider: (token: string | void, invitations: ProviderInvitationPayload[]) => Promise<InviteProviderResponse>
  checkTributaryId: (token: string | void, tributaryId: string) => Promise<CheckTributaryIdResponse>
}
