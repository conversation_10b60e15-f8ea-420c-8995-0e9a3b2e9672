import { StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';

export const STORE_DISCOUNT_ADD_FAILED = 'Error al agregar el descuento de tienda';
export const STORE_DISCOUNT_UPDATE_FAILED = 'Error al actualizar el descuento de tienda';

export const CREATE_STORE_DISCOUNT = 'Crear descuento de tienda';
export const UPDATE_STORE_DISCOUNT = 'Actualizar descuento de tienda';
export const PROPERTIES_SUBHEADER = 'Propiedades';

export const storeDiscountInitialState: StoreDiscountItem = {
  id: '',
  name: '',
  discountType: 'amount',
  discountValue: 0,
  requiredAmount: 0,
  startDate: '',
  endDate: '',
  disabledAt: null,
};

export const INPUT_NAME = {
  NAME: 'name',
  DISCOUNT_TYPE: 'discountType',
  DISCOUNT_VALUE: 'discountValue',
  REQUIRED_AMOUNT: 'requiredAmount',
  START_DATE: 'startDate',
  END_DATE: 'endDate',
  ENABLE_DISABLE: 'active',
};

export const PLACEHOLDERS = {
  NAME: 'Nombre del descuento',
  DISCOUNT_VALUE: 'Ej: 10',
  REQUIRED_AMOUNT: 'Ej: 100',
};

export const TOOLTIPS = {
  NAME: 'Nombre descriptivo del descuento. Ej: Descuento de verano',
  DISCOUNT_TYPE: 'Selecciona si quieres hacer descuentos en porcentajes o en montos',
  DISCOUNT_VALUE: 'Valor del descuento a aplicar. Si tipo de descuento es porcentaje, el valor debe ser entre 0 y 100',
  REQUIRED_AMOUNT: 'Monto de compra para activar el descuento. Si el monto de compra es 0, el descuento se activará siempre',
  START_DATE: 'Fecha de inicio del descuento',
  END_DATE: 'Fecha de fin del descuento',
};
