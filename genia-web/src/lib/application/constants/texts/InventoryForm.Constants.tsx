/* eslint-disable max-len */

import { InfoProps } from '@pitsdepot/storybook';

import { InventoryItem } from '#application/deprecated/DashboardPages.Type';

export const INVENTORY_ADD_SUCCES = 'Agregado al inventario con éxito';
export const INVENTORY_ADD_FAILED = 'Error al agregar al inventario';
export const INVENTORY_UPDATE_SUCCES = 'Actualizado en el inventario con éxito';
export const INVENTORY_UPDATE_FAILED = 'Error al actualizar en el inventario';
export const RESPONSE_ERROR = 'Error en la respuesta';
export const UPDATE_BUTTON = 'Actualizar Item';
export const ADD_BUTTON = 'Agregar Item al inventario';
export const DUPLICATED_SKU_ERROR = 'Ya existe un producto con este SKU, por favor intenta con uno diferente';
export const SAVE = 'Guardar';
export const NEW_INVENTORY_ITEM = 'Nueva pieza de inventario';
export const UPDATE_INVENTORY_ITEM = 'Actualizar pieza de inventario';
export const NAME_REQUIRED = 'El nombre es requerido';
export const ATTRIBUTES_ERROR = 'Tienes errores agregando los atributos';
export const STOCK_ERRORS = 'El valor del stock debe ser mayor a 0';
export const DESCRIPTION_LENGTH_FAILED = 'La descripcion no puede ser mayor a 500 caracteres';
export const ERROR_DESCRIPTION = 'La descripcion es requerida';

export const PLACEHOLDERS = {
  PRODUCT_NAME: 'Ej: Pastillas de Freno Delanteras - Marca Acme - Toyota Corolla 2018...',
  SKU_AUTOGENERATED: 'Autogenerado...',
  SKU: 'SKU12345',
  DESCRIPTION: 'Ej: Diseñadas para soportar altas temperaturas y frenadas bruscas...',
};

export const LABELS = {
  PRODUCT_NAME: 'Nombre del Producto',
  SKU: 'SKU',
  SKU_AUTOGENERATE: 'Autogenerar',
  TYPE: 'Tipo',
  DESCRIPTION: 'Descripción',
  ADD_IMAGE: 'Agregar Imagen',
  STOCK: 'Stock',
  STOCK_DISABLED: 'Desactivar Stock',
  MEASUREMENT_UNIT: 'Unidad de Medida',
  ATTRIBUTES: 'Atributos',
};

export const TOOLTIPS = {
  PRODUCT_NAME: 'Introduzca un nombre descriptivo para identificar el producto. Ejemplo: \'Kit de embrague marca ACME  Mazda Allegro 2012\', \'Bomba de dirección hidráulica\'',
  SKU: 'Código único para identificar el producto en el inventario. Puede ser alfanumérico. Ejemplo: \'SKU12345\'. Si no lo tiene, puede seleccionar autogenerar',
  TYPE: 'Selecciona el tipo al que pertenece el producto.Ejemplo: \'Entrada de Producto\', \'Commodity\'',
  DESCRIPTION: 'Proporciona una breve descripción del producto. Puede incluir características, uso o detalles relevantes.',
  ADD_IMAGE: 'Sube una imagen clara y representativa del producto. Funcionalidad temporalmente no disponible',
  STOCK: 'Cantidad actual del producto disponible en el inventario. Puede desactivar esta opción si no requiere validación de existencias.',
  MEASUREMENT_UNIT: 'Define cómo se mide el producto. Ejemplo: \'unidades\', \'kilogramos\', \'litros\', etc.',
  ATTRIBUTES: 'Añade características organizadas del producto. Cada categoría que crees debe contener al menos una subcategoría o uno o más valores (por ejemplo, \'Color\' con valores \'Rojo\', \'Azul\'). Puedes anidar hasta 4 niveles de subcategorías si es necesario. El ultimo elemento de una categoría debe ser un valor.',
};

export const INPUT_NAME = {
  NAME: 'name',
  SKU: 'sku',
  TYPE: 'type',
  DESCRIPTION: 'description',
  STOCK: 'stock',
  MEASUREMENT_UNIT: 'measurementUnit',
  IMAGE: 'image',
};

export const inventoryAdditionInitialState: Partial<InventoryItem> = {
  name: '',
  sku: null,
  description: '',
  stock: 0,
  restrictedStock: 0,
  attributes: null,
  standardIdentifier: '',
  type: 'product_input',
  measurementUnit: 'Unidades',
  hasStockValidation: true,
};

export const attributesInfo: InfoProps = {
  tooltip: {
    content: TOOLTIPS.ATTRIBUTES,
    position: 'bottom',
  },
};
