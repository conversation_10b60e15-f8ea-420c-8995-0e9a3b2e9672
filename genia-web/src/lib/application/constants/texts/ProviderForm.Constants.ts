import { Provider } from '#application/provider/Provider.Type';

export const providerFormInitialState: Partial<Provider> = {
  name: '',
  tributaryId: '',
};

export const TITLE = 'Proveedor';
export const NEW_PROVIDER_TITLE = 'Nuevo Proveedor';
export const EDIT_PROVIDER_TITLE = 'Editar Proveedor';

export const DUPLICATE_PROVIDER_NAME_ERROR = 'Ya existe un proveedor con ese nombre, por favor ingresa uno diferente';

export const PROVIDER_ADD_SUCCES = 'Proveedor agregado con éxito';
export const PROVIDER_ADD_FAILED = 'Error al agregar el proveedor';
export const PROVIDER_UPDATE_SUCCES = 'Proveedor actualizado con éxito';
export const PROVIDER_UPDATE_FAILED = 'Error al actualizar proveedor';

export const LABELS = {
  NAME: 'Nombre',
  TRIBUTARY_ID: 'ID Tributario',
  MEDIA: 'Agregar Imagen',
};

export const INPUT_NAME = {
  NAME: 'name',
  TRIBUTARY_ID: 'tributaryId',
  MEDIA: 'image',
};

export const PLACEHOLDERS = {
  NAME: 'Ejemplo: Proveedor marca Acme',
  TRIBUTARY_ID: 'Ejemplo: XAXX010101000',
};

export const TOOLTIPS = {
  NAME: 'Ingresa el nombre de tu proveedor. Ejemplo: Proveedor marca Acme',
  TRIBUTARY_ID: 'Ingresa el Identificador Tributario de tu proveedor de acuerdo a las leyes fiscales de tu país. No uses espacios en blanco. Ejemplo: XAXX010101000',
  MEDIA: 'Sube una imagen clara y representativa de la identidad gráfica de tu Proveedor. Funcionalidad temporalmente no disponible',
};
