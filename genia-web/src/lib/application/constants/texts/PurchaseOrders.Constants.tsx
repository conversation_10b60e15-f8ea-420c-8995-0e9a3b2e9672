export const PROVIDER_LABEL = 'Proveedor';

export const SHIPPING_ORDER = {
  LABELS: {
    ADDRESS: 'Dirección de envío',
    DATE: 'Fecha de envío',
  },
  PLACEHOLDERS: {
    ADDRESS: 'Dirección de envío',
    DATE: 'Fecha de envío',
  },
};

export const purchaseGeneralOrdersInitialState = {
  deliveryDate: null,
  notes: '',
};

export const purchaseOrderItem = {
  id: '',
  name: '',
  sku: '',
  quantity: 0,
  unitCost: 0,
  tax: 0,
  total: 0,
};

export const PURCHASE_ORDER_MESSAGES = {
  NEW_PURCHASE_ORDER: 'Nueva Orden de Compra',
  ORDER_NUMBER_PREFIX: 'Orden de Compra',
  INVALID_DELIVERY_DATE: 'Fecha de entrega no válida',
  MISSING_PROVIDER: 'Debe seleccionar un proveedor',
  EMPTY_ROWS: 'Debe agregar al menos un item',
  MISSING_GENERAL_DATA: 'Datos generales incompletos',
  DUPLICATE_INVENTORY_IDS: 'No se puede crear una orden con productos de inventario duplicados',
};

export const PURCHASE_ORDER_TABLE_PROPERTIES = {
  HEADERS: {
    ITEM_NAME: 'Producto',
    SKU: 'Numero Identificador',
    QTY: 'Cantidad',
    COST_UNT: 'Costo Unitario',
    AFTER_DISCOUNT: 'Después de descuento',
    TAX: 'Impuesto',
    TOTAL: 'Total',
    EMPTY: '',
  },
  FIELDS: {
    NAME: 'name',
    READ_ID: 'readId',
    SKU: 'sku',
    QUANTITY: 'quantity',
    UNIT_COST: 'unitCost',
    AFTER_DISCOUNT: 'unitPriceAfterDiscount',
    TAX: 'tax',
    TOTAL: 'total',
    TRASH: 'trash',
    PURCHASE_TAX: 'purchaseTax',
  },
};

export const PURCHASE_ORDER_LIST_PROPERTIES = {
  HEADERS: {
    ITEM_NAME: 'Producto',
    SKU: 'Numero Identificador',
    QTY: 'Cantidad',
    COST_UNT: 'Costo Unitario',
    TAX: 'Impuesto',
    EMPTY: '',
    ID: 'Id',
    PROVIDER: 'Proveedor',
    TOTAL: 'Total',
    CREATED_AT: 'Fecha de creación',
    UPDATED_AT: 'Ultima actualización',
  },
  PROPERTIES: {
    READ_ID: 'readId',
    PROVIDER_NAME: 'providerName',
    TOTAL_AMOUNT: 'totalAmount',
    CREATED_AT: 'createdAt',
    UPDATED_AT: 'updatedAt',
    PROVIDER_ID: 'providerId',
    DELIVERY_DATE: 'deliveryDate',
  },
};

export const PURCHASE_ORDER_ITEM_LABELS = {
  LABELS: {
    DELIVERY_DATE: 'Fecha de entrega',
    NOTES: 'Notas',
    NAME: 'Nombre',
    SKU: 'Identificador de producto',
    QUANTITY: 'Cantidad',
    COST: 'Precio unitario',
    TAX: 'Precio/unitario con descuento',
    TOTAL: 'Total',
    UNIT_PRICE_AFTER_DISCOUNT: 'Precio unitario despues de descuento',
    UNIT_PRICE_AFTER_DISCOUNT_AND_TAXES: 'Total unitario',
  },
  PLACEHOLDERS: {
    DELIVERY_DATE: 'DD/MM/YYYY',
    NAME: 'Nombre de la pieza',
    SKU: 'RED-3000',
    PRICE: '$ 00.00',
    QUANTITY: '0',
    SHIP_ADDRESS: 'Agrega una direccion',
    SEARCH_BY_ID: 'Buscar por id',
    UNIT_PRICE_AFTER_DISCOUNT: '$ 00.00',
    UNIT_PRICE_AFTER_DISCOUNT_AND_TAXES: '$ 00.00',
  },
  FIELDS: {
    NAME: 'name',
    SKU: 'sku',
    QUANTITY: 'quantity',
    UNIT_COST: 'unitCost',
    TAX: 'tax',
    TOTAL: 'Total',
    TRASH: 'trash',
    PURCHASE_TAX: 'purchaseTax',
    UNIT_PRICE_AFTER_DISCOUNT: 'unitPriceAfterDiscount',
    UNIT_PRICE_AFTER_DISCOUNT_AND_TAXES: 'unitPriceAfterDiscountAndTaxes',
  },
  TOOLTIPS: {
    DELIVERY_DATE: 'Ingresa la fecha cuando quieras que se envie la orden',
  },
  FILTERS: {
    PROVIDERS: 'Proveedores',
  },
};

export const PURCHASE_ORDER_TABLE = {
  PURCHASE_ORDERS_TITLE: 'Ordenes de compra',
  SEARCH_BY_ID_PLACEHOLDER: 'Buscar por id',
  ADD_NEW_PURCHASE_ORDER: 'Nueva orden de compra',
  ADD_NEW_PIECE: 'Agregar nueva pieza',
  DELETE_BUTTON: 'Eliminar',
};
