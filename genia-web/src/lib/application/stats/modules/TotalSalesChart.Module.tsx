import {
  BaseSelectInput,
  CircleLoader, LineChartComponent,
  SelectContent, SelectGroup,
  SelectItem, SelectLabel, SelectTrigger, SelectValue,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { useState } from 'react';

import { PriceCell } from '#appComponent/table/TableCells.Component';
import { StatsPeriod } from '#application/stats/Stats.Type';
import ApplicationRegistry from '#composition/Application.Registry';

interface TotalSalesChartModuleProps {
  className?: string;
}

function TotalSalesChartModule(props: TotalSalesChartModuleProps) {
  const { className } = props;
  const [period, setPeriod] = useState<StatsPeriod>('last30Days');
  const { items, total, loading } = ApplicationRegistry.StatsService.useGetTotalSalesByPeriod({ period });
  const monthNames = [
    'Enero', 'Febrero', 'Mar<PERSON>', 'Abril', 'Mayo', '<PERSON><PERSON>',
    '<PERSON>', 'Agosto', 'Septiembre', 'Oct<PERSON>re', 'Noviem<PERSON>', 'Diciembre',
  ];

  const series = [
    {
      key: 'sales',
      label: 'Total',
      color: Theme.colors.primary,
    },
  ];

  const filteredData = (period === 'byQuarter' || period === 'bySemester')
    ? (items || []).slice(-4)
    : (items || []);

  const data = filteredData.map((item) => {
    if (period === 'lastYear') {
      const monthNumber = parseInt(item.period.slice(-2), 10);
      return {
        month: monthNames[monthNumber - 1] || item.period.slice(-2),
        sales: item.total,
        day: '',
      };
    }
    if (period === 'byQuarter' || period === 'bySemester') {
      return {
        day: item?.period,
        sales: item?.total,
        month: '',
      };
    }
    return {
      day: item?.period?.slice(-2) || '',
      sales: item?.total,
      month: '',
    };
  });

  const xAxisKey = period === 'lastYear' ? 'month' : 'day';
  const tickLength = period === 'lastYear' ? 3 : undefined;
  const showAllTicks = period === 'byQuarter' || period === 'bySemester' || period === 'lastYear';
  const margin = showAllTicks ? 30 : 0;

  return (
    <div className={`flex-1 box-border min-w-middle max-w-middle lg:min-w-third lg:max-w-third ${className}`}>
      <div className="flex flex-col flex-1 bg-white rounded-lg shadow-md p-4 gap-2 h-full relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10">
            <CircleLoader />
          </div>
        )}
        <div className={`flex justify-between items-center ${loading ? 'opacity-30 pointer-events-none' : ''}`}>
          <div className='flex flex-col flex-1'>
            <div className='font-medium text-xl'>
              Ventas totales
            </div>
            <div>
              {PriceCell(total, '!mr-0')}
            </div>
          </div>
          <BaseSelectInput value={period} onValueChange={(value) => setPeriod(value as StatsPeriod)}>
            <SelectTrigger className='flex-1'>
              <SelectValue placeholder='test' />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Periodo</SelectLabel>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="last30Days">Últimos 30 días</SelectItem>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="thisMonth">Este mes</SelectItem>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="lastMonth">Mes pasado</SelectItem>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="byQuarter">Por trimestre</SelectItem>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="bySemester">Por semestre</SelectItem>
                <SelectItem className='hover:bg-gray-100 hover:cursor-pointer' value="lastYear">Último año</SelectItem>
              </SelectGroup>
            </SelectContent>
          </BaseSelectInput>
        </div>
        <LineChartComponent
          data={data || []}
          series={series}
          xAxisKey={xAxisKey}
          lineType='linear'
          tickLength={tickLength}
          interval={showAllTicks ? 0 : undefined}
          marginRight={margin}
          marginLeft={margin}
          tickStyles={{ fontSize: period === 'lastYear' ? 8 : 12 }}
        />
      </div>
    </div>
  );
}

export default TotalSalesChartModule;
