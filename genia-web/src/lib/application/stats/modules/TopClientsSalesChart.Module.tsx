import {
  BarChartComponent,
  CircleLoader,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';

import ApplicationRegistry from '#composition/Application.Registry';

interface TopClientsSalesChartProps {
  className?: string;
}

function TopClientsSalesChart(props: TopClientsSalesChartProps) {
  const { className } = props;
  const { items, loading } = ApplicationRegistry.StatsService.useGetTopClients({});

  const series = [
    {
      key: 'sales',
      label: 'Total',
      color: Theme.colors.primary,
    },
  ];

  const topClients = items.slice(0, 5);
  const colorSteps = [600, 500, 400, 300, 200, 100];

  const data = topClients.map((item, index) => ({
    name: item.name,
    sales: item.total,
    fill: Theme.colors.primaryVariants[colorSteps[index] as keyof typeof Theme.colors.primaryVariants] || Theme.colors.primary,
  }));

  return (
    <div className={`flex-1 box-border min-w-middle max-w-middle lg:min-w-third lg:max-w-third ${className}`}>
      <div className="flex flex-col  bg-white rounded-lg shadow-md p-4 gap-2 h-full relative">
        {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10">
          <CircleLoader />
        </div>
        )}

        <div className='flex flex-col flex-1'>
          <div className='font-medium text-xl'>
            Top 5 Clientes
          </div>
        </div>
        <BarChartComponent
          data={data || []}
          series={series}
          vertical
          yAxisKey='name'
          tickWidth={80}
          tickStyles={{ fontSize: '12px' }}
          tickLength={10}
        />
      </div>
    </div>
  );
}

export default TopClientsSalesChart;
