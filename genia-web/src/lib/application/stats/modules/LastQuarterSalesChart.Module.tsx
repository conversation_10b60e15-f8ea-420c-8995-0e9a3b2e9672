import {
  Bar<PERSON>hartComponent,
  CircleLoader,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';

import { PriceCell } from '#appComponent/table/TableCells.Component';
import ApplicationRegistry from '#composition/Application.Registry';

interface LastQuarterSalesChartProps {
  className?: string;
}

function LastQuarterSalesChart(props: LastQuarterSalesChartProps) {
  const { className } = props;
  const period = 'lastYear';
  const { items, total, loading } = ApplicationRegistry.StatsService.useGetTotalSalesByPeriod({ period });

  const series = [
    {
      key: 'sales',
      label: 'Total',
      color: Theme.colors.primary,
    },
  ];

  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre',
  ];

  const lastSixItems = items.slice(-6).reverse();
  const colorSteps = [600, 500, 400, 300, 200, 100];
  const data = lastSixItems.map((item, index) => {
    const monthNumber = parseInt(item.period.slice(-2), 10);
    return {
      month: monthNames[monthNumber - 1] || item.period.slice(-2),
      sales: item.total,
      fill: Theme.colors.primaryVariants[colorSteps[index] as keyof typeof Theme.colors.primaryVariants] || Theme.colors.primary,
    };
  });

  return (
    <div className={`flex-1 box-border min-w-middle max-w-middle lg:min-w-third lg:max-w-third ${className}`}>
      <div className="flex flex-col flex-1 bg-white rounded-lg shadow-md p-4 gap-2 h-full relative">
        {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10">
          <CircleLoader />
        </div>
        )}
        <div className='flex flex-col flex-1'>
          <div className='font-medium text-xl'>
            Últimos 6 meses
          </div>
          <div>
            {PriceCell(total, '!mr-0')}
          </div>
        </div>
        <BarChartComponent
          data={data || []}
          series={series}
          vertical
          xAxisKey='sales'
          yAxisKey='month'
          tickStyles={{ fontSize: '12px' }}
          tickWidth={80}
        />
      </div>
    </div>
  );
}

export default LastQuarterSalesChart;
