export interface StatsByPeriod {
    total: number;
    period: string;
}

export interface TopStats {
    id: string;
    name: string;
    total: number;
}

export type StatsPeriod =
    | 'last30Days'
    | 'thisMonth'
    | 'lastMonth'
    | 'byQuarter'
    | 'bySemester'
    | 'lastYear';

export interface GetStatsDataProps {
    period?: StatsPeriod;
}

export interface GetStatsDataResponse<T> {
    total: number;
    items: T[];
    loading: boolean;
    error: Error | string | null;
}
