import {
  Button,
  Column, FilterGroup, Row,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { ChipCellComponent } from '#appComponent/common/ChipCell.component';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { AnchorCell, GenericCell, PriceCell } from '#appComponent/table/TableCells.Component';
import { statusParam } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { checkedFilters } from '#application/common/utils/CheckFilters.Util';
import { setUrlParams } from '#application/common/utils/SetUrlParams.Util';
import ApplicationRegistry from '#composition/Application.Registry';
import { Order } from '#domain/aggregates/order/Order.Entity';

type FilterType = 'clientId' | 'status';

const renderDateChip = (value: string) => (
  <ChipCellComponent color={Theme.colors.dark[200]} text={value} />
);

const columns: Column[] = [
  {
    header: 'Id',
    dataKey: 'readId',
    width: '10%',
    renderer: AnchorCell,
  },
  {
    header: 'Estado',
    dataKey: 'state',
    width: '10%',
    renderer: (value: string) => <ChipCellComponent
      color={statusParam[value].color}
      text={statusParam[value].name} />,
  },
  {
    header: 'Cliente',
    dataKey: 'clientName',
    width: '20%',
    renderer: AnchorCell,
  },
  {
    header: 'Usuario',
    dataKey: 'userEmail',
    width: '20%',
    renderer: GenericCell,
  },
  {
    header: 'Total',
    dataKey: 'totalAmount',
    width: '10%',
    renderer: PriceCell,
  },
  {
    header: 'Fecha de creación',
    dataKey: 'createdAt',
    width: '15%',
    renderer: renderDateChip,
  },
  {
    header: 'Fecha de actualización',
    dataKey: 'updatedAt',
    width: '15%',
    renderer: renderDateChip,
  },
];

function mapper(items: Order[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    readId: { value: item.orderInfo.orderNumber, url: ApplicationRegistry.PathService.saleOrders.viewSaleOrder(item.id) },
    state: item.orderInfo.status.id,
    clientName: {
      value: item.receiver.name,
      url: item.receiver.id ? ApplicationRegistry.PathService.clients.viewClient(item.receiver.id) : undefined,
    },
    userEmail: item.orderInfo.assignedUser?.email,
    totalAmount: item.orderInfo.summary.total,
    createdAt: item.createdAt ? new Date(item.createdAt).toLocaleString('es-MX', { hour12: true }) : 'No disponible',
    updatedAt: item.updatedAt ? new Date(item.updatedAt).toLocaleString('es-MX', { hour12: true }) : 'No disponible',
  }));
}

export function SaleOrderListModule() {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [filtersFromUrl, setFiltersFromUrl] = useState({});
  const [filters, setFilters] = useState<FilterGroup<FilterType>[]>([]);

  const goToPath = ApplicationRegistry.PathService.useGoToPath();

  const { items } = ApplicationRegistry.ClientService.useGetClients({
    limit: 100, offset: 0, orderBy: 'name', order: 'asc',
  });

  const baseFilters: FilterGroup<FilterType>[] = useMemo(() => ([
    {
      key: 'clientId',
      name: 'Clientes',
      options: items?.map((client) => ({ id: client.id, label: client.name })) || [],
    },
    {
      key: 'status',
      name: 'Estado',
      options: [
        { id: 'pending', label: 'Pendiente' },
        { id: 'processing', label: 'Procesando' },
        { id: 'in_transit', label: 'En tránsito' },
        { id: 'completed', label: 'Completada' },
        { id: 'cancelled', label: 'Cancelada' },
        { id: 'in_review', label: 'En revisión' },
      ],
    },
  ]), [items]);

  const onRedirect = () => {
    goToPath(ApplicationRegistry.PathService.saleOrders.addSaleOrder());
  };

  useEffect(() => {
    if (location.search.length === 0) {
      setFiltersFromUrl({});
    }
  }, [JSON.stringify(location)]);

  useEffect(() => {
    const clientIdFromUrl = queryParams.getAll('clientId');
    const statusParams = queryParams.getAll('status');

    const filtersUrl = {
      clientId: clientIdFromUrl,
      status: statusParams,
    };

    setFiltersFromUrl(filtersUrl);
  }, []);

  useEffect(() => {
    setFilters(checkedFilters(baseFilters, filtersFromUrl));
  }, [baseFilters, filtersFromUrl]);

  return (
    <PaginatedListAppComponent
      title='Órdenes de Venta'
      showHeader={false}
      columns={columns}
      useGetHook={ApplicationRegistry.SaleOrderService.useGetSaleOrders}
      mapper={mapper}
      filters={filters}
      searchTitle= 'Buscar por id'
      filtersFromUrl={filtersFromUrl}
      setUrlParams={setUrlParams}
      headerButton={
        <Button onClick={onRedirect} className="self-end" size="small">
          Nueva orden de venta
        </Button>
      }
    />
  );
}
