import {
  useCallback,
  useEffect,
  useState,
} from 'react';

import { DropDownSearchAppComponentOption, DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { ProductDropDownProduct } from '#appComponent/common/orders/productDropDown/ProductDropDown.Context';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import { ConditionParams, CreateOrderModule } from '#application/common/modules/CreateOrder.Module';
import { OrderConditions } from '#application/common/orders/Order.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';

function useFetchCatalog(excludedItems: string[]): DropDownSearchFetch {
  return (params: {searchText: string, page: number}) => {
    const [options, setOptions] = useState<ProductDropDownProduct[]>([]);
    const mapCatalog = (catalog: CatalogItem[]) => catalog.map((item) => ({
      id: item.id,
      name: item.name,
      productId: item.readId || '',
      image: item.catalog_media?.[0]?.url || '',
    })).filter((excludedItem) => !excludedItems.includes(excludedItem.id));

    const {
      items: catalog = [], loading,
    } = ApplicationRegistry.CatalogService.useGetCatalogs({
      limit: 20,
      offset: (params.page - 1) * 20,
      searchTerm: params.searchText,
      orderBy: 'name',
      order: 'asc',
    });

    useEffect(() => {
      setOptions(mapCatalog(catalog));
    }, [JSON.stringify(catalog)]);

    const customRefetch = () => {
      setOptions(mapCatalog(catalog));
    };

    return {
      options,
      refetch: customRefetch,
      isLoading: loading,
    };
  };
}

function useGetInventoryRelations(ids: string[]): ({
  items: Record<string, OrderItemInventoryRelation[]>;
  error: unknown;
  loading: boolean;
}) {
  const { items, error, loading } = ApplicationRegistry.CatalogService.useGetCatalogInventoryByIds(ids);

  const mappedItems = items.reduce((acc, item) => {
    const mappedItem: OrderItemInventoryRelation = {
      id: item.inventoryId,
      image: item.image,
      name: item.name,
      quantity: item.quantity,
      sku: item.sku,
      total: item.quantity,
    };

    const catalogId = item.catalogId || '';
    if (!acc[catalogId]) {
      acc[catalogId] = [];
    }
    acc[catalogId].push(mappedItem);
    return acc;
  }, {} as Record<string, OrderItemInventoryRelation[]>);

  return {
    items: mappedItems,
    error,
    loading,
  };
}

function useFetchClients(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const {
    items: clients = [], loading,
  } = ApplicationRegistry.ClientService.useGetClients({
    limit: 20,
    offset: (params.page - 1) * 20,
    searchTerm: params.searchText,
    orderBy: 'name',
    order: 'asc',
  });

  const options = clients.map((client) => ({
    id: client.id,
    name: client.name,
  }));

  return {
    options,
    refetch: () => {},
    isLoading: loading,
  };
}

function useGetConditions(params: ConditionParams): ({
  conditions: OrderConditions | null;
  error: unknown;
  loading: boolean;
}) {
  const { conditions, error, loading } = ApplicationRegistry.SaleOrderService.useGetConditions({
    ...params,
    orderItems: params.orderItems?.map((item) => ({
      ...item,
      catalogId: item.referenceId || undefined,
    })) || [],
    clientId: params.receiverId || undefined,
  });

  return {
    conditions,
    error,
    loading,
  };
}

export function CreateSaleOrderModule() {
  const [excludedItems, setExcludedItems] = useState<string[]>([]);

  const { applyOrderSave } = ApplicationRegistry.SaleOrderService.useSaveSaleOrder();

  const onChange = (_: Order | null, orderEntity: OrderEntity | null) => {
    if (orderEntity) {
      const currentItems = orderEntity.orderItems
        .filter((item) => item.id)
        .map((item) => item.id);
      setExcludedItems(currentItems);
    }
  };

  const onSave = async (payload: Order) => {
    const response = await applyOrderSave(payload);

    return response;
  };

  return (
    <CreateOrderModule
      getPath={ApplicationRegistry.PathService.saleOrders.viewSaleOrder}
      receiverType='client'
      useFetchCatalog={useCallback(useFetchCatalog(excludedItems), [JSON.stringify(excludedItems)])}
      useGetInventoryRelations={useGetInventoryRelations}
      useFetchReceiver={useFetchClients}
      title={TextService.getText().saleOrders.new}
      useGetConditions={useGetConditions}
      onChange={onChange}
      onSave={onSave}
    />
  );
}
