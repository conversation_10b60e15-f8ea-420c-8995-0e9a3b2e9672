import { DropDownSearchAppComponentOption } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { Notification } from '#appComponent/common/Notification.Component';
import { UpdateOrderModule } from '#application/common/modules/UpdateOrder.Module';
import { statusParam } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusValueObject } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface UpdateSaleOrderModuleProps {
  saleOrderId: string;
}

const textService = TextService.getText();

function useFetchClients(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const {
    items: clients = [], loading,
  } = ApplicationRegistry.ClientService.useGetClients({
    limit: 20,
    offset: (params.page - 1) * 20,
    searchTerm: params.searchText,
    orderBy: 'name',
    order: 'asc',
  });

  const options = clients.map((client) => ({
    id: client.id,
    name: client.name,
  }));

  return {
    options,
    refetch: () => {},
    isLoading: loading,
  };
}

function useFetchUsers(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const limit = 20;
  const offset = (params.page - 1) * limit;
  const {
    items: users = [], loading, refetch,
  } = ApplicationRegistry.UsersService.useGetUsers({
    limit,
    offset,
    searchTerm: params.searchText,
    orderBy: 'email',
    order: 'asc',
  });

  const options = users.map((item) => ({
    id: String(item.id),
    name: item.email,
  }));

  return {
    options,
    refetch,
    isLoading: loading,
  };
}

function useGetAvailableStatuses(saleOrder: Order): (() => OrderStatusValueObject[]) {
  return () => {
    const { availableStatuses, error } = ApplicationRegistry.SaleOrderService.useGetAvailableStatuses(saleOrder);

    if (error) {
      Notification({ message: textService.orders.loadError, type: MSG_ERROR_TYPES.ERROR });
      return [];
    }

    return availableStatuses.map((status) => new OrderStatusValueObject(statusParam[status]));
  };
}

function mapToSavePayload(orderEntity: OrderEntity, orderState: Order) {
  const isFinished = orderEntity.isFinished();
  const shippingAddress = orderState.orderInfo.shippingAddress === '' || isFinished ? undefined : orderState?.orderInfo.shippingAddress;
  const notes = orderState?.orderInfo.notes === '' ? undefined : orderState?.orderInfo.notes;
  const deliveryDate = orderState.orderInfo.deliveryDate === '' ? null : orderState.orderInfo.deliveryDate;
  const status = orderState.orderInfo.status.id !== orderEntity.info.status.value.id ? orderEntity.info.status.value.id : undefined;
  const assignedUserId = orderEntity?.info.assignedUser?.id || undefined;

  return {
    id: orderEntity.id,
    notes,
    deliveryDate,
    shippingAddress,
    status,
    assignedUserId,
  };
}

export function UpdateSaleOrderModule(props: UpdateSaleOrderModuleProps) {
  const { saleOrderId } = props;

  return (
    <UpdateOrderModule
      entityId={saleOrderId}
      getOrder={ApplicationRegistry.SaleOrderService.useGetSaleOrder}
      useGetAvailableStatuses={useGetAvailableStatuses}
      useFetchReceiver={useFetchClients}
      mapToSavePayload={mapToSavePayload}
      title={TextService.getText().saleOrders.titleSingle}
      useUpdateOrder={ApplicationRegistry.SaleOrderService.useUpdateSaleOrder}
      useFetchUsers={useFetchUsers}
    />
  );
}
