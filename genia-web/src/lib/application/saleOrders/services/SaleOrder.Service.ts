import { OrderConditions, OrderConditionsParams, OrderConditionsParamsItem } from '#application/common/orders/Order.Type';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';
import { Order } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface SaleOrderServiceUpdateParams {
  id: string;
  notes?: string;
  deliveryDate?: string | null;
  shippingAddress?: string | null;
  assignedUserId?: string;
  status?: OrderStatusIds;
}

export interface SaleOrderConditionsParamsItem extends OrderConditionsParamsItem {
  catalogId?: string;
}

export interface SaleOrderConditionsParams extends OrderConditionsParams{
  shippingPrice?: number;
  orderItems?:SaleOrderConditionsParamsItem[];
  clientId?: string;
}

export interface SaleOrderService {
  useGetSaleOrders: (props: GetTableDataProps) => GetTableDataResponse<Order>;
  useGetSaleOrder: (id: string) => {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  };
  useGetAvailableStatuses: (saleOrder: Order) => {
    availableStatuses: OrderStatusIds[];
    error: unknown;
  };
  useUpdateSaleOrder: () => {
    applyOrderUpdate: (order: SaleOrderServiceUpdateParams) => Promise<Order>;
  };
  useSaveSaleOrder: () => {
    applyOrderSave: (order: Order) => Promise<Order>;
  }
  useGetConditions: (saleOrder: SaleOrderConditionsParams) => {
    conditions: OrderConditions | null;
    error: unknown;
    loading: boolean;
  };
}
