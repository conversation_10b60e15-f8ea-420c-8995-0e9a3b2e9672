import { Avatar } from '@pitsdepot/storybook';

import { getAvatarStyles } from '#infrastructure/implementation/application/utils/avatarColorsCombination';

interface CustomResultsValueProps {
  name: string;
  media?: { url: string }[];
  catalog_media?: { url: string }[];
}

export const CustomResults = (value: unknown) => {
  const { name, media, catalog_media: catalogMedia } = value as CustomResultsValueProps;

  const avatarMedia = catalogMedia?.[0]?.url || media?.[0]?.url;

  const avatarBgColor = `bg-${getAvatarStyles(name).backgroundColor}-500`;
  const avatarTextColor = getAvatarStyles(name).textColor;

  return (
    <div className="flex items-center">
      <div className={`h-8 w-8 ${avatarBgColor} ${avatarTextColor} rounded-full flex items-center justify-center text-lg m-2`}>

        <Avatar
          src={avatarMedia}
          name={name}
          size={32}
          className={`${getAvatarStyles(name).textColor}`}
          initialsBackground={getAvatarStyles(name).backgroundColor}
        />
      </div>
      <div className='flex flex-col'>
        <div className='text-xsm'>{name}</div>
      </div>
    </div>
  );
};
