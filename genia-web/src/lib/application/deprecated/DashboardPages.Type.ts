import { TooltipProps } from '@pitsdepot/storybook';

import { Client } from '#application/client/Client.Type';

export type InventoryTypeProps = 'commodity' | 'product_input';
export type CatalogTypeProps = 'product' | 'service' | 'bundle';
export type DiscountTypes = 'percentage' | 'amount';

export interface Hierarchy {
  name: string;
  backgroundColor?: string;
  hierarchies?: Hierarchy[];
}

export interface CategoryAttributes {
  key: string;
  values?: string[];
  categories?: CategoryAttributes[];
}

export interface AttributesProps {
  attributeFormInputs: CategoryAttributes[];
  edit?: boolean;
  onChange?: (props: CategoryAttributes[]) => void;
  setError?: (value: boolean) => void;
  labelProps: {
    title: string;
    emptyHierarchyMessage?: string;
    tooltip?: TooltipProps;
  };
}

export type MediaType = 'image' | 'video';
export interface Media {
  id: string;
  mediaType: MediaType;
  url: string;
}

export type InventoryItem = {
  id: string;
  name: string;
  sku: string | null;
  description?: string | null;
  stock: number;
  restrictedStock?: number;
  attributes?: CategoryAttributes[] | AttributesProps[] | null;
  measurementUnit: string | null;
  disabledAt: boolean | null;
  hasStockValidation: boolean;
  standardIdentifier: string | null
  type: InventoryTypeProps;
  inventoryMedia: Media[];
};

export type CatalogItem = Omit<InventoryItem, 'type' | 'measurementUnit' | 'hasStockValidation' | 'standardIdentifier' | 'inventoryMedia' | 'disabledAt' | 'sku'> & {
  type: CatalogTypeProps;
  price: number;
  requiresStock: boolean;
  disabledAt: boolean | null;
  readId: string;
  catalog_media: Media[];
};

export type SearchFilter = {
  [key: string]: string[];
};
export interface GetTableDataProps<T extends SearchFilter = SearchFilter> {
  limit: number;
  offset: number;
  orderBy: string;
  order: string;
  searchTerm?: string;
  filters?: T;
  id?: string;
  providerId?: string;
}

export interface GetTableDataResponse<T> {
  totalNumberOfItems: number;
  items: T[];
  loading: boolean;
  error: Error | string | null;
  refetch: () => void;
}

export interface AddProviderInventory {
  id?: string;
  name?: string;
  currentPurchasePrice?: number;
  currentDiscount?: number;
}

interface ProviderInvenriesProp {
  currentDiscount: number;
  currentPurchasePrice: number;
}

export interface ProviderByInventoryId {
  id: string;
  name: string;
  providerInventories: ProviderInvenriesProp[],
}

export interface ProviderByInventoryItem {
  id: string | undefined;
  name: string | undefined;
  providerInventories: {
    currentPurchasePrice: number | undefined;
    currentDiscount: number | undefined;
  }[]
}

export type TypeProps = 'category' | 'value';

export interface NewFormInventoryProps {
  attributes?: CategoryAttributes[];
  description: string;
  hasStockValidation: boolean;
  measurementUnit: string;
  name: string;
  sku: string | null;
  standardIdentifier: string;
  stock: number;
  restrictedStock: number;
  type: string;
  isSkuAutoGenerated?: boolean
}

export interface NewFormCatalogProps {
  attributes: CategoryAttributes[] | null;
  description: string | null;
  hasStockValidation?: boolean;
  name: string;
  price: number;
  type?: string;
  active?: boolean;
  standardIdentifier?: string;
  requiresStock?: boolean;
  disabledAt?: Date | null;
  isIdAutoGenerated?: boolean;
  isTaxEnable?: boolean;
}

export interface CatalogDiscountClients {
  clientId: string;
  client: Client;
}

export interface CatalogDiscountCatalogs {
  catalog: CatalogItem;
}

export interface CatalogDiscountItem {
  id: string;
  name: string;
  discountType?: 'amount' | 'percentage';
  discountValue: number;
  requiredQuantity: number;
  startDate?: string;
  endDate?: string | null;
  disabledAt?: string | null;
  companyId?: string;
  readId?: string;
  active?: boolean;
  catalog_discount_clients?: CatalogDiscountClients[];
  catalogs?: CatalogDiscountCatalogs[];
}

export type StoreDiscountItem = Omit<CatalogDiscountItem, 'requiredQuantity' | 'catalog_discount_clients' | 'catalogs'> & {
  requiredAmount: number
  storeDiscountClients?: CatalogDiscountClients[]
};

export interface Taxes {
  id: string;
  name: string;
  type: string;
  value: number;
  amount: number;
}

export interface ProviderCatalog {
  id: string;
  name: string;
  readId: string;
  price: number;
  media?: { url: string}[];
  catalog_media?: { url: string}[];
}

export interface DropdownSearchProps<T> {
  itemsPerPage: number;
  getItems: (params: {
    limit: number;
    offset: number;
    orderBy: string;
    order: string;
    searchTerm: string;
  }) => {
    items: T[];
    totalNumberOfItems: number;
  };
  selectedItemIds?: string[];
  getId: (item: T) => string;
  minSearchLength?: number;
}

export interface DropDownSearchResponse<T> {
  searchInput: string;
  setSearchInput: React.Dispatch<React.SetStateAction<string>>;
  searchValue: string;
  setSearchValue: React.Dispatch<React.SetStateAction<string>>;
  isLoading: boolean;
  filteredOptions: T[];
  totalNumberOfItems: number | undefined;
  loadMoreOptions: () => void;
  handleInputChange: (value: string) => void;
  handleDropdownToggle: (isOpen: boolean) => void;
  handleKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  resetSearch: () => void;
}
