import {
  FormLabel,
  FormLabelProps,
  OptionsDropdownProps, SearchBox,
} from '@pitsdepot/storybook';
import {
  useCallback, useMemo, useState,
} from 'react';

import noImage from '#/assets/no-image.png';
import { CustomDropdownOptionRenderer } from '#appComponent/common/CustomDropdownOptionRenderer';
import { useDropdownSearch } from '#appComponent/common/hooks/useDropdownSearch.Hook';
import { InventoryCatalogs } from '#application/catalog/Catalog.Type';
import { ClassicDropdownInputSearch } from '#application/deprecated/ClassicDropdownInputSearch.Component';
import { InventoryItem } from '#application/inventory/Inventory.Type';
import ApplicationRegistry from '#composition/Application.Registry';

import { ControlledSelectedItem, RelatedItem } from '../../appComponent/common/ControlledSelectedItem';

const itemsPerPage = 10;

interface InventoryRelationProps {
  disabled?: boolean;
  label: FormLabelProps;
  relatedInventory?: Pick<InventoryCatalogs, 'inventoryId' | 'quantity' >[];
  onAddRelatedInventory?: (item: Pick<InventoryCatalogs, 'inventoryId' | 'quantity'>) => void;
  onRemoveRelatedInventory?: (inventoryId: string) => void;
  onChangeRelatedInventoryQuantity?: (inventoryId: string, quantity: number) => void;
}

function InventoryRelation(props: InventoryRelationProps) {
  const {
    label,
    disabled,
    relatedInventory = [],
    onAddRelatedInventory,
    onRemoveRelatedInventory,
    onChangeRelatedInventoryQuantity,
  } = props;
  const [imageErrorMap, setImageErrorMap] = useState<Record<string, boolean>>({});

  const relatedIds = relatedInventory?.map((item) => item.inventoryId).filter(Boolean);

  const { items: relatedInventoryItems } = ApplicationRegistry.InventoryService.useGetInventory({
    limit: relatedIds?.length || 0,
    offset: 0,
    orderBy: 'name',
    order: 'asc',
    filters: relatedIds && relatedIds.length > 0 ? { id: relatedIds } : undefined,
  });

  const {
    filteredOptions,
    handleInputChange,
    handleKeyDown,
    handleDropdownToggle,
    loadMoreOptions,
  } = useDropdownSearch<InventoryItem>({
    itemsPerPage,
    getItems: (params) => ApplicationRegistry.InventoryService.useGetInventory(
      disabled ? { ...params, limit: 0 } : params,
    ),
    selectedItemIds: relatedIds || [],
    getId: (item) => item.id,
  });

  const selectedItems: RelatedItem[] = useMemo(() => {
    if (!relatedInventoryItems || relatedInventoryItems.length === 0) return [];
    return relatedInventoryItems.map((item) => {
      const related = relatedInventory?.find((r) => r.inventoryId === item.id);
      return {
        id: item.id,
        name: item.name,
        sku: item.sku,
        quantity: related?.quantity || 1,
        url: item.inventoryMedia?.[0]?.url,
        onRemoveItem: () => {
          onRemoveRelatedInventory?.(item.id);
        },
      };
    });
  }, [relatedInventoryItems, relatedInventory, onRemoveRelatedInventory]);

  const handleChangeValue = useCallback((element: OptionsDropdownProps) => {
    if (relatedInventory.some((r) => r.inventoryId === element.id)) return;
    onAddRelatedInventory?.({ inventoryId: element.id, quantity: 1 });
  }, [relatedInventory, onAddRelatedInventory]);

  const handleImageError = useCallback((id: string) => {
    setImageErrorMap((prev) => ({ ...prev, [id]: true }));
  }, []);

  const optionsWithImage = useMemo(
    () => filteredOptions?.map((item) => {
      const { id } = item;
      const imageUrl = imageErrorMap[id]
        ? noImage
        : item.inventoryMedia?.[0]?.url || noImage;
      return {
        ...item,
        src: imageUrl,
        onImageError: () => handleImageError(id),
      };
    }),
    [filteredOptions, imageErrorMap, handleImageError],
  );

  const customSearchBox = (
    <SearchBox
      placeholder="Buscar productos"
      pressEnterLabel="Enter"
      onValueChange={handleInputChange}
      onKeyDown={handleKeyDown}
    />
  );

  const handleQuantityChange = (id: string, newQuantity: number) => {
    onChangeRelatedInventoryQuantity?.(id, newQuantity);
  };

  return (
    <div className='w-full max-w-[300px] mt-4'>
      <FormLabel {...label} />
      <ClassicDropdownInputSearch
        wrapperClassName="px-2 h-[38px] rounded-xl relative bg-white z-50 border w-full"
        contentClassName="flex justify-between items-center py-[9px] text-xsm"
        containerClassName=" "
        placeholder="Seleccionar"
        allOptions={optionsWithImage}
        onChangeValue={handleChangeValue}
        customInputSearch={customSearchBox}
        disabled={disabled}
        customRenderer={CustomDropdownOptionRenderer}
        onDropdownToggle={handleDropdownToggle}
        onLoadMore={loadMoreOptions}
        position="top"
      />
      <div className='mt-2 flex flex-col gap-2'>
        {selectedItems?.map((item) => (
          <ControlledSelectedItem
            key={item.id}
            item={item}
            disabled={disabled}
            onQuantityChange={handleQuantityChange}
          />
        ))}
      </div>
    </div>
  );
}

export default InventoryRelation;
