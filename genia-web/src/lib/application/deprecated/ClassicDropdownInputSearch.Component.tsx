import {
  Avatar,
  DropdownWithSearch,
  IconImporter,
  OptionsDropdownProps,
} from '@pitsdepot/storybook';

import { getAvatarStyles } from '#infrastructure/implementation/application/utils/avatarColorsCombination';

import { CustomResults } from './CustomResults.Component';

interface ExtendedOptionsDropdownProps extends OptionsDropdownProps {
  media?: { url: string }[];
  catalog_media?: { url: string }[];
}

interface ClassicDropdownInputSearchProps {
  children?: React.ReactNode;
  allOptions: ExtendedOptionsDropdownProps[];
  option?: ExtendedOptionsDropdownProps[];
  onChangeValue: (selectedOption: ExtendedOptionsDropdownProps) => void;
  selected?: string;
  containerClassName?: string;
  contentClassName?: string;
  wrapperClassName?: string;
  placeholder?: string;
  size?: number;
  disabled?: boolean;
  label?: string;
  onSearch?: (searchTerm: string) => void;
  onLoadMore?: () => void;
  customInputSearch?: React.ReactNode;
  customRenderer?: (item: OptionsDropdownProps) => React.ReactNode;
  onDropdownToggle?: (isOpen: boolean) => void;
  position?: 'top' | 'bottom';
}

export function ClassicDropdownInputSearch(props: ClassicDropdownInputSearchProps) {
  const {
    children,
    allOptions,
    option,
    onChangeValue,
    selected,
    containerClassName,
    contentClassName,
    wrapperClassName,
    placeholder = 'Select an option',
    size = 36,
    disabled = false,
    label,
    onSearch,
    onLoadMore,
    customInputSearch,
    customRenderer,
    onDropdownToggle,
    position,
  } = props;

  const customOptions = allOptions?.map((item) => ({
    ...item,
    renderer: customRenderer || CustomResults,
  }));

  const loadMoreOptions = () => {
    if (onLoadMore) {
      onLoadMore();
    }
  };

  const avatarMedia = (option?.[0] as ExtendedOptionsDropdownProps)?.media?.[0]?.url || (option?.[0] as ExtendedOptionsDropdownProps)?.catalog_media?.[0]?.url;

  return (
    <div className={containerClassName || 'h-[99px] mt-4 mb-4 relative z-10'}>
      <DropdownWithSearch
        options={customOptions as ExtendedOptionsDropdownProps[]}
        setSelectedOption={(optItem) => !disabled && onChangeValue(optItem)}
        itemsSelected={option as ExtendedOptionsDropdownProps[]}
        disabled={disabled}
        onSearchChange={onSearch}
        customSearchbox={customInputSearch}
        showAvatar={false}
        onToggle={onDropdownToggle}
        loadMoreOptions={loadMoreOptions}
        position={position}
      >
        <p className='text-xs my-2'>{label}</p>
        {children || <div className={`
          ${wrapperClassName || 'px-[24px] py-[16px] rounded-xl min-w-[323px] relative bg-white z-50 border'}
          ${disabled ? 'pd-bg-gray-100 cursor-not-allowed pd-border-neutral-300' : ''}
        `}>
          <div className={contentClassName || 'flex justify-between border-b px-[12px] py-[16px]'}>
            <div className='flex flex-row items-center gap-2 truncate'>
              {selected && <Avatar
                src={avatarMedia || ''}
                name={selected}
                size={size}
                className={`${getAvatarStyles(selected).textColor} ${disabled ? 'pd-opacity-60' : ''} min-w-[${size}px] min-h-[${size}px] w-[${size}px] h-[${size}px]`}
                initialsBackground={getAvatarStyles(selected).backgroundColor}
              />}

              <span
                className={selected ? `${disabled ? 'pd-text-neutral-500' : 'text-black'} truncate` : 'text-gray-400'}
              >
                {selected || placeholder}
              </span>
            </div>

            <IconImporter
              size={18}
              name='caretDown'
              className={`pd-transition-all pd-ease-in-out ${disabled ? 'pd-text-neutral-400 pd-opacity-60' : 'pd-text-dark-400 hover:pd-text-dark-700'}`}
            />
          </div>
        </div>}
      </DropdownWithSearch>
    </div>
  );
}
