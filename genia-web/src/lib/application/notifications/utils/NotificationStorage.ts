import { Notification } from '#domain/types/Notification.Type';

const LAST_SEEN_NOTIFICATION_KEY = 'lastSeenNotificationId';

export class NotificationStorage {
  static saveLastSeenNotificationId(notifications: Notification[]): void {
    if (notifications.length === 0) return;

    const mostRecentNotification = notifications[0];
    const mostRecentId = mostRecentNotification.id;

    localStorage.setItem(LAST_SEEN_NOTIFICATION_KEY, mostRecentId);
  }

  static getLastSeenNotificationId(): string | null {
    return localStorage.getItem(LAST_SEEN_NOTIFICATION_KEY);
  }

  static getNewNotificationsCount(notifications: Notification[]): number {
    if (notifications.length === 0) return 0;

    const lastSeenId = this.getLastSeenNotificationId();
    if (!lastSeenId) {
      return notifications.length;
    }

    const position = notifications.findIndex((notification) => notification.id === lastSeenId);

    if (position === -1) {
      return notifications.length;
    }

    if (position === 0) {
      return 0;
    }

    return position;
  }

  static clearLastSeenNotificationId(): void {
    localStorage.removeItem(LAST_SEEN_NOTIFICATION_KEY);
  }
}
