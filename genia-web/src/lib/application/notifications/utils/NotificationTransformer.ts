import { Notification } from '#domain/types/Notification.Type';

export interface OrderNotificationPayload {
  orderId: string;
  orderNumber: string;
  customerName?: string;
  amount?: number;
}

export interface InventoryNotificationPayload {
  productId: string;
  productName: string;
  currentStock: number;
  minimumStock?: number;
}

export interface ClientNotificationPayload {
  clientId: string;
  clientName: string;
  action: 'registered' | 'updated' | 'deleted';
}

export interface DiscountNotificationPayload {
  discountId: string;
  discountCode: string;
  action: 'activated' | 'deactivated' | 'expired';
}

export const transformNotificationMessage = (type: string, company: Record<string, unknown>): string => {
  switch (type) {
    case 'provider_invitation': {
      return `Has recibido una invitacion para unirte como proveedor de ${company?.name || 'la empresa'}`;
    }

    default:
      return `Notificacion de tipo: ${type}`;
  }
};

export interface RawNotification {
  id: string;
  ownerUserId: string;
  company_id: string;
  company: {
    name: string;
  };
  payload: Record<string, unknown>;
  requiredRoles?: string[];
  type: string;
  createdAt: string | Date;
}

export const transformNotification = (rawNotification: RawNotification): Notification => {
  const message = transformNotificationMessage(rawNotification.type, rawNotification.company);

  return {
    ...rawNotification,
    requiredRoles: rawNotification.requiredRoles || [],
    createdAt: new Date(rawNotification.createdAt),
    message,
    isRead: false,
  };
};

export const NotificationTransformer = {
  transform: transformNotification,
};
