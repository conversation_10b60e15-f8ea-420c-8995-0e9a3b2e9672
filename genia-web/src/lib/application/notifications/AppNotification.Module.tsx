import { Avatar, NotificationComponent, Title } from '@pitsdepot/storybook';
import { useCallback, useRef, useState } from 'react';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { useNotificationBadge } from '#application/notifications/hooks/useNotificationBadge.Hook';
import { useNotifications } from '#application/notifications/hooks/useNotifications';
import { useProviderInvitationActions } from '#application/notifications/hooks/useProviderInvitationActions.Hook';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';
import { Notification as NotificationType } from '#domain/types/Notification.Type';
import { ProviderInvitationPayload } from '#domain/types/ProviderInvitation.Type';

export interface NotificationMenuProps {
  notifications: NotificationType[];
  isOpen: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  isLoadingMore: boolean;
}

const NotificationItem = ({ notification }: { notification: NotificationType }) => {
  const { acceptInvitation, rejectInvitation } = useProviderInvitationActions();
  const [isAccepting, setIsAccepting] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);
  const text = TextService.getText();

  const formatMessage = (message: string, companyName: string) => {
    const parts = message.split(companyName);
    if (parts.length === 2) {
      return (
        <>
          {parts[0]}
          <span className="font-bold">{companyName}</span>
          {parts[1]}
        </>
      );
    }
    return message;
  };

  const getRelativeTime = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} min`;
    }
    if (diffInHours < 24) {
      return `${diffInHours}h`;
    }
    return `${diffInDays}d`;
  };

  const handleAcceptInvitation = async () => {
    setIsAccepting(true);

    try {
      await acceptInvitation(
        notification.id,
        notification.payload as unknown as ProviderInvitationPayload,
      );
      setIsProcessed(true);
      Notification({
        message: 'Invitación aceptada exitosamente',
        type: MSG_ERROR_TYPES.SUCCESS,
      });
    } catch (error) {
      Notification({
        message: 'Error al aceptar la invitación',
        type: MSG_ERROR_TYPES.ERROR,
      });
    } finally {
      setIsAccepting(false);
    }
  };

  const handleRejectInvitation = async () => {
    setIsRejecting(true);

    try {
      await rejectInvitation(
        notification.id,
        notification.payload as unknown as ProviderInvitationPayload,
      );
      setIsProcessed(true);
      Notification({
        message: 'Invitación rechazada exitosamente',
        type: MSG_ERROR_TYPES.SUCCESS,
      });
    } catch (error) {
      Notification({
        message: 'Error al rechazar la invitación',
        type: MSG_ERROR_TYPES.ERROR,
      });
    } finally {
      setIsRejecting(false);
    }
  };

  return (
    <div className="py-2 px-3 hover:bg-fadedGray hover:rounded text-gray-500 border-b">
      <div className="flex items-start gap-3">
        <Avatar
          size={36}
          name={notification.company.name}
        />
        <div className="flex-1">
          <div className="flex justify-between items-start mb-1">
            <div className="!text-dark-600 text-sm text-justify flex-1">
              {formatMessage(notification.message, notification.company.name)}
            </div>
            <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
              {getRelativeTime(notification.createdAt)}
            </span>
          </div>

          {notification.type === 'provider_invitation' && !isProcessed && (
            <div className="mt-2 flex gap-2">
              <button
                onClick={handleAcceptInvitation}
                disabled={isAccepting || isRejecting}
                className={`px-3 py-1 text-xs text-gray-700 rounded transition-colors ${
                  isAccepting || isRejecting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : ' bg-fadedGreen hover:bg-green-600/50'
                }`}
              >
                {isAccepting ? text.notifications.accepting : text.notifications.accept}
              </button>
              <button
                onClick={handleRejectInvitation}
                disabled={isAccepting || isRejecting}
                className={`px-3 py-1 text-xs text-gray-700 rounded transition-colors ${
                  isAccepting || isRejecting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-fadedRed hover:bg-red-600/50'
                }`}
              >
                {isRejecting ? text.notifications.rejecting : text.notifications.reject}
              </button>
            </div>
          )}

          {notification.type === 'provider_invitation' && isProcessed && (
            <div className="mt-2 text-xs text-gray-500">
              {text.notifications.invitacionProcesada}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const DefaultNotificationMenu = ({
  notifications,
  isOpen,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: NotificationMenuProps) => {
  const text = TextService.getText();
  const observer = useRef<IntersectionObserver>();

  const lastNotificationElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          onLoadMore();
        }
      });
      if (node) observer.current.observe(node);
    },
    [hasMore, onLoadMore, isLoadingMore],
  );

  return (
    <div
      className={`
          absolute top-full mt-2 left-0 bg-white rounded-md shadow-lg p-4 w-80
          flex flex-col gap-1 overflow-hidden
          transition-all duration-300 ease-in-out
          ${isOpen ? 'opacity-100 max-h-60' : 'opacity-0 max-h-0'}
        `}
    >
      <Title as="h4" size="xsm" weight="regular">
        {text.notifications.title}
      </Title>
      {notifications?.length ? (
        <div className="overflow-y-auto">
          {notifications?.map((notification, index) => {
            if (notifications.length === index + 1) {
              return (
                <div ref={lastNotificationElementRef} key={notification.id}>
                  <NotificationItem notification={notification} />
                </div>
              );
            }
            return <NotificationItem key={notification.id} notification={notification} />;
          })}
          {isLoadingMore && (
            <div className="text-center py-2 text-gray-500">
              <span className="text-sm">Cargando más notificaciones...</span>
            </div>
          )}
        </div>
      ) : (
        <Title as="h5" size="xxsm" weight="light" data-testid="noNotifications">
          {text.notifications.noNotifications}
        </Title>
      )}
    </div>
  );
};

export function AppNotificationModule() {
  const {
    notifications,
    newNotificationsCount,
    loadMoreNotifications,
    hasMore,
    isLoadingMore,
  } = useNotifications();
  const { displayBadgeCount, handleBellClick } = useNotificationBadge(notifications, newNotificationsCount);

  return (
    <article>
      <div onClick={handleBellClick} className="text-white">
        <NotificationComponent
          badgeCount={displayBadgeCount}
          notifications={notifications}
        >
          <DefaultNotificationMenu
            notifications={notifications}
            isOpen={true}
            hasMore={hasMore}
            onLoadMore={loadMoreNotifications}
            isLoadingMore={isLoadingMore}
          />
        </NotificationComponent>
      </div>
    </article>
  );
}
