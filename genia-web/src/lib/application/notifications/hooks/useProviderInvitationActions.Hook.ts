import { useContext } from 'react';

import { ProviderInvitationPayload } from '#domain/types/ProviderInvitation.Type';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { ProviderInvitationsHttp } from '#infrastructure/api/http/ProviderInvitations.Http';

export const useProviderInvitationActions = () => {
  const { getToken } = useContext(AuthContext);

  const acceptInvitation = async (notificationId: string, payload: ProviderInvitationPayload) => {
    const token = await getToken();
    const invitationId = payload.invitationId || notificationId;

    await ProviderInvitationsHttp.acceptInvitation(token, invitationId);
  };

  const rejectInvitation = async (notificationId: string, payload: ProviderInvitationPayload) => {
    const token = await getToken();
    const invitationId = payload.invitationId || notificationId;

    await ProviderInvitationsHttp.rejectInvitation(token, invitationId);
  };

  return {
    acceptInvitation,
    rejectInvitation,
  };
};
