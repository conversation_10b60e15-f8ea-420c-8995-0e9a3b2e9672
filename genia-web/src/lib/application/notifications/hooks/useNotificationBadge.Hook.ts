import { useCallback, useState } from 'react';

import { NotificationStorage } from '#application/notifications/utils/NotificationStorage';
import { Notification } from '#domain/types/Notification.Type';

export const useNotificationBadge = (notifications: Notification[], newNotificationsCount: number) => {
  const [hasBeenClicked, setHasBeenClicked] = useState(false);
  const [lastNotificationCount, setLastNotificationCount] = useState(0);

  const handleBellClick = useCallback(() => {
    if (notifications.length > 0) {
      NotificationStorage.saveLastSeenNotificationId(notifications);
      setHasBeenClicked(true);
    }
  }, [notifications]);

  if (notifications.length > lastNotificationCount) {
    setLastNotificationCount(notifications.length);
    if (hasBeenClicked) {
      setHasBeenClicked(false);
    }
  }

  const displayBadgeCount = hasBeenClicked ? 0 : newNotificationsCount;

  return {
    displayBadgeCount,
    handleBellClick,
  };
};
