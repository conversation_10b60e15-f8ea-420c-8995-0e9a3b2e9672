import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { DropDownSearchAppComponentOption, DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { ProductDropDownProduct } from '#appComponent/common/orders/productDropDown/ProductDropDown.Context';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import { ConditionParams, CreateOrderModule } from '#application/common/modules/CreateOrder.Module';
import { OrderConditions } from '#application/common/orders/Order.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';

function useFetchCatalog(excludedItems: string[], providerCompanyId: string): DropDownSearchFetch {
  return (params: {searchText: string, page: number}) => {
    const [options, setOptions] = useState<ProductDropDownProduct[]>([]);

    const mapCatalog = (catalog: CatalogItem[]) => catalog.map((item) => ({
      id: item.id,
      name: item.name,
      productId: item.readId || '',
      image: item.catalog_media?.[0]?.url || '',
    })).filter((excludedItem) => !excludedItems.includes(excludedItem.id));

    const {
      items: catalog = [],
      loading,
    } = ApplicationRegistry.ProviderService.useGetProvidersCatalog({
      limit: 20,
      offset: (params.page - 1) * 20,
      searchTerm: params.searchText,
      filters: {
        // fake uuid we need to change the provider catalog or to create a new one;
        company: [providerCompanyId || 'bf8a2d7a-4faf-4105-8f00-ce39f9eeaff1'],
      },
      orderBy: 'name',
      order: 'asc',
    });

    useEffect(() => {
      setOptions(mapCatalog(catalog));
    }, [JSON.stringify(catalog)]);

    const customRefetch = () => {
      setOptions(mapCatalog(catalog));
    };

    return {
      options,
      refetch: customRefetch,
      isLoading: loading,
    };
  };
}

function useFetchProviders(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const {
    items: providers = [], loading,
  } = ApplicationRegistry.ProviderService.useGetProviders({
    limit: 20,
    offset: (params.page - 1) * 20,
    searchTerm: params.searchText,
    orderBy: 'name',
    order: 'asc',
  });

  const options = providers.map((provider) => ({
    id: provider.id,
    name: provider.name,
    companyId: provider.providerCompanyId,
  }));

  return {
    options,
    refetch: () => {},
    isLoading: loading,
  };
}

function useFetchInventory(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const {
    items: inventory = [], loading,
  } = ApplicationRegistry.InventoryService.useGetInventory({
    limit: 20,
    offset: (params.page - 1) * 20,
    searchTerm: params.searchText,
    orderBy: 'name',
    order: 'asc',
  });
  return {
    options: inventory.map((item) => ({
      id: item.id,
      name: item.name,
      sku: item.sku,
      image: item.inventoryMedia[0]?.url || '',
    })),
    refetch: () => {},
    isLoading: loading,
  };
}

function useGetConditions(receiverCompanyId: string | undefined | null) {
  return function pureGetConditions(params: ConditionParams): ({
  conditions: OrderConditions | null;
  error: unknown;
  loading: boolean;
  }) {
    const isReceiverWithCompanyId = !!receiverCompanyId;
    const mappedParams: ConditionParams = {
      ...params,
      orderItems: params.orderItems?.map((item) => ({
        referenceId: item.referenceId,
        quantity: item.quantity,
        unitPrice: isReceiverWithCompanyId ? undefined : item.unitPrice,
        unitPriceAfterDiscount: isReceiverWithCompanyId ? undefined : item.unitPriceAfterDiscount,
        taxIds: item.taxIds,
      })),
    };

    const { conditions, error, loading } = ApplicationRegistry.PurchaseOrderService.useGetConditions({
      ...mappedParams,
      providerId: params.receiverId || undefined,
    });

    return {
      conditions,
      error,
      loading,
    };
  };
}

function useGetInventoryRelations(providerId: string) {
  return function pureGetRelations(ids: string[]): {
    items: Record<string, OrderItemInventoryRelation[]>;
    error: unknown;
    loading: boolean;
  } {
    const { items, error, loading } = ApplicationRegistry.PurchaseOrderService
      .useGetInventorySuggestions(providerId, ids);

    return {
      items,
      error,
      loading,
    };
  };
}

function normalizeOrder(order: Order) {
  const newOrderEntity: OrderEntity = OrderEntity.fromJson(order);

  const newOrderInfo = newOrderEntity.info
    .setOrderNumber({
      ...newOrderEntity.info.orderNumber,
      value: '',
      disabled: true,
      hidden: true,
    })
    .setShippingPrice({
      ...newOrderEntity.info.shippingPrice,
      value: 0,
      disabled: true,
      hidden: true,
    })
    .setDeliveryDate({
      ...newOrderEntity.info.deliveryDate,
      disabled: true,
      hidden: true,
    })
    .setStatus({
      ...newOrderEntity.info.status,
      disabled: true,
      hidden: true,
    });

  const normalizedOrderItems = newOrderEntity.orderItems.map((item) => {
    if (item.id && item.id !== '') {
      return item.disableProductNumber(true);
    }
    return item;
  });

  return newOrderEntity.setInfo(newOrderInfo).setOrderItems(normalizedOrderItems);
}

export function CreatePurchaseOrderModule({ from }: {from: string | null}) {
  const [orderEntity, setOrderEntity] = useState<OrderEntity | null>(null);
  const [excludedItems, setExcludedItems] = useState<string[]>([]);

  const { applyOrderSave } = ApplicationRegistry.PurchaseOrderService.useSavePurchaseOrder();
  const { resetCart } = ApplicationRegistry.PurchaseOrderService.usePurchaseOrderFromExternalData();

  const {
    items: providers = [],
  } = ApplicationRegistry.ProviderService.useGetProviders({
    limit: 100,
    offset: 0,
    orderBy: 'name',
    order: 'asc',
  });

  const selectedProviderCompanyId = useMemo(() => {
    const provider = providers.find((p) => p.id === orderEntity?.receiver?.id);
    if (!provider) return '';
    return provider.providerCompanyId;
  }, [orderEntity?.receiver?.id, JSON.stringify(providers)]);

  const onChange = useCallback((_: Order | null, newEntity: OrderEntity | null) => {
    setOrderEntity(newEntity);

    if (orderEntity) {
      const currentItems = orderEntity.orderItems
        .filter((item) => item.id)
        .map((item) => item.id);
      setExcludedItems(currentItems);
    }
  }, [JSON.stringify(orderEntity?.orderItems)]);

  const onSave = async (payload: Order) => {
    const payloadWithAssignedUser = {
      ...payload,
      assignedUserId: orderEntity?.info.assignedUser?.id,
    };

    const response = await applyOrderSave(payloadWithAssignedUser);

    if (response && from) {
      resetCart();
    }

    return response;
  };

  return (
    <CreateOrderModule
      getPath={ApplicationRegistry.PathService.purchaseOrders.viewPurchaseOrder}
      receiverType='provider'
      useFetchCatalog={useCallback(useFetchCatalog(excludedItems, selectedProviderCompanyId as string), [JSON.stringify(excludedItems), selectedProviderCompanyId])}
      useGetInventoryRelations={useCallback(useGetInventoryRelations(orderEntity?.receiver?.id || ''), [orderEntity?.receiver?.id])}
      useFetchReceiver={useFetchProviders}
      title={TextService.getText().purchaseOrders.new}
      useGetConditions={useCallback(useGetConditions(selectedProviderCompanyId), [selectedProviderCompanyId])}
      onChange={onChange}
      useFetchInventory={useFetchInventory}
      initialData={from ? normalizeOrder(ApplicationRegistry.PurchaseOrderService.usePurchaseOrderFromExternalData().order) : undefined}
      onSave={onSave}
    />
  );
}
