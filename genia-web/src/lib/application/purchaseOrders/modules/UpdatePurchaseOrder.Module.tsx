import { DropDownSearchAppComponentOption } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { Notification } from '#appComponent/common/Notification.Component';
import { UpdateOrderModule } from '#application/common/modules/UpdateOrder.Module';
import { statusParam } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusValueObject } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface UpdatePurchaseOrderModuleProps {
  purchaseOrderId: string;
}

const textService = TextService.getText();

function useFetchUsers(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const { items: users = [], loading } = ApplicationRegistry.UsersService.useGetUsers({
    limit: 20,
    offset: (params.page - 1) * 20,
    orderBy: 'name',
    order: 'asc',
  });

  return {
    options: users.map((item) => ({
      id: String(item.id),
      name: item.email,
    })),
    refetch: () => {},
    isLoading: loading,
  };
}

function useFetchProviders(params: {searchText: string, page: number}): {options: DropDownSearchAppComponentOption[], refetch: () => void, isLoading: boolean} {
  const {
    items: providers = [], loading,
  } = ApplicationRegistry.ProviderService.useGetProviders({
    limit: 20,
    offset: (params.page - 1) * 20,
    searchTerm: params.searchText,
    orderBy: 'name',
    order: 'asc',
  });

  const options = providers.map((provider) => ({
    id: provider.id,
    name: provider.name,
  }));

  return {
    options,
    refetch: () => {},
    isLoading: loading,
  };
}

function useGetAvailableStatuses(purchaseOrder: Order): (() => OrderStatusValueObject[]) {
  return () => {
    const { availableStatuses, error } = ApplicationRegistry.PurchaseOrderService.useGetAvailableStatuses(purchaseOrder);

    if (error) {
      Notification({ message: textService.orders.loadError, type: MSG_ERROR_TYPES.ERROR });
      return [];
    }

    return availableStatuses.map((status) => new OrderStatusValueObject(statusParam[status]));
  };
}

function mapToSavePayload(orderEntity: OrderEntity, orderState: Order) {
  const hasCompanyProvider = orderEntity.receiver.companyId !== undefined;
  const isFinished = orderEntity.isFinished();

  const shippingAddress = orderState?.orderInfo.shippingAddress === '' || hasCompanyProvider || isFinished ? undefined : orderState?.orderInfo.shippingAddress;
  const notes = orderState?.orderInfo.notes === '' ? undefined : orderState?.orderInfo.notes;
  const deliveryDate = !orderState?.orderInfo.deliveryDate || hasCompanyProvider ? undefined : orderState?.orderInfo.deliveryDate;
  const status = orderState.orderInfo.status.id !== orderEntity.info.status.value.id ? orderEntity?.info.status.value.id : undefined;
  const assignedUserId = orderEntity?.info.assignedUser?.id || undefined;

  return {
    id: orderEntity.id,
    notes,
    deliveryDate,
    shippingAddress,
    status,
    assignedUserId,
  };
}

export function UpdatePurchaseOrderModule(props: UpdatePurchaseOrderModuleProps) {
  const { purchaseOrderId } = props;

  return (
    <UpdateOrderModule
      entityId={purchaseOrderId}
      getOrder={ApplicationRegistry.PurchaseOrderService.useGetPurchaseOrderWithCatalogMedia}
      useGetAvailableStatuses={useGetAvailableStatuses}
      useFetchReceiver={useFetchProviders}
      mapToSavePayload={mapToSavePayload}
      title={TextService.getText().purchaseOrders.titleSingle}
      useUpdateOrder={ApplicationRegistry.PurchaseOrderService.useUpdatePurchaseOrder}
      useFetchUsers={useFetchUsers}
    />
  );
}
