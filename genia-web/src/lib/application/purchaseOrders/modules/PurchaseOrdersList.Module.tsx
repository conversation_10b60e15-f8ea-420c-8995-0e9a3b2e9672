import {
  Button,
  Column,
  FilterGroup,
  Row,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { ChipCellComponent } from '#appComponent/common/ChipCell.component';
import { AnchorCell } from '#appComponent/table/AnchorCell.AppComponent';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { GenericCell, PriceCell } from '#appComponent/table/TableCells.Component';
import { statusParam } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { checkedFilters } from '#application/common/utils/CheckFilters.Util';
import { setUrlParams } from '#application/common/utils/SetUrlParams.Util';
import {
  ERROR_MESSAGES,
  FILTER_NAMES,
  LOADING_MESSAGES,
} from '#application/purchaseOrders/PurchaseOrderList.constants';
import ApplicationRegistry from '#composition/Application.Registry';
import { Order } from '#domain/aggregates/order/Order.Entity';

import { useGetProviders } from '../../../infrastructure/implementation/application/provider/ReadModelProvider.Service';

type FilterType = 'providerId' | 'status';

const renderDateChip = (value: string) => (
  <ChipCellComponent color={Theme.colors.dark[200]} text={value} />
);

const columns: Column[] = [
  {
    header: 'Id',
    dataKey: 'readId',
    width: '10%',
    renderer: AnchorCell,
  },
  {
    header: 'Estado',
    dataKey: 'state',
    width: '10%',
    renderer: (value: string) => <ChipCellComponent
      color={statusParam[value].color}
      text={statusParam[value].name} />,
  },
  {
    header: 'Proveedor',
    dataKey: 'providerName',
    width: '20%',
    renderer: AnchorCell,
  },
  {
    header: 'Usuario',
    dataKey: 'userEmail',
    width: '20%',
    renderer: GenericCell,
  },
  {
    header: 'Total',
    dataKey: 'totalAmount',
    width: '10%',
    renderer: PriceCell,
  },
  {
    header: 'Fecha de creación',
    dataKey: 'createdAt',
    width: '15%',
    renderer: renderDateChip,
  },
  {
    header: 'Fecha de actualización',
    dataKey: 'updatedAt',
    width: '15%',
    renderer: renderDateChip,
  },
];

function mapper(items: Order[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    readId: { value: item.orderInfo.orderNumber, url: ApplicationRegistry.PathService.purchaseOrders.viewPurchaseOrder(item.id) },
    state: item.orderInfo.status.id,
    providerName: {
      value: item.receiver?.name,
      url: item.receiver.id ? ApplicationRegistry.PathService.providers.viewProvider(item.receiver.id) : undefined,
    },
    userEmail: item.orderInfo.assignedUser?.email,
    totalAmount: item.orderInfo.summary.total,
    createdAt: item.createdAt ? new Date(item.createdAt).toLocaleString('es-MX', { hour12: true }) : 'No disponible',
    updatedAt: item.updatedAt ? new Date(item.updatedAt).toLocaleString('es-MX', { hour12: true }) : 'No disponible',
  }));
}

export function PurchaseOrderListModule() {
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);

  const [filtersFromUrl, setFiltersFromUrl] = useState({});
  const [filters, setFilters] = useState<FilterGroup<FilterType>[]>([]);

  const goToPath = ApplicationRegistry.PathService.useGoToPath();

  const { items: providers, loading: isProvidersLoading, error: providersError } = useGetProviders({
    limit: 10, offset: 0, orderBy: 'name', order: 'asc',
  });

  const baseFilters: FilterGroup<FilterType>[] = useMemo(() => {
    if (!providers || providers.length === 0) {
      return [];
    }

    return ([
      {
        key: 'providerId',
        name: FILTER_NAMES.PROVIDERS,
        options: providers?.map((provider) => ({ id: provider.id, label: provider.name })) || [],
      },
    ]);
  }, [providers]);

  useEffect(() => {
    if (location.search.length === 0) {
      setFiltersFromUrl({});
    }
  }, [JSON.stringify(location)]);

  useEffect(() => {
    const providerIdFromUrl = queryParams.getAll('providerId');

    const filtersUrl = {
      providerId: providerIdFromUrl,
    };

    setFiltersFromUrl(filtersUrl);
  }, []);

  useEffect(() => {
    setFilters(checkedFilters(baseFilters, filtersFromUrl));
  }, [baseFilters, filtersFromUrl]);

  const onRedirect = () => {
    goToPath(ApplicationRegistry.PathService.purchaseOrders.addPurchaseOrder());
  };

  if (providersError) {
    return <div>{ERROR_MESSAGES.PROVIDERS_ERROR}</div>;
  }

  if (isProvidersLoading) {
    return <div>{LOADING_MESSAGES.PROVIDERS_LOADING}</div>;
  }

  return (
    <PaginatedListAppComponent
      title='Ordenes de compra'
      columns={columns}
      useGetHook={ApplicationRegistry.PurchaseOrderService.useGetPurchaseOrders}
      mapper={mapper}
      filters={filters}
      searchTitle='Buscar por id'
      filtersFromUrl={filtersFromUrl}
      setUrlParams={setUrlParams}
      showHeader={false}
      headerButton={
        <Button onClick={onRedirect} className="self-end" size="small">
          Nueva orden de compra
        </Button>
      }
    />
  );
}
