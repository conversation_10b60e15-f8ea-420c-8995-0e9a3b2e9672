import { SearchFilter } from '#application/deprecated/DashboardPages.Type';

export interface PurchaseOrder {
  id: string;
  status: string;
  provider: {
    name: string;
    id: string;
  }
  user?: {
    email: string;
    id: string;
  }
  total: number;
  readId: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseOrderFilters extends SearchFilter {
  providerId: string[];
  status: string[];
}
