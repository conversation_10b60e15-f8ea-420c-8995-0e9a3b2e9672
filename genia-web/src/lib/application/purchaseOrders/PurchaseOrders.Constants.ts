export const PROVIDER_LABEL = 'Proveedor';

export const SHIPPING_ORDER = {
  LABELS: {
    ADDRESS: 'Dirección de envío',
    DATE: 'Fecha de envío',
    SHIPPING_PRICE: 'Precio de envío',
  },
  PLACEHOLDERS: {
    ADDRESS: 'Dirección de envío',
    DATE: 'Fecha de envío',
    SHIPPING_PRICE: '$0.00',
  },
  TOOLTIPS: {
    ADDRESS: 'Dirección a la que se enviará la orden',
    SHIPPING_PRICE: 'Costo de envío',
  },
};

export const purchaseGeneralOrdersInitialState = {
  providerId: '',
  deliveryDate: null,
  notes: '',
  shippingAddress: '',
  shippingPrice: 0,
};

export const purchaseOrderItem = {
  id: '',
};

export const PURCHASE_ORDER_MESSAGES = {
  NEW_PURCHASE_ORDER: 'Nueva Orden de Compra',
  ORDER_NUMBER_PREFIX: 'Orden de Compra',
  INVALID_DELIVERY_DATE: 'Fecha de entrega no válida',
  MISSING_PROVIDER: 'Debe seleccionar un proveedor',
  EMPTY_ROWS: 'Debe agregar al menos un item',
  MISSING_GENERAL_DATA: 'Datos generales incompletos',
  ORDER_CREATION_SUCCESFULL: 'Orden creada exitosamente',
  ORDER_CREATION_FAILED: 'Error al crear la orden',
};

export const PURCHASE_ORDER_PROPERTIES = {
  LABELS: {
    DELIVERY_DATE: 'Fecha de entrega',
    NOTES: 'Notas',
  },
  NAME: {
    DELIVERY_DATE: 'deliveryDate',
    NOTES: 'notes',
  },
  PLACEHOLDERS: {
    DELIVERY_DATE: 'Fecha de entrega',
    NOTES: 'Notas',
  },
  TOOLTIPS: {
    DELIVERY_DATE: 'Fecha en la que se entregará la orden',
    NOTES: 'Notas adicionales',
  },
};

export const PURCHASE_ORDER_TABLE_PROPERTIES = {
  HEADERS: {
    ITEM_NAME: 'Producto',
    SKU: 'Numero identificador',
    QTY: 'Cantidad',
    COST_UNT: 'Costo unitario',
    AFTER_DISCOUNT: 'Después de descuento',
    TAX: 'Impuestos',
    TOTAL: 'Total',
    EMPTY: '',
  },
  FIELDS: {
    NAME: 'name',
    READ_ID: 'readId',
    SKU: 'sku',
    QUANTITY: 'quantity',
    UNIT_COST: 'unitCost',
    AFTER_DISCOUNT: 'unitPriceAfterDiscount',
    TAX: 'tax',
    TOTAL: 'Total',
    TRASH: 'trash',
    PURCHASE_TAX: 'purchaseTax',
  },
};

export const PURCHASE_ORDER_LIST_PROPERTIES = {
  HEADERS: {
    ITEM_NAME: 'Producto',
    SKU: 'SKU',
    QTY: 'QTY',
    COST_UNT: 'COST/UNT',
    TAX: 'TAX',
    EMPTY: '',
    ID: 'Id',
    PROVIDER: 'Proveedor',
    TOTAL: 'Total',
    CREATED_AT: 'Fecha de creación',
    UPDATED_AT: 'Ultima actualización',
  },
  PROPERTIES: {
    READ_ID: 'readId',
    PROVIDER_NAME: 'providerName',
    TOTAL_AMOUNT: 'totalAmount',
    CREATED_AT: 'createdAt',
    UPDATED_AT: 'updatedAt',
    PROVIDER_ID: 'providerId',
    DELIVERY_DATE: 'deliveryDate',
  },
};

export const PURCHASE_ORDER_ITEM_LABELS = {
  LABELS: {
    DELIVERY_DATE: 'Fecha de entrega',
    NOTES: 'Notas',
    NAME: 'Producto',
    SKU: 'Sku',
    QUANTITY: 'QTY',
    COST: 'Costo',
    TAX: 'Tax (%)',
    TOTAL: 'Total',
  },
  PLACEHOLDERS: {
    DELIVERY_DATE: 'DD/MM/YYYY',
    NAME: 'Nombre de la pieza',
    SKU: 'RED-3000',
    PRICE: '$ 00.00',
    SHIP_ADDRESS: 'Agrega una direccion',
    SEARCH_BY_ID: 'Buscar por id',
  },
  FIELDS: {
    NAME: 'name',
    SKU: 'sku',
    QUANTITY: 'quantity',
    UNIT_COST: 'unitCost',
    TAX: 'tax',
    TOTAL: 'Total',
    TRASH: 'trash',
    PURCHASE_TAX: 'purchaseTax',
  },
  TOOLTIPS: {
    DELIVERY_DATE: 'Ingresa la fecha cuando quieras que se envie la orden',
  },
  FILTERS: {
    PROVIDERS: 'Proveedores',
  },
};

export const PURCHASE_ORDER_TABLE = {
  PURCHASE_ORDERS_TITLE: 'Ordenes de compra',
  SEARCH_BY_ID_PLACEHOLDER: 'Buscar por id',
  ADD_NEW_PURCHASE_ORDER: 'Nueva orden de compra',
  ADD_NEW_PIECE: 'Agregar nueva pieza',
  DELETE_BUTTON: 'Eliminar',
};

export const ADD_PROVIDER_LABEL = 'Agregar proveedor';
export const SEARCH_CATALOG = 'Buscar por catalogo';
