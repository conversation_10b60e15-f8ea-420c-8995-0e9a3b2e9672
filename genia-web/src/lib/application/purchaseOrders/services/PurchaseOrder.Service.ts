import { OrderConditions, OrderConditionsParams, OrderConditionsParamsItem } from '#application/common/orders/Order.Type';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';
import { CatalogMediaProps, GetCatalogMediaResult } from '#domain/aggregates/catalog/CatalogInventory.ValueObject';
import { Order } from '#domain/aggregates/order/Order.Entity';
import { OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface PurchaseOrderServiceUpdateParams {
  id: string;
  notes?: string;
  deliveryDate?: string | null;
  shippingAddress?: string | null;
  assignedUserId?: string;
  status?: OrderStatusIds;
}

export interface PurchaseOrderConditionsParamsItem extends OrderConditionsParamsItem {
  referenceId?: string;
}

export interface PurchaseOrderConditionsParams extends OrderConditionsParams{
  shippingPrice?: number;
  orderItems?:PurchaseOrderConditionsParamsItem[];
  providerId?: string;
}

export interface PurchaseOrderService {
  useGetPurchaseOrders: (props: GetTableDataProps) => GetTableDataResponse<Order>;
  useGetPurchaseOrder: (id: string) => {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  };
  useGetAvailableStatuses: (saleOrder: Order) => {
    availableStatuses: OrderStatusIds[];
    error: unknown;
  };
  useUpdatePurchaseOrder: () => {
    applyOrderUpdate: (order: PurchaseOrderServiceUpdateParams) => Promise<Order>;
  };
  useSavePurchaseOrder: () => {
    applyOrderSave: (order: Order) => Promise<Order>;
  }
  useGetConditions: (saleOrder: PurchaseOrderConditionsParams) => {
    conditions: OrderConditions | null;
    error: unknown;
    loading: boolean;
  };
  useGetInventorySuggestions: (providerId: string, currentItems: string[]) => {
    items: Record<string, OrderItemInventoryRelation[]>;
    error: unknown;
    loading: boolean;
  };
  usePurchaseOrderFromExternalData: () => {
    order: Order;
    resetCart: () => void;
  };
  useGetCatalogMedia: (props: CatalogMediaProps) => GetCatalogMediaResult;
  useGetPurchaseOrderWithCatalogMedia: (purchaseOrderId: string) => {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  };
}
