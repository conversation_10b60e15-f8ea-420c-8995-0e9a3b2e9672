import { AxiosResponse } from 'axios';

import { GetTableDataProps, GetTableDataResponse, NewFormInventoryProps } from '#application/deprecated/DashboardPages.Type';

import {
  CreateHistoryMovementProps, InventoryHistory,
  InventoryItem,
} from '../Inventory.Type';

export interface InventoryService {
  useGetInventory: (props: GetTableDataProps) => GetTableDataResponse<InventoryItem>;
  useGetInventoryItem: (id: string) => {
    inventoryItem: InventoryItem | null,
    loading: boolean,
    error: Error | null,
    refetchInventoryItem: () => void,
  };
  createInventory(token: string | void, payload: NewFormInventoryProps): Promise<AxiosResponse>;
  updateInventory(token: string | void, id: string, payload: NewFormInventoryProps): Promise<AxiosResponse>;
  useGetInventoryHistory: (props: GetTableDataProps) => GetTableDataResponse<InventoryHistory>;
  createInventoryHistoryMovement(props: CreateHistoryMovementProps): Promise<AxiosResponse>;
}
