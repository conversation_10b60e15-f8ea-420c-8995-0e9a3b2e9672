import { AxiosResponse } from 'axios';
import {
  useContext,
  useEffect,
  useState,
} from 'react';
import { useParams } from 'react-router-dom';

import CreateUpdateItem from '#appComponent/common/CreateUpdateItem';
import FieldText from '#appComponent/common/FieldText.Component';
import { useMedia } from '#appComponent/common/hooks/useMedia.Hook';
import InventoryForm from '#appComponent/common/InventoryForm.Component';
import { MediaToRemoveProvider, useMediaToRemove } from '#appComponent/common/media/MediaToRemove.Context';
import { Notification } from '#appComponent/common/Notification.Component';
import {
  INPUT_NAME,
  inventoryAdditionInitialState,
} from '#application/constants/texts/InventoryForm.Constants';
import { CategoryAttributes, InventoryItem, NewFormInventoryProps } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

interface CustomAxiosResponse extends AxiosResponse {
  statusCode?: number;
}

function CreateUpdateInventoryContent() {
  const { id } = useParams();
  const { inventoryId, refetchInventory, setRefetchInventory } = useContext(ApplicationRegistry.InventoryContext);
  const { getToken } = useContext(ApplicationRegistry.AuthContext);
  const goToPath = ApplicationRegistry.PathService.useGoToPath();
  const { imagesToRemove } = useMediaToRemove();
  const { handleMediaOperations } = useMedia();

  const [editMode, setEditMode] = useState(!inventoryId || false);
  const [defaultInputs, setDefaultInputs] = useState<Partial<InventoryItem>>(inventoryAdditionInitialState);
  const [attributeFormInputs, setAttributeFormInputs] = useState<CategoryAttributes[]>([]);
  const [isLoading, setIsloading] = useState(false);
  const [dataErrorName, setDataErrorName] = useState<string>('');
  const [newAttributesForm, setNewAttributesForm] = useState<CategoryAttributes[]>([]);
  // eslint-disable-next-line max-len
  const [newForm, setNewForm] = useState<NewFormInventoryProps & { isSkuAutoGenerated?: boolean, images?:(File | { id: string; url: string; name: string })[]} | null>(null);
  const [inputEmptyError, setInputEmptyError] = useState<boolean>(false);

  const { inventoryItem, loading, refetchInventoryItem } = ApplicationRegistry.InventoryService.useGetInventoryItem(inventoryId || '');

  const { createInventory, updateInventory } = ApplicationRegistry.InventoryService;

  useEffect(() => {
    if (refetchInventory) {
      refetchInventoryItem();
      setRefetchInventory?.(false);
    }
  }, [refetchInventory]);

  useEffect(() => {
    if (inventoryId && inventoryItem) {
      setDefaultInputs({
        ...inventoryItem,
        description: inventoryItem.description || '',
        hasStockValidation: !inventoryItem.hasStockValidation,
      });

      setAttributeFormInputs(inventoryItem?.attributes || []);
      setNewAttributesForm(inventoryItem?.attributes || []);
    }
  }, [inventoryId, inventoryItem]);

  const {
    validation: {
      nameRequired,
      skuRequired,
      lengthDescriptionFailed,
      stockValueError,
    },
    inventory: {
      addInventoryFailed,
      updateInventoryFailed,
      addInventorySuccess,
      updateInventorySuccess,
    },
    error: {
      responseError,
    },
  } = TextService.getText();

  const checkValidations = async () => {
    if (!newForm?.name) {
      Notification({ message: nameRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      setDataErrorName(INPUT_NAME.NAME);
      return true;
    }

    if (!newForm?.isSkuAutoGenerated && !newForm?.sku) {
      Notification({ message: skuRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      setDataErrorName(INPUT_NAME.SKU);
      return true;
    }

    if (newForm?.description?.length > 500) {
      Notification({ message: lengthDescriptionFailed, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return true;
    }

    if (!newForm.hasStockValidation && newForm.stock < 0) {
      Notification({ message: stockValueError, type: MSG_ERROR_TYPES.ERROR });
      setDataErrorName(INPUT_NAME.STOCK);
      setIsloading(false);
      return true;
    }

    return false;
  };

  const onSave = async () => {
    const token = await getToken();

    const payload = {
      ...newForm,
      name: newForm?.name?.trim(),
      description: newForm?.description === '' ? null : newForm?.description?.trim(),
      stock: newForm?.stock,
      standardIdentifier: newForm?.standardIdentifier === '' ? null : newForm?.standardIdentifier,
      attributes: newAttributesForm?.length === 0 ? null : newAttributesForm,
      measurementUnit: newForm?.measurementUnit?.trim(),
      hasStockValidation: !newForm?.hasStockValidation,
    };

    try {
      const response: CustomAxiosResponse = !inventoryId
        ? await createInventory(token, payload as NewFormInventoryProps) : await updateInventory(token, inventoryId, payload as NewFormInventoryProps);
      if (response.statusCode === 409) return;
      if (!response?.status) {
        Notification({ message: !inventoryId ? addInventoryFailed : updateInventoryFailed, type: MSG_ERROR_TYPES.ERROR });
        return;
      }

      const entityId = id ? response?.data?.id : response.data?.[0].id;

      if ((imagesToRemove.length > 0 || newForm?.images?.length) && entityId && token) {
        await handleMediaOperations({
          entity: 'inventory',
          entityId,
          images: newForm?.images,
          token,
        });
      }

      Notification({ message: !inventoryId ? addInventorySuccess : updateInventorySuccess, type: MSG_ERROR_TYPES.SUCCESS });

      if (id) {
        refetchInventoryItem();
      }

      if (!inventoryId && response.data) {
        const createdInventoryId = Array.isArray(response.data) && response.data.length > 0
          ? response.data[0].id
          : response.data.id;

        if (createdInventoryId) {
          goToPath(ApplicationRegistry.PathService.inventory.viewInventory(createdInventoryId));
          return;
        }
      } else if (inventoryId) {
        setEditMode(false);
        return;
      }

      goToPath(ApplicationRegistry.PathService.inventory.base());
    } catch (err) {
      Notification({ message: responseError, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleChildStateChange = (newState: NewFormInventoryProps) => {
    setNewForm(newState);
  };

  return (
    <CreateUpdateItem
      title={inventoryId ? 'Actualizar pieza de inventario' : 'Nueva pieza de inventario'}
      addButtonLabel={inventoryId ? 'Actualizar pieza de inventario' : 'Guardar'}
      checkValidations={checkValidations}
      editMode={editMode}
      setEditMode={setEditMode}
      isLoading={isLoading || loading}
      setIsloading={setIsloading}
      attributeFormInputs={attributeFormInputs}
      setNewAttributesForm={setNewAttributesForm}
      setInputEmptyError={setInputEmptyError}
      newAttributesForm={newAttributesForm}
      inputEmptyError={inputEmptyError}
      onSave={onSave}
      >
      <InventoryForm
        handleChildStateChange={handleChildStateChange}
        defaultInputs={defaultInputs}
        editMode={editMode}
        dataErrorName={dataErrorName}
        />
      {inventoryId && <FieldText
        label='Stock restringido'
        value={inventoryItem?.restrictedStock ?? 0}
        tooltip={{
          content: 'Indica la cantidad de un producto que no está disponible para su uso o venta inmediata.',
        }}
        />}
    </CreateUpdateItem>
  );
}

export const CreateUpdateInventoryModule = () => (
  <MediaToRemoveProvider>
    <CreateUpdateInventoryContent />
  </MediaToRemoveProvider>
);
