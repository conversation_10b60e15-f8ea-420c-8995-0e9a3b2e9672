import { useContext } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { ProvidersFormComponent } from '#appComponent/inventoryProviders/ProvidersForm.Component';
import { INVENTORY_ADD_SUCCES, INVENTORY_UPDATE_SUCCES } from '#application/constants/texts/InventoryForm.Constants';
import { AddProviderInventory } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import { useInventoryProviders } from '#infrastructure/implementation/application/hooks/useInventoryProviders';

const InventoryProvidersModule = () => {
  const { inventoryId } = useContext(ApplicationRegistry.InventoryContext);
  const {
    providersByInventoryId, removeProvider, updateProvider, addProvider,
  } = useInventoryProviders(inventoryId || '');

  // TODO: Implement infinite scroll
  const { items: providers } = ApplicationRegistry.ProviderService.useGetProviders({
    limit: 100, offset: 1, orderBy: 'name', order: 'asc',
  });

  const handleSaveProviders = async (provider?: AddProviderInventory, oldProvider?: AddProviderInventory | null) => {
    try {
      if (oldProvider) {
        await updateProvider(oldProvider, provider);
      } else {
        await addProvider(provider);
      }
      Notification({ message: !inventoryId ? INVENTORY_ADD_SUCCES : INVENTORY_UPDATE_SUCCES, type: MSG_ERROR_TYPES.SUCCESS });
    } catch (error) {
      Notification({ message: 'Error al guardar proveedor', type: MSG_ERROR_TYPES.ERROR });
    }
  };

  const handleRemoveProvider = async (providerId: string) => {
    try {
      await removeProvider(providerId);
      Notification({ message: !inventoryId ? INVENTORY_ADD_SUCCES : INVENTORY_UPDATE_SUCCES, type: MSG_ERROR_TYPES.SUCCESS });
    } catch (error) {
      Notification({ message: 'Error al eliminar proveedor', type: MSG_ERROR_TYPES.ERROR });
    }
  };

  return (
    <ProvidersFormComponent
      initialProviders={providersByInventoryId || []}
      allProviders={providers || []}
      onSaveProviders={handleSaveProviders}
      onRemoveProvider={handleRemoveProvider}
    />
  );
};

export default InventoryProvidersModule;
