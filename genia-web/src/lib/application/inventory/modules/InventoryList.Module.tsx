import { Column, Row } from '@pitsdepot/storybook';

import ProductImg from '#/assets/icon.png';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import {
  AttributesCell, InventoryProductCell, StatusCell, StockCell,
} from '#appComponent/table/TableCells.Component';
import { InventoryItem } from '#application/inventory/Inventory.Type';
import ApplicationRegistry from '#composition/Application.Registry';

const columns: Column[] = [
  {
    header: 'Producto',
    dataKey: 'product',
    width: '30%',
    renderer: InventoryProductCell,
  },
  {
    header: 'Stock',
    dataKey: 'stock',
    width: '10%',
    renderer: StockCell,
  },
  {
    header: 'Stock restringido',
    dataKey: 'restrictedStock',
    width: '5%',
  },
  {
    header: 'Unidad de Medida',
    dataKey: 'measurement',
    width: '10%',
  },
  { header: 'Sku', dataKey: 'sku', width: '10%' },
  {
    header: 'Atributos',
    dataKey: 'attributes',
    width: '20%',
    renderer: AttributesCell,
  },
  {
    header: 'Status',
    dataKey: 'status',
    width: '10%',
    renderer: StatusCell,
  },
];

function mapper(items: InventoryItem[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    product: { name: item.name, imgUrl: item.inventoryMedia?.[0]?.url || ProductImg, id: item.id },
    sku: item.sku,
    stock: { value: item.stock, validation: !item.hasStockValidation },
    restrictedStock: item.restrictedStock,
    attributes: item.attributes,
    status: item.disabledAt,
    measurement: item.measurementUnit,
  }));
}

export function InventoryListModule() {
  return (
    <PaginatedListAppComponent
      title='Inventario'
      columns={columns}
      useGetHook={ApplicationRegistry.InventoryService.useGetInventory}
      mapper={mapper}
      searchTitle='Buscar por SKU, nombre, descripción...'
      orderBy='stock'
      showHeader={false}
    />
  );
}
