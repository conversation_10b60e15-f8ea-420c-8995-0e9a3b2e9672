import {
  useCallback,
} from 'react';

import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import InventoryistoryComponent from '#appComponent/inventory/InventoryHistory.Component';
import InventoryHistoryFormComponent from '#appComponent/inventory/InventoryHistoryForm.Component';
import { UseInventoryHistory } from '#application/inventory/hooks/UseInventoryHistory.Hook';

function InventoryHistoryModule() {
  const { startEditing, cancelEditing, isEditing } = UseInventoryHistory();

  const handleClick = useCallback(() => {
    startEditing();
  }, [startEditing]);

  const handleBack = useCallback(() => {
    cancelEditing();
  }, [cancelEditing]);

  return (
    <div className="flex-1 bg-white p-5 rounded-2xl shadow-md flex flex-col gap-2 max-h-fit" >
      <div className='flex items-center justify-between h-12'>
        {
          isEditing ? (
            <h2
              className='text-md cursor-pointer'
              onClick={handleBack}
              >Volver</h2>
          ) : (
            <>
              <h2 className="text-xl">Historial de inventario</h2>
              <OrderActionButtonsAppComponent
                type={isEditing ? 'save' : 'edit'}
                onClick={handleClick}
                isSaving={false}
                />
            </>
          )
        }
      </div>
      { isEditing ? (
        <InventoryHistoryFormComponent/>
      ) : (
        <InventoryistoryComponent />
      )}
    </div>
  );
}

export default InventoryHistoryModule;
