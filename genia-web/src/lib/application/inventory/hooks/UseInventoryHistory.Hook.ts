import { useCallback, useContext, useState } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import { InventoryHistoryValueObject, MovementType } from '#domain/aggregates/inventory/InventoryHistory.ValueObject';

interface InventoryHistoryFormState {
    quantity: string;
    type: MovementType | '';
    reason: string;
}

interface TouchedFields {
    quantity: boolean;
    type: boolean;
    reason: boolean;
}

interface UseInventoryHistoryState {
    formState: InventoryHistoryFormState;

    isEditing: boolean;
    isSaving: boolean;
    isValid: boolean;
    errors: Record<string, string>;

    setQuantity: (value: string) => void;
    setMovementType: (value: MovementType) => void;
    setReason: (value: string) => void;

    startEditing: () => void;
    cancelEditing: () => void;
    resetForm: () => void;

    saveHistoryMovement: () => Promise<void>;
}

const initialFormState: InventoryHistoryFormState = {
  quantity: '',
  type: '',
  reason: '',
};

const initialTouchedState: TouchedFields = {
  quantity: false,
  type: false,
  reason: false,
};

function validateForm(formState: InventoryHistoryFormState, touched: TouchedFields): Record<string, string> {
  const errors: Record<string, string> = {};

  if (touched.quantity && !formState.quantity) {
    errors.quantity = 'La cantidad es requerida';
  } else if (touched.quantity && (Number.isNaN(Number(formState.quantity)) || Number(formState.quantity) <= 0)) {
    errors.quantity = 'La cantidad debe ser un número mayor a 0';
  }

  if (touched.type && !formState.type) {
    errors.type = 'El tipo de movimiento es requerido';
  }

  if (touched.reason && formState.reason.trim().length <= 2 && formState.reason.trim().length > 0) {
    errors.reason = 'Demasiado corto';
  }

  if (touched.reason && formState.reason.trim().length > 80) {
    errors.reason = 'Demasiado largo';
  }

  return errors;
}

export function UseInventoryHistory(
  initialHistory?: InventoryHistoryValueObject,
): UseInventoryHistoryState {
  const {
    cancelEditingHistory,
    startEditingHistory,
    setIsSavingHistory,
    setIsEditingHistory,
    isEditingHistory: isEditing,
    isSavingHistory: isSaving,
  } = useContext(ApplicationRegistry.InventoryContext);

  const { getToken } = useContext(ApplicationRegistry.AuthContext);

  const { inventoryId, setRefetchInventory } = useContext(ApplicationRegistry.InventoryContext);

  if (!inventoryId) {
    throw new Error('No inventoryId provided');
  }

  const [formState, setFormState] = useState<InventoryHistoryFormState>(() => {
    if (initialHistory) {
      return {
        quantity: initialHistory.quantity.value.toString(),
        type: initialHistory.movementType.value,
        reason: initialHistory.reason.value || '',
      };
    }
    return initialFormState;
  });

  const [touched, setTouched] = useState<TouchedFields>(initialTouchedState);

  const errors = validateForm(formState, touched);
  const isValid = Object.keys(errors).length === 0;

  const setQuantity = useCallback((value: string) => {
    setFormState((prev) => ({ ...prev, quantity: value }));
    setTouched((prev) => ({ ...prev, quantity: true }));
  }, []);

  const setMovementType = useCallback((value: MovementType) => {
    setFormState((prev) => ({ ...prev, type: value }));
    setTouched((prev) => ({ ...prev, type: true }));
  }, []);

  const setReason = useCallback((value: string) => {
    setFormState((prev) => ({ ...prev, reason: value }));
    setTouched((prev) => ({ ...prev, reason: true }));
  }, []);

  const startEditing = useCallback(() => {
    startEditingHistory();
  }, []);

  const cancelEditing = useCallback(() => {
    cancelEditingHistory();
    setTouched(initialTouchedState);
    if (initialHistory) {
      setFormState({
        quantity: initialHistory.quantity.value.toString(),
        type: initialHistory.movementType.value,
        reason: initialHistory.reason.value || '',
      });
    } else {
      setFormState(initialFormState);
    }
  }, [initialHistory]);

  const resetForm = useCallback(() => {
    setFormState(initialFormState);
    setTouched(initialTouchedState);
  }, []);

  const saveHistoryMovement = useCallback(async () => {
    setTouched({ quantity: true, type: true, reason: true });

    const allErrors = validateForm(formState, { quantity: true, type: true, reason: true });
    if (Object.keys(allErrors).length > 0) return;

    setIsSavingHistory(true);

    try {
      const token = await getToken();

      const payload = {
        quantity: Number(formState.quantity),
        movementType: formState.type as MovementType,
        reason: formState.reason,
      };

      const response = await ApplicationRegistry.InventoryService.createInventoryHistoryMovement({ token, inventoryId, payload });

      if (response.status === 204) {
        Notification({ message: 'Movimiento guardado correctamente', type: MSG_ERROR_TYPES.SUCCESS });
      }
      setIsEditingHistory(false);
      setRefetchInventory?.(true);
      resetForm();
    } catch (error) {
      Notification({ message: 'Error al guardar el movimiento', type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsSavingHistory(false);
    }
  }, [formState, resetForm]);

  return {
    ...formState,
    formState,
    isEditing,
    isSaving,
    isValid,
    errors,
    setQuantity,
    setMovementType,
    setReason,
    startEditing,
    cancelEditing,
    resetForm,
    saveHistoryMovement,
  };
}
