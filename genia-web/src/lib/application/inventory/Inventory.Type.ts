interface CategoryAttributes {
  key: string;
  values?: string[];
  categories?: CategoryAttributes[];
}

type InventoryTypeProps = 'commodity' | 'product_input';

type MediaType = 'image' | 'video';
interface Media {
  id: string;
  mediaType: MediaType;
  url: string;
}

export type InventoryItem = {
  id: string;
  name: string;
  sku: string | null;
  description?: string | null;
  stock: number;
  attributes: CategoryAttributes[];
  measurementUnit: string | null;
  disabledAt: boolean | null;
  hasStockValidation: boolean;
  companyId: string;
  createdAt: string;
  standardIdentifier: string | null
  restrictedStock: number;
  type: InventoryTypeProps;
  inventoryMedia: Media[];
  price?: number; // Agregamos el campo price para compatibilidad
};

type InventoryMovementType = 'inbound' | 'outbound';

export interface InventoryHistory {
  id: string;
  inventoryId: string;
  quantity: number;
  movementType: InventoryMovementType;
  reason?: string | null;
  userId: string;
  measurementUnit: string;
  createdAt: string;
  user: {
    email: string;
  };
}

export interface InventoryHistoryPayload {
  quantity: number;
  movementType: InventoryMovementType;
  reason?: string | null;
}

export interface CreateHistoryMovementProps {
  token: string | void;
  inventoryId: string;
  payload: InventoryHistoryPayload;
}
