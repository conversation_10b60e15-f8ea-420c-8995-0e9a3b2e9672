import { Avatar } from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

export function CompanyProfileModule() {
  const { company, loading, error } = ApplicationRegistry.CompanyService.useGetCurrentCompany();
  const text = TextService.getText();

  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (error) {
      Notification({
        message: 'Error al cargar la información de la compañía',
        type: MSG_ERROR_TYPES.ERROR,
      });
    }
  }, [error]);

  if (loading) {
    return (
      <div className='flex flex-col w-full gap-4 p-7'>
        <div className='flex justify-center items-center h-64'>
          <div className='text-lg text-gray-600'>Cargando información de la compañía...</div>
        </div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className='flex flex-col w-full gap-4 p-7'>
        <div className='flex justify-center items-center h-64'>
          <div className='text-lg text-gray-600'>No se pudo cargar la información de la compañía</div>
        </div>
      </div>
    );
  }

  const getFieldValue = (value?: string) => value || 'no disponible';

  const handleActionButtonClick = (currentType: 'edit' | 'save') => {
    if (currentType === 'save') {
      // TODO: Implementar lógica de guardado
      setIsSaving(true);
      setTimeout(() => {
        setIsSaving(false);
        setIsEditing(false);
        Notification({
          message: 'Información de la compañía actualizada exitosamente',
          type: MSG_ERROR_TYPES.SUCCESS,
        });
      }, 1000);
    } else {
      setIsEditing(true);
    }
  };

  return (
    <div className='flex flex-col w-full gap-6 p-7 pb-7'>
      <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
        <div className='flex items-center justify-between mb-6'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>{text.company.profile}</h1>
            <p className='text-gray-600 mt-1'>{text.company.information}</p>
          </div>
          <OrderActionButtonsAppComponent
            type={isEditing ? 'save' : 'edit'}
            onClick={handleActionButtonClick}
            isSaving={isSaving}
            disabled={isSaving}
          />
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div className='flex items-start gap-4'>
            <div className='flex-shrink-0'>
              <Avatar
                size={64}
                name={company.name}
              />
            </div>

            <div className='flex-1 space-y-3'>
              <div>
                <label className='block text-sm font-medium text-gray-700 '>
                  {text.company.name}
                </label>
                <p className='text-gray-900 text-lg font-semibold'>{company.name}</p>
                <p className='text-gray-400'>{getFieldValue(company.country)}</p>
              </div>
            </div>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              ID Tributario
            </label>
            <p className='text-gray-900'>{getFieldValue(company.tributaryId)}</p>
          </div>

          <div className='md:col-span-2'>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              {text.company.description}
            </label>
            {isEditing ? (
              <textarea
                defaultValue={company.description || ''}
                placeholder="Ingrese la descripción de la empresa"
                rows={4}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            ) : (
              <p className='text-gray-900 whitespace-pre-wrap'>{getFieldValue(company.description)}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
