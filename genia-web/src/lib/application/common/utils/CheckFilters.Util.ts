import { FilterGroup } from '@pitsdepot/storybook';

import { SearchFilter } from '#appComponent/table/Table.Type';

export function checkedFilters<T extends string>(filters: FilterGroup<T>[], selectedFilters: SearchFilter): FilterGroup<T>[] {
  return filters.map((filter) => ({
    ...filter,
    options: filter.options.map((option) => ({
      ...option,
      selected: selectedFilters[filter.key]?.includes(option.id) || false,
    })),
  }));
}
