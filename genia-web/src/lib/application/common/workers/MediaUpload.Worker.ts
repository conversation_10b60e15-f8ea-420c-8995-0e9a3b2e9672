import { SignedUrlResult, UploadResult } from '#application/common/services/Media.Service';

interface WorkerData {
    kind: string;
    filesToUpload: File[];
    entity: string;
    entityId: string;
    token: string | null | undefined;
    getSignedUrlEndpoint: string;
    postMediaEndpoint: string;
    signedUrlResults?: SignedUrlResult[];
}

async function uploadFilesToSignedUrls(signedUrlResults: SignedUrlResult[]): Promise<UploadResult[]> {
  const uploadPromises = signedUrlResults
    .filter((result) => result.success && result.data && result.data.signedUrl && result.fileObject)
    .map(async (result) => {
      const fileToUpload = result.fileObject;
      const { signedUrl } = (result.data!);
      const fileName = result.fileName || fileToUpload.name;

      try {
        const uploadResponse = await fetch(signedUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': fileToUpload.type || 'application/octet-stream',
          },
          body: fileToUpload,
        });

        if (uploadResponse.ok) {
          return { success: true, fileName, status: uploadResponse.status };
        }

        const errorBody = await uploadResponse.text();
        return {
          success: false, fileName, status: uploadResponse.status, error: errorBody,
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Network error';
        return { success: false, fileName, error: errorMessage };
      }
    });

  const uploadOutcomes = await Promise.allSettled(uploadPromises);

  return uploadOutcomes.map((outcome) => {
    if (outcome.status === 'fulfilled') {
      return outcome.value;
    }
    return {
      success: false,
      fileName: 'Unknown file',
      error: outcome.reason instanceof Error ? outcome.reason.message : 'Unknown error',
    };
  });
}

self.onmessage = async (event: MessageEvent<WorkerData>) => {
  const {
    signedUrlResults,
  } = event.data;

  try {
    if (!signedUrlResults || signedUrlResults.length === 0) {
      self.postMessage({
        success: false,
        isComplete: true,
        error: 'No se recibieron resultados de URLs firmadas',
      });
      return;
    }

    const uploadResults = await uploadFilesToSignedUrls(signedUrlResults);

    uploadResults.forEach((result: UploadResult) => {
      self.postMessage({
        success: result.success,
        fileName: result.fileName,
        message: result.success
          ? `Archivo ${result.fileName} cargado exitosamente.`
          : `Error al subir el archivo ${result.fileName}'}`,
      });
    });

    self.postMessage({
      success: true,
      isComplete: true,
      message: 'Proceso de carga completado',
    });
  } catch (error) {
    self.postMessage({
      success: false,
      isComplete: true,
      error: 'No se pudieron subir los archivos, por favor intenta nuevamente',
    });

    throw new Error(error instanceof Error ? error.message : String(error));
  }
};
