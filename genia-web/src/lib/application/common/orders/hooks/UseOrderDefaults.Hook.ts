import Theme from '@pitsdepot/storybook/theme';
import { useMemo } from 'react';

import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity, OrderError } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusIds, OrderStatusValueObject, OrderStatusValueObjectParams } from '#domain/aggregates/order/OrderStatus.ValueObject';

interface UseOrderState {
  getInitialUpdatableOrder: (order: Order) => OrderEntity;
  getInitialCreateOrder: (receiverType: 'client' | 'provider') => OrderEntity;
}

const text = TextService.getText();

export const statusParam: Record<string, OrderStatusValueObjectParams> = {
  client_approval: { id: OrderStatusIds.CLIENT_APPROVAL, name: text.orders.statusClientApproval, color: Theme.colors.fadedTosca },
  approved_by_client: { id: OrderStatusIds.APPROVED_BY_CLIENT, name: text.orders.statusApprovedByClient, color: Theme.colors.fadedViolet },
  pending: { id: OrderStatusIds.PENDING, name: text.orders.statusPending, color: Theme.colors.fadedOrange },
  processing: { id: OrderStatusIds.PROCESSING, name: text.orders.statusProcessing, color: Theme.colors.fadedBlue },
  in_transit: { id: OrderStatusIds.IN_TRANSIT, name: text.orders.statusInTransit, color: Theme.colors.fadedPeach },
  completed: { id: OrderStatusIds.COMPLETED, name: text.orders.statusCompleted, color: Theme.colors.fadedGreen },
  cancelled: { id: OrderStatusIds.CANCELLED, name: text.orders.statusCancelled, color: Theme.colors.fadedRed },
  in_review: { id: OrderStatusIds.IN_REVIEW, name: text.orders.statusInReview, color: Theme.colors.fadedYellow },
};

function getInitialUpdatableOrder(order: Order): OrderEntity {
  const newOrderEntity = OrderEntity.fromJson(order)
    .disableItems(true)
    .setReceiver({
      ...order.receiver,
      disabled: true,
    });

  const hasCompanyProvider = order.receiver.type === 'provider' && !!order.receiver.companyId;
  const isClientApproval = newOrderEntity.info.status.value.id === OrderStatusIds.CLIENT_APPROVAL;

  const newOrderInfo = newOrderEntity.info
    .setOrderNumber({
      ...newOrderEntity.info.orderNumber,
      disabled: true,
      hidden: true,
    })
    .setShippingPrice({
      ...newOrderEntity.info.shippingPrice,
      disabled: hasCompanyProvider,
      hidden: true,
    })
    .setDeliveryDate({
      ...newOrderEntity.info.deliveryDate,
      disabled: hasCompanyProvider,
      hidden: false,
    })
    .setShippingAddress({
      ...newOrderEntity.info.shippingAddress,
      disabled: hasCompanyProvider,
      hidden: false,
    })
    .setStatus({
      ...newOrderEntity.info.status,
      disabled: hasCompanyProvider && !isClientApproval,
      value: new OrderStatusValueObject({
        ...statusParam[newOrderEntity.info.status.value.id],
        id: newOrderEntity.info.status.value.id,
        name: statusParam[newOrderEntity.info.status.value.id].name,
        color: statusParam[newOrderEntity.info.status.value.id].color,
      }),
    });

  return newOrderEntity
    .setInfo(newOrderInfo);
}

function getInitialCreateOrder(receiverType: 'client' | 'provider'): OrderEntity {
  return useMemo(() => {
    const orderEntity = OrderEntity.empty(receiverType);

    const isPurchaseOrder = receiverType === 'provider';

    const items = orderEntity.orderItems.map((item) => item.disable(true));

    const orderInfo = orderEntity
      .info.disable(true)
      .setOrderNumber({
        ...orderEntity.info.orderNumber,
        hidden: isPurchaseOrder,
        disabled: true,
      })
      .setStatus({
        ...orderEntity.info.status,
        hidden: true,
      });

    const order = orderEntity.setInfo(orderInfo)
      .setOrderItems(items)
      .setErrors((!isPurchaseOrder ? { [OrderError.ORDER_NUMBER_REQUIRED]: 'required' } : {}) as Record<OrderError, string>);

    return order;
  }, []);
}

export function UseOrderDefaultsHook() : UseOrderState {
  return {
    getInitialCreateOrder,
    getInitialUpdatableOrder,
  };
}
