import { useCallback, useState } from 'react';

import { OrderConditions, PublicCatalogDiscount, PublicStoreDiscount } from '#application/common/orders/Order.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import { OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderItemEntity, OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderItemDiscountEntity } from '#domain/aggregates/order/OrderItemDiscount.Entity';
import { OrderTaxValueObject } from '#domain/aggregates/order/OrderTax.ValueObject';

interface UseOrderState {
  orderEntity: OrderEntity;
  handleUpdates: (entity: OrderEntity) => void;
  setConditions: (entity: OrderEntity, conditions: OrderConditions) => void;
  addItemToOrder: (entity: OrderEntity) => void;
  setInventoryRelations: (entity: OrderEntity, relations: Record<string, OrderItemInventoryRelation[]>) => void;
}

const handleReceiver = (entity: OrderEntity, oldEntity: OrderEntity) => {
  if (!entity.receiver.id) return entity;

  let newEntity = entity;
  const isSaleOrder = newEntity.receiver.type === 'client';

  if (entity.receiver.id !== oldEntity.receiver.id && !isSaleOrder) {
    newEntity = entity.clean();

    const isProviderWithCompany = !!newEntity.receiver.companyId;

    const items = newEntity.orderItems.map((item) => item
      .setId(!isProviderWithCompany ? ApplicationRegistry.IdService.generateId() : '')
      .disableName(false)
      .disableProductNumber(isProviderWithCompany)
      .disableQuantity(isProviderWithCompany)
      .disableUnitPrice(isProviderWithCompany)
      .disableUnitPriceAfterDiscount(isProviderWithCompany)
      .disableUnitPriceAfterDiscountAndTaxes(true));

    const info = newEntity.info.disableStatus(true).hideStatus(true);

    newEntity = newEntity
      .setOrderItems(items)
      .setInfo(info);
  } else if (entity.receiver.id !== oldEntity.receiver.id && isSaleOrder) {
    const atLeastOneItemValid = newEntity.orderItems.some((item) => item.id && item.quantity.value > 0);

    newEntity = entity.setInfo(entity.info.setSummary({
      ...entity.info.summary,
      loading: atLeastOneItemValid,
    }));
    newEntity = newEntity.setOrderItems(newEntity.orderItems.map((item) => item.setSummary({
      ...item.summary,
      loading: !!item.id && item.quantity.value > 0,
    })));
  }

  const hasProviderCompany = newEntity.receiver.type === 'provider' && !!newEntity.receiver.companyId;

  const info = newEntity.info.disableNotes(false)
    .disableOrderNumber(!isSaleOrder)
    .hideOrderNumber(!isSaleOrder)
    .disableShippingAddress(false)
    .disableShippingPrice(hasProviderCompany)
    .hideShippingPrice(hasProviderCompany)
    .disableDeliveryDate(hasProviderCompany)
    .hideDeliveryDate(hasProviderCompany);

  const orderItems = newEntity.orderItems.map((item) => item.disableName(false));

  return newEntity.setInfo(info)
    .setOrderItems(orderItems);
};

function mapDiscount(conditionDiscount: PublicStoreDiscount | PublicCatalogDiscount): OrderItemDiscountEntity {
  const discountCategory = (conditionDiscount as PublicStoreDiscount).requiredAmount !== undefined ? 'store' : 'catalog';

  return new OrderItemDiscountEntity({
    id: conditionDiscount.id,
    discountValue: conditionDiscount.discountValue,
    discountType: conditionDiscount.discountType,
    discountCategory,
    name: conditionDiscount.name,
    priceAfterDiscount: conditionDiscount.priceAfterDiscount,
    startDate: conditionDiscount.startDate,
    endDate: conditionDiscount.endDate,
    required: (conditionDiscount as PublicStoreDiscount).requiredAmount || (conditionDiscount as PublicCatalogDiscount).requiredQuantity || 0,
  });
}

const handleItemChanges = (entity: OrderEntity, oldEntity: OrderEntity): OrderEntity => {
  const oldItemsMap = new Map(oldEntity.orderItems.map((item) => [item.id, item]));
  let hasNewInfo = false;
  let newInfo = entity.info;

  const isPurchaseOrder = entity.receiver.type === 'provider';
  const hasReceiverCompanyId = !!entity.receiver.companyId;
  const purchaseOrderWithoutCompany = isPurchaseOrder && !hasReceiverCompanyId;

  const items = entity.orderItems.map((item) => {
    const oldItem = oldItemsMap.get(item.id);

    const shouldDisableQuantity = purchaseOrderWithoutCompany ? false : !item.id;

    let newItem = item.disableQuantity(shouldDisableQuantity);
    let hasChanged = false;

    if (oldItem) {
      newItem = newItem.setUnitPriceAfterDiscount({
        ...item.unitPriceAfterDiscount,
        value: (purchaseOrderWithoutCompany && newItem.unitPriceAfterDiscount.value === 0 && newItem.unitPrice.value > 0)
          ? newItem.unitPrice.value : newItem.unitPriceAfterDiscount.value,
      });

      const hasChangedQuantity = newItem.quantity.value !== oldItem.quantity.value;

      const quantityIsValid = !Number.isNaN(newItem.quantity.value) && newItem.quantity.value > 0;

      const unitPriceIsValid = purchaseOrderWithoutCompany ? !Number.isNaN(newItem.unitPrice.value) && newItem.unitPrice.value > 0 : true;

      const unitPriceAfterDiscountIsValid = purchaseOrderWithoutCompany
        ? !Number.isNaN(newItem.unitPriceAfterDiscount.value) && newItem.unitPriceAfterDiscount.value > 0 : true;

      const hasChangedUnitPrice = purchaseOrderWithoutCompany ? (newItem.unitPrice.value !== oldItem.unitPrice.value) : false;

      const hasChangedUnitPriceAfterDiscount = purchaseOrderWithoutCompany
        ? (newItem.unitPriceAfterDiscount.value !== oldItem.unitPriceAfterDiscount.value) : false;

      if ((hasChangedQuantity || hasChangedUnitPrice || hasChangedUnitPriceAfterDiscount)
           && quantityIsValid && unitPriceIsValid && unitPriceAfterDiscountIsValid) {
        hasChanged = true;

        newItem = newItem.setInventoryRelations(
          newItem.inventoryRelations.map((relation) => ({
            ...relation,
            total: newItem.quantity.value * relation.quantity,
          })),
        );
      }
    }

    if (!oldItem && newItem.id && !purchaseOrderWithoutCompany) {
      hasChanged = true;
      newItem = newItem.setInventoryRelations([]);
    }

    if (!oldItem && purchaseOrderWithoutCompany) {
      hasChanged = (newItem.quantity.value > 0 && newItem.unitPrice.value > 0 && newItem.unitPriceAfterDiscount.value > 0);
    }

    if (hasChanged) {
      newItem = newItem.setSummary({
        ...item.summary,
        loading: true,
      });
    }

    if (hasChanged && !hasNewInfo) {
      newInfo = entity.info.setSummary({
        ...entity.info.summary,
        loading: hasChanged,
      });

      hasNewInfo = true;
    }

    return newItem;
  });

  return entity
    .setOrderItems(items)
    .setInfo(newInfo);
};

const handleShippingPriceChange = (entity: OrderEntity, oldEntity: OrderEntity): OrderEntity => {
  const oldShippingPrice = oldEntity.info.shippingPrice.value;
  const newShippingPrice = entity.info.shippingPrice.value;
  const hasChanged = oldShippingPrice !== newShippingPrice;
  const hasAtLeastOneItem = entity.orderItems.some((item) => item.id && item.quantity.value > 0);
  const hasValidPrice = newShippingPrice === null || (newShippingPrice > 0 && newShippingPrice <= 1000000);

  const newInfo = entity.info
    .setSummary({
      ...entity.info.summary,
      loading: (hasChanged && hasAtLeastOneItem && hasValidPrice) || entity.info.summary.loading,
    });

  return entity
    .setInfo(newInfo);
};

function handleOrderUpdates(entity: OrderEntity, oldEntity: OrderEntity): OrderEntity {
  let newEntity = handleReceiver(entity, oldEntity);

  newEntity = handleItemChanges(newEntity, oldEntity);
  newEntity = handleShippingPriceChange(newEntity, oldEntity);

  return newEntity;
}

function setNewConditions(entity: OrderEntity, conditions: OrderConditions): OrderEntity {
  const items = entity.orderItems.map((item) => {
    const condition = conditions.orderItems.find((conditionItem) => conditionItem.id === item.id);

    if (condition) {
      const { discounts } = condition;
      const conditionDiscount = discounts?.appliedDiscount?.catalogDiscount || discounts?.appliedDiscount?.storeDiscount;
      let appliedDiscount: OrderItemDiscountEntity | null = null;
      const applicableDiscounts = discounts?.applicableDiscounts ? discounts.applicableDiscounts.catalogDiscounts
        .map(mapDiscount)
        .concat(discounts?.applicableDiscounts?.storeDiscounts?.map(mapDiscount)) : [];

      if (conditionDiscount) {
        appliedDiscount = mapDiscount(conditionDiscount);
      }

      return item
        .setUnitPrice({
          ...item.unitPrice,
          value: condition.unitPrice,
        })
        .setUnitPriceAfterDiscount({
          ...item.unitPriceAfterDiscount,
          value: condition.unitPriceAfterDiscount,
        })
        .setUnitPriceAfterDiscountAndTaxes({
          ...item.unitPriceAfterDiscountAndTaxes,
          value: condition.unitPriceAfterDiscountAndTaxes,
        })
        .setApplicableDiscounts(applicableDiscounts)
        .setAppliedDiscount(appliedDiscount)
        .setSummary({
          ...item.summary,
          loading: false,
          subtotal: {
            ...item.summary.subtotal,
            value: condition.subtotal,
          },
          total: {
            ...item.summary.total,
            value: condition.total,
          },
          taxes: condition.taxes.map((tax) => new OrderTaxValueObject({
            id: tax.id,
            name: tax.name,
            value: tax.value,
            amount: tax.amount,
            type: tax.type,
          })),
        });
    }
    return item;
  });

  const newSummary = entity.info.summary
    .setLoading(false)
    .setSubtotal({
      ...entity.info.summary.subtotal,
      value: conditions.subtotal,
    })
    .setTotal({
      ...entity.info.summary.total,
      value: conditions.total,
    })
    .setShippingPrice({
      ...entity.info.summary.shippingPrice,
      value: conditions.shippingPrice,
    })
    .setSubtotalBeforeDiscount({
      ...entity.info.summary.subtotalBeforeDiscount,
      value: conditions.subtotalBeforeDiscount,
    })
    .setTotalDiscount({
      ...entity.info.summary.totalDiscount,
      value: conditions.totalDiscount,
    })
    .setTaxes(conditions.taxes.map((tax) => new OrderTaxValueObject({
      id: tax.id,
      name: tax.name,
      value: tax.value,
      amount: tax.amount,
      type: tax.type,
    })));

  const info = entity.info.setSummary(newSummary);

  return entity.setOrderItems(items)
    .setInfo(info);
}

function addItem(entity: OrderEntity) {
  const { receiver } = entity;

  const isPurchaseOrder = receiver.type === 'provider';
  const isProviderWithoutCompany = isPurchaseOrder && !receiver.companyId;

  const newItem = OrderItemEntity.empty()
    .setId(isProviderWithoutCompany ? ApplicationRegistry.IdService.generateId() : '')
    .setQuantity({ value: 0, disabled: !isProviderWithoutCompany })
    .setUnitPrice({ value: 0, disabled: !isProviderWithoutCompany })
    .setUnitPriceAfterDiscount({ value: 0, disabled: !isProviderWithoutCompany })
    .setUnitPriceAfterDiscountAndTaxes({ value: 0, disabled: true })
    .setImage({ value: '', disabled: !isProviderWithoutCompany })
    .setName({ value: '', disabled: false })
    .setProductNumber({ value: '', disabled: !isProviderWithoutCompany });

  return entity.addItem(newItem);
}

function setNewInventoryRelations(entity: OrderEntity, relations: Record<string, OrderItemInventoryRelation[]>) {
  const items = entity.orderItems.map((item) => {
    const inventory = relations[item.id];
    if (item.inventoryRelations.length > 0) return item;
    if (inventory) {
      return item.setInventoryRelations(inventory.map((inv) => ({
        ...inv,
        total: item.quantity.value * inv.quantity,
      })));
    }
    return item.setInventoryRelations([]);
  });
  return entity.setOrderItems(items);
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function actionsWrapper(originalFunction: (...args: any[]) => OrderEntity, setOrderState: (entity: OrderEntity) => void) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (...args: any[]) => {
    const newEntity = originalFunction(...args);
    setOrderState(newEntity);
  };
}

export function UseOrder(initialOrder: OrderEntity) : UseOrderState {
  const [orderEntity, setOrderEntity] = useState<OrderEntity>(initialOrder);

  const setState = (newEntity: OrderEntity) => {
    setOrderEntity((prev) => {
      if (JSON.stringify(prev) !== JSON.stringify(newEntity)) {
        return newEntity;
      }
      return prev;
    });
  };
  const handleUpdates = useCallback(actionsWrapper((newEntity: OrderEntity) => handleOrderUpdates(newEntity, orderEntity), setState), [JSON.stringify(orderEntity)]);
  const setConditions = useCallback(actionsWrapper(setNewConditions, setState), [JSON.stringify(orderEntity)]);
  const addItemToOrder = useCallback(actionsWrapper(addItem, setState), [JSON.stringify(orderEntity)]);
  const setInventoryRelations = useCallback(actionsWrapper(setNewInventoryRelations, setState), [JSON.stringify(orderEntity)]);

  return {
    handleUpdates,
    setConditions,
    addItemToOrder,
    setInventoryRelations,
    orderEntity,
  };
}
