import { OrderTax } from '#domain/aggregates/order/OrderTax.ValueObject';

interface PublicDiscount {
  id: string;
  discountValue: number;
  discountType: string;
  name: string;
  priceAfterDiscount: number;
  startDate?: string | null;
  endDate?: string | null;
}

export interface PublicCatalogDiscount extends PublicDiscount {
  requiredQuantity: number;
}

export interface PublicStoreDiscount extends PublicDiscount {
  requiredAmount: number;
}

export interface OrderConditionItem {
  id: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  total: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  taxes: OrderTax[];
  discounts: {
    appliedDiscount: {
      catalogDiscount?: PublicCatalogDiscount| null;
      storeDiscount?: PublicStoreDiscount | null;
    };
    applicableDiscounts: {
      catalogDiscounts: PublicCatalogDiscount[];
      storeDiscounts: PublicStoreDiscount[];
    };
  }
}

export interface OrderConditions {
  subtotalBeforeDiscount: number;
  totalDiscount: number;
  subtotal: number;
  totalTaxes: number;
  shippingPrice: number;
  total: number;
  taxes: OrderTax[];
  orderItems: OrderConditionItem[];
  hasTaxOnShipping?: boolean;
}

export interface OrderConditionsParamsItem {
  quantity?: number;
  unitPrice?: number;
  taxIds?: string[];
  unitPriceAfterDiscount?: number;
}

export interface OrderConditionsParams{
  shippingPrice?: number;
  orderItems?:OrderConditionsParamsItem[];
  hasTaxOnShipping?: boolean;
}
