import { UserRole } from '#domain/aggregates/user/User.Entity';

export interface UserFormData {
  name: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode?: string; // Opcional para mantener compatibilidad con UpdateUser
  role: UserRole;
  disabledAt?: Date | null;
}

export const userFormInitialState: UserFormData = {
  name: '',
  lastName: '',
  email: '',
  phoneNumber: '',
  countryCode: '',
  role: UserRole.USER,
  disabledAt: null,
};
