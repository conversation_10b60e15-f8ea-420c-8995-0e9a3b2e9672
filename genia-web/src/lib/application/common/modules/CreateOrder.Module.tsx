import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { useDebounceCallback } from '#appComponent/common/hooks/useDebounce.Hook';
import { Notification } from '#appComponent/common/Notification.Component';
import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import {
  OrderDetailAppComponent,
} from '#appComponent/common/orders/orderDetail/OrderDetail.AppComponent';
import { OrderDetailContextProvider } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import { UseOrder } from '#application/common/orders/hooks/UseOrder.Hook';
import { UseOrderDefaultsHook } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { OrderConditions, OrderConditionsParams, OrderConditionsParamsItem } from '#application/common/orders/Order.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';

const textService = TextService.getText();

const errorsMap = {
  errorSave: textService.orders.errorSave,
  duplicatedOrderNumber: textService.orders.errorDuplicatedOrderNumber,
};

export interface ConditionsParamsItem extends OrderConditionsParamsItem {
  referenceId?: string;
}

export interface ConditionParams extends OrderConditionsParams {
  shippingPrice?: number;
  orderItems?:ConditionsParamsItem[];
  receiverId?: string;
}

export interface CreateOrderModuleProps {
  title: string;
  receiverType: 'client' | 'provider';
  getPath: (id: string) => string;
  useFetchReceiver: DropDownSearchFetch
  useFetchCatalog: DropDownSearchFetch
  useGetConditions: (params: ConditionParams) => {
    conditions: OrderConditions | null;
    error: unknown;
    loading: boolean;
  };
  useGetInventoryRelations: (ids: string[]) => {
    items: Record<string, OrderItemInventoryRelation[]>;
    error: unknown;
    loading: boolean;
  };
  onChange?: (order: Order | null, orderEntity: OrderEntity | null) => void;
  useFetchInventory?: DropDownSearchFetch;
  initialData?: OrderEntity;
  onSave: (payload: Order) => Promise<Order>;
  useFetchUsers?: DropDownSearchFetch;
}

export function CreateOrderModule(props: CreateOrderModuleProps) {
  const {
    title,
    useFetchReceiver,
    useFetchCatalog,
    useGetConditions,
    receiverType,
    getPath,
    useGetInventoryRelations,
    onChange,
    useFetchInventory,
    initialData,
    onSave,
    useFetchUsers,
  } = props;

  const { getInitialCreateOrder } = UseOrderDefaultsHook();
  const initialOrder = initialData || getInitialCreateOrder(receiverType);
  const {
    orderEntity, handleUpdates, setConditions, addItemToOrder, setInventoryRelations,
  } = UseOrder(initialOrder);
  const [orderState, setOrderState] = useState<Order | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const goToPath = ApplicationRegistry.PathService.useGoToPath();

  const currentItems = useMemo(
    () => orderEntity.orderItems
      .filter((item) => orderEntity.receiver.companyId && item.id)
      .map((item) => item.id),
    [orderEntity.orderItems.map((item) => item.id).join(',')],
  );

  const { items: itemsWithInventory, error: catalogWithInventoryError } = useGetInventoryRelations(currentItems);

  const isManualOrder = orderEntity.receiver.id && orderEntity.receiver.type === 'provider' && !orderEntity.receiver.companyId;

  const { items: taxes, error: taxesError } = ApplicationRegistry.TaxesService.useGetTaxes(!!isManualOrder);

  useEffect(() => {
    if (taxesError) {
      Notification({ message: textService.taxes.loadError, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [JSON.stringify(taxesError)]);

  useEffect(() => {
    if (catalogWithInventoryError) {
      return Notification({ message: textService.orders.loadError, type: MSG_ERROR_TYPES.ERROR });
    }

    return setInventoryRelations(orderEntity, itemsWithInventory);
  }, [JSON.stringify(itemsWithInventory), catalogWithInventoryError]);

  let payloadOrderItemsConditions = orderState?.orderItems
    .filter((item) => item.quantity > 0)
    .map((item) => ({
      referenceId: item.id,
      quantity: item.quantity,
    }));

  if (isManualOrder) {
    payloadOrderItemsConditions = orderState?.orderItems
      .filter((item) => item.quantity > 0)
      .map((item) => ({
        referenceId: item.id,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        unitPriceAfterDiscount: item.unitPriceAfterDiscount,
        unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes,
        taxIds: [taxes?.[0]?.id],
      }));
  }

  const { conditions, error } = useGetConditions({
    shippingPrice: orderState?.orderInfo.shippingPrice,
    hasTaxOnShipping: true,
    orderItems: payloadOrderItemsConditions,
    receiverId: orderState?.receiver.id || undefined,
  });

  useEffect(() => {
    if (error) {
      return Notification({ message: textService.orders.loadError, type: MSG_ERROR_TYPES.ERROR });
    }

    if (!conditions) return undefined;

    return setConditions(orderEntity, conditions);
  }, [conditions, error]);

  const onAddProduct = useCallback(() => {
    addItemToOrder(orderEntity);
  }, [JSON.stringify(orderEntity)]);

  const addProduct = useMemo(() => ({
    disabled: !orderEntity?.receiver?.id,
    onClick: onAddProduct,
  }), [orderEntity]);

  useDebounceCallback(() => {
    setOrderState(orderEntity.toJson());
  }, [JSON.stringify(orderEntity)], 500);

  useEffect(() => {
    if (onChange) {
      onChange(orderState, orderEntity);
    }
  }, [JSON.stringify(orderState), JSON.stringify(orderEntity)]);

  const onEntityChange = (newOrderEntity: OrderEntity) => {
    handleUpdates(newOrderEntity);
  };

  const onActionClick = async () => {
    try {
      setIsSaving(true);
      const order = orderEntity.toJson();
      const payload = {
        ...order,
        orderItems: order.orderItems.filter((item) => item.id),
      };
      const response = await onSave(payload);
      goToPath(getPath(response.id));
      Notification({ message: textService.orders.orderCreated, type: MSG_ERROR_TYPES.SUCCESS });
    } catch (e) {
      const responseError = e as Error;
      let errorMessage = errorsMap.errorSave;
      if (responseError.message.includes('already exists')) {
        errorMessage = errorsMap.duplicatedOrderNumber;
      }
      Notification({ message: errorMessage, type: MSG_ERROR_TYPES.ERROR });
    }

    setIsSaving(false);
  };

  const memoizedActionButtons = <OrderActionButtonsAppComponent
    isSaving={isSaving}
    disabled={!orderEntity.isValid()}
    onClick={onActionClick}
    type='save'
/>;

  return (
    <div className='bg-white w-full rounded-2xl shadow-lg p-8 flex flex-col gap-4'>
      <div className="flex justify-between items-center h-12">
        <h2 className="text-lg font-semibold">
          {title}
        </h2>
        {memoizedActionButtons}
      </div>
      <div className="flex flex-wrap gap-8">
        <div className='flex-1 relative z-10'>
          <OrderDetailContextProvider
            order={orderEntity}
            editable={true}
            useFetchCatalog={useFetchCatalog}
            useFetchReceiver={useFetchReceiver}
            useFetchUsers={useFetchUsers}
            useFetchStatuses={() => []}
            onChange={onEntityChange}
            useFetchInventory={useFetchInventory}
            onAddProduct={addProduct}>
            <OrderDetailAppComponent/>
          </OrderDetailContextProvider>
        </div>
      </div>
    </div>
  );
}
