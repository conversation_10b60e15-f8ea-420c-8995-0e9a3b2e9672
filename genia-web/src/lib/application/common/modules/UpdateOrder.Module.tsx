import { useEffect, useMemo, useState } from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { ModuleLoaderAppComponent } from '#appComponent/common/components/moduleLoader/ModuleLoader.AppComponent';
import { Notification } from '#appComponent/common/Notification.Component';
import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import {
  OrderDetailAppComponent,
} from '#appComponent/common/orders/orderDetail/OrderDetail.AppComponent';
import { OrderDetailContextProvider } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import { statusParam, UseOrderDefaultsHook } from '#application/common/orders/hooks/UseOrderDefaults.Hook';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';
import { Order, OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusIds, OrderStatusValueObject } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface OrderServiceUpdateParams {
  id: string;
  notes?: string;
  deliveryDate?: string | null;
  shippingAddress?: string | null;
  assignedUserId?: string;
  status?: OrderStatusIds;
}
export interface UpdateOrderModuleProps {
  entityId: string;
  title: string;
  useGetAvailableStatuses: (order: Order) => (() => OrderStatusValueObject[])
  useFetchReceiver: DropDownSearchFetch
  useUpdateOrder: () => ({
    applyOrderUpdate: (payload: OrderServiceUpdateParams) => Promise<Order>;
  })
  useFetchUsers: DropDownSearchFetch
  getOrder: (id: string) => {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  };
  mapToSavePayload: (orderEntity: OrderEntity, orderState: Order) => {
    id: string
    notes: string | undefined,
    deliveryDate: string | null | undefined,
    shippingAddress: string | undefined,
    status: OrderStatusIds | undefined,
    assignedUserId?: string;
  };
}

const textService = TextService.getText();

export function UpdateOrderModule(props: UpdateOrderModuleProps) {
  const {
    entityId,
    title,
    useGetAvailableStatuses,
    useFetchReceiver,
    getOrder,
    mapToSavePayload,
    useUpdateOrder,
    useFetchUsers,
  } = props;
  const { getInitialUpdatableOrder } = UseOrderDefaultsHook();
  const [editMode, setEditMode] = useState(false);
  const [orderState, setOrderState] = useState<Order | null>(null);
  const { applyOrderUpdate } = useUpdateOrder();
  const [isSaving, setIsSaving] = useState(false);
  const [orderEntity, setOrderEntity] = useState<OrderEntity | null>(null);
  const [upstreamStatus, setUpstreamStatus] = useState<OrderStatusIds | null>(null);

  const {
    order,
    loading: isLoadingOrder,
    error: orderError,
    refetch: refetchOrder,
  } = getOrder(entityId);

  const upstreamIsFinished = useMemo(() => [
    OrderStatusIds.COMPLETED, OrderStatusIds.CANCELLED,
  ].includes(upstreamStatus!), [upstreamStatus]);

  useEffect(() => {
    if (order) {
      setOrderEntity(getInitialUpdatableOrder(order));
      setUpstreamStatus(statusParam[order.orderInfo.status.id].id);
    }
  }, [order]);

  useEffect(() => {
    if (orderError) {
      Notification({ message: textService.orders.loadError, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [JSON.stringify(orderError)]);

  useEffect(() => {
    if (!orderEntity) return;
    setOrderState(orderEntity.toJson());
  }, [JSON.stringify(orderEntity)]);

  const onActionClick = async (currentAction: 'edit' | 'save') => {
    if (currentAction === 'edit') {
      setEditMode(true);
    } else if (currentAction === 'save') {
      try {
        if (!orderEntity || !orderState) return;

        const payload = mapToSavePayload(orderEntity, {
          ...orderState,
          orderInfo: {
            ...orderState.orderInfo,
            status: {
              ...orderState.orderInfo.status,
              id: upstreamStatus!,
            },
          },
        });

        const {
          shippingAddress,
          notes,
          deliveryDate,
          status,
          assignedUserId,
        } = payload;

        if (!shippingAddress && !notes && !deliveryDate && !status && !assignedUserId) return;

        setIsSaving(true);
        await applyOrderUpdate(payload);
        Notification({ message: textService.orders.orderUpdated, type: MSG_ERROR_TYPES.SUCCESS });
        refetchOrder();
      } catch (error) {
        Notification({ message: textService.orders.errorSave, type: MSG_ERROR_TYPES.ERROR });
      }

      setIsSaving(false);
      setEditMode(false);
    }
  };

  const onOrderDetailChange = (newOrderEntity: OrderEntity) => {
    setOrderEntity(newOrderEntity);
  };

  if (!orderEntity || isLoadingOrder) {
    return <ModuleLoaderAppComponent />;
  }

  return (
    <div className='bg-white w-full rounded-2xl shadow-lg p-8 flex flex-col gap-4'>
      <div className="flex justify-between items-center h-12">
        <h2 className="text-lg font-semibold">
          {title} <span className='text-toscaBlue'>#{order?.orderInfo.orderNumber}</span>
        </h2>
        {!upstreamIsFinished && (
          <OrderActionButtonsAppComponent
            isSaving={isSaving}
            onClick={onActionClick}
            disabled={(!orderEntity.isValid()) && editMode}
            type= {!editMode ? 'edit' : 'save'}
          />
        )}
      </div>
      <div className="flex flex-wrap gap-8">
        <div className='flex-1 relative z-10'>
          <OrderDetailContextProvider
            order={orderEntity}
            useFetchCatalog={() => ({ options: [], refetch: () => {}, isLoading: false }) }
            useFetchReceiver={useFetchReceiver}
            useFetchStatuses={useGetAvailableStatuses(order!)}
            editable={editMode}
            onChange={onOrderDetailChange}
            useFetchUsers={useFetchUsers}
          >
            <OrderDetailAppComponent/>
          </OrderDetailContextProvider>
        </div>
      </div>
    </div>
  );
}
