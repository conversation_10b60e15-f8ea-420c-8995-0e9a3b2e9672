import {
  DropdownSimple,
  DropdownWithSearch,
  FormInputType,
  FormLabel,
  IconImporter,
  InputComponent,
  isEmail,
  type OptionsDropdownProps,
} from '@pitsdepot/storybook';
import { useCallback, useMemo, useState } from 'react';

import { Country } from '#application/common/types/Country.Type';
import { UserFormData } from '#application/common/types/UserFormData';
import ApplicationRegistry from '#composition/Application.Registry';
import { UserRole } from '#domain/aggregates/user/User.Entity';

interface InviteUserModuleProps {
  isEditing?: boolean;
  formState: UserFormData;
  text: {
    common: { properties: string };
    validation: { nameRequired: string };
    user: {
      invalidEmail: string;
      phoneRequiredInline: string;
      invalidPhoneNumber: string;
      invitationInfo: string;
    };
  };
  setFormState: React.Dispatch<React.SetStateAction<UserFormData>>;
  setHasError: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  actionButton: () => JSX.Element;
  disabledFields?: Array<keyof UserFormData>;
}

const roleOptions = [
  { id: UserRole.USER, name: 'Usuario' },
  { id: UserRole.ADMIN, name: 'Administrador' },
];

const isFieldDisabled = (fieldName: keyof UserFormData, disabledFields: Array<keyof UserFormData>): boolean => disabledFields.includes(fieldName);

const validatePhoneNumber = (phoneNumber: string) => /^[0-9]{7,15}$/.test(phoneNumber);

export function InviteUserDetailModule({
  isEditing = true,
  formState,
  text,
  setFormState,
  setHasError,
  title,
  actionButton,
  disabledFields = [],
}: InviteUserModuleProps) {
  const { countries, loading: countriesLoading } = ApplicationRegistry.CountriesService.useGetCountries();
  const [dropdownKey, setDropdownKey] = useState(0);
  const [searchText, setSearchText] = useState('');

  const validateForm = useCallback((data: Partial<UserFormData>) => {
    const hasErrors = !data.name
        || !data.lastName
        || !data.email
        || !data.role
        || !data.phoneNumber
        || !data.countryCode
        || (data.email && isEmail(data.email))
        || (data.phoneNumber && !validatePhoneNumber(data.phoneNumber));

    setHasError(Boolean(hasErrors));
    return !hasErrors;
  }, []);

  const handleInputChange = useCallback((fieldName: keyof UserFormData, e: React.ChangeEvent<HTMLInputElement> | React.FormEvent<HTMLSelectElement>) => {
    let value: string | UserRole | null;

    if (fieldName === 'role') {
      value = (e.target as HTMLSelectElement).value as UserRole;
    } else {
      value = (e.target as HTMLInputElement).value;
    }

    if (fieldName === 'phoneNumber') {
      value = value.replace(/[^0-9]/g, '');
    }

    setFormState((prev: UserFormData) => ({ ...prev, [fieldName]: value }));

    validateForm({ ...formState, [fieldName]: value });
  }, [setFormState, validateForm, formState]);

  const handleCountryCodeChange = useCallback((country: OptionsDropdownProps) => {
    setFormState((prev: UserFormData) => ({ ...prev, countryCode: country.id }));
    validateForm({ ...formState, countryCode: country.id });

    setSearchText('');
    setDropdownKey((prev) => prev + 1);
  }, [setFormState, validateForm, formState]);

  const handleSearchChange = useCallback((searchtext: string) => {
    setSearchText(searchtext);
  }, []);

  const countryOptions = useMemo(() => {
    const uniqueCountries = countries.reduce((unique: Country[], country: Country) => {
      if (!unique.find((c) => c.phoneCode === country.phoneCode)) {
        unique.push(country);
      }
      return unique;
    }, []);

    const filteredCountries = uniqueCountries.filter((country: Country) => {
      if (!searchText) return true;
      const searchLower = searchText.toLowerCase();
      return (
        country.name.toLowerCase().includes(searchLower)
        || country.phoneCode.includes(searchText)
        || country.isoCode.toLowerCase().includes(searchLower)
      );
    });

    return filteredCountries.map((country: Country) => ({
      id: country.phoneCode,
      name: `${country.flag} ${country.name} (${country.phoneCode})`,
      src: country.flag,
    }));
  }, [countries, searchText]);

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleInputChange(e.target.name as keyof UserFormData, e);
  };

  return (
    <div>
      <div className='bg-white rounded-2xl w-full p-8 max-h-min shadow-lg flex flex-col gap-6'>
        <div className='flex justify-between'>
          <h2 className="text-lg font-semibold">
            {title}
          </h2>
          <div className='flex items-center gap-4'>
            {actionButton()}
          </div>
        </div>

        <div className="bg-primary border border-primary rounded-lg p-4">
          <div className="flex items-start">
            <IconImporter
              name="info"
              size={20}
              className="text-gray-700 mr-3 mt-0.5 flex-shrink-0"
            />
            <p className="text-gray-700 text-sm">
              {text.user.invitationInfo}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div className='flex flex-col gap-6'>
            <div className='flex flex-col gap-2'>
              <FormLabel title='Nombre'/>
              <InputComponent
                name="name"
                value={formState.name}
                placeholder="Ingrese el nombre"
                onChange={onInputChange}
                disabled={!isEditing || isFieldDisabled('name', disabledFields)}
                isRequired
                />
              {!formState.name && isEditing && <p className='text-negative text-xs'>{text.validation.nameRequired}</p>}
            </div>

            <div className='flex flex-col gap-2'>
              <FormLabel title='Correo electrónico'/>
              <InputComponent
                name="email"
                inputType={FormInputType.Email}
                value={formState.email}
                placeholder="<EMAIL>"
                onChange={onInputChange}
                disabled={!isEditing || isFieldDisabled('email', disabledFields)}
                isRequired
                />
              {(!formState.email || (formState.email && isEmail(formState.email)))
                && isEditing
                && <p className='text-negative text-xs'>{text.user.invalidEmail}</p>}
            </div>

            <div className='flex flex-col gap-2 max-w-40'>
              <FormLabel title='Rol'/>
              <DropdownSimple
                options={roleOptions}
                setSelectedOption={(option) => {
                  const newValue = option.id as UserRole;
                  setFormState((prev: UserFormData) => ({ ...prev, role: newValue }));
                  validateForm({ ...formState, role: newValue });
                }}
                disabled={!isEditing || isFieldDisabled('role', disabledFields)}
                showAvatar={false}
                >
                <div className="flex items-center justify-between cursor-pointer px-3 py-2 border border-gray-300 rounded-md">
                  <span>{roleOptions.find((role) => role.id === formState.role)?.name}</span>
                  <IconImporter name="caretDown" className="ml-2" />
                </div>
              </DropdownSimple>
              {!formState.role && isEditing && <p className='text-negative text-xs'>{text.validation.nameRequired}</p>}
            </div>
          </div>

          <div className='flex flex-col gap-6'>
            <div className='flex flex-col gap-2'>
              <FormLabel title='Apellido'/>
              <InputComponent
                name="lastName"
                value={formState.lastName}
                placeholder="Ingrese el apellido"
                onChange={onInputChange}
                disabled={!isEditing || isFieldDisabled('lastName', disabledFields)}
                isRequired
                />
              {!formState.lastName && isEditing && <p className='text-negative text-xs'>{text.validation.nameRequired}</p>}
            </div>

            <div className='flex flex-col gap-2'>
              <FormLabel title='Teléfono'/>
              <div className='flex gap-2'>
                <div className='w-64 flex flex-col'>
                  <DropdownWithSearch
                    key={dropdownKey}
                    options={countryOptions}
                    setSelectedOption={handleCountryCodeChange}
                    disabled={!isEditing || isFieldDisabled('countryCode', disabledFields) || countriesLoading}
                    showAvatar={false}
                    onSearchChange={handleSearchChange}
                  >
                    <div className="flex items-center justify-between cursor-pointer px-3 py-2 border border-gray-300 rounded-md">
                      <span className="text-sm">
                        {formState.countryCode
                          ? countryOptions.find((country) => country.id === formState.countryCode)?.name || `Pais (${formState.countryCode})`
                          : 'Pais'
                        }
                      </span>
                      <IconImporter name="caretDown" className="ml-2" size={12} />
                    </div>
                  </DropdownWithSearch>
                  {!formState.countryCode && isEditing && (
                    <p className='text-negative text-xs mt-1'>El codigo de pais es requerido</p>
                  )}
                </div>

                <div className='flex-1 flex flex-col'>
                  <InputComponent
                    name="phoneNumber"
                    value={formState.phoneNumber}
                    placeholder="************"
                    onChange={onInputChange}
                    disabled={!isEditing || isFieldDisabled('phoneNumber', disabledFields)}
                    isRequired
                  />
                  {!formState.phoneNumber && isEditing && (
                    <p className='text-negative text-xs mt-1'>{text.user.phoneRequiredInline}</p>
                  )}
                  {formState.phoneNumber && !validatePhoneNumber(formState.phoneNumber) && (
                    <p className='text-negative text-xs mt-1'>{text.user.invalidPhoneNumber}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
