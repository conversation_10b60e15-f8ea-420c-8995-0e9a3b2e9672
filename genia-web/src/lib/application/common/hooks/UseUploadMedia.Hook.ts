import { Notification } from '#appComponent/common/Notification.Component';
import { MediaImage, SignedUrlResult } from '#application/common/services/Media.Service';
import MediaUploadWorker from '#application/common/workers/MediaUpload.Worker?worker';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';

interface UseUploadMediaProps {
    images: MediaImage[];
    entity: string;
    entityId: string;
    token: string | void;
    kind: string;
    onComplete?: () => void;
    signedUrlResults?: SignedUrlResult[];
}

export function UseUploadMedia(props: UseUploadMediaProps) {
  const {
    images, entity, entityId, token, kind, onComplete, signedUrlResults,
  } = props;

  const filesToUpload = images.filter((img): img is File => img instanceof File);

  if (filesToUpload.length === 0) return;

  if (!window.Worker) {
    Notification({
      message: 'Tu navegador no soporta Web Workers, por favor actualiza tu navegador',
      type: MSG_ERROR_TYPES.ERROR,
    });
    return;
  }

  const mediaWorker = new MediaUploadWorker();

  mediaWorker.onmessage = (event) => {
    const {
      success, message, error, fileName, isComplete,
    } = event.data;

    if (fileName) {
      if (success) {
        Notification({
          message: `Archivo ${fileName} cargado correctamente.`,
          type: MSG_ERROR_TYPES.SUCCESS,
        });
      } else {
        Notification({
          message: `Error al subir el archivo ${fileName}: ${error || message}`,
          type: MSG_ERROR_TYPES.ERROR,
        });
      }
    }

    if (isComplete) {
      mediaWorker.terminate();
      if (onComplete) {
        onComplete();
      }
    }
  };

  mediaWorker.postMessage({
    kind,
    filesToUpload,
    entity,
    entityId,
    token,
    getSignedUrlEndpoint: ApplicationRegistry.PathService.signedUrl.base(),
    postMediaEndpoint: ApplicationRegistry.PathService.media.base(),
    signedUrlResults,
  });
}
