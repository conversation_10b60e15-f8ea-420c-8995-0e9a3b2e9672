import { useEffect, useState } from 'react';

export const useInactivity = (timeout: number) => {
  const [isInactive, setIsInactive] = useState(false);

  useEffect(() => {
    let lastActivity = Date.now();

    const handleActivity = () => {
      lastActivity = Date.now();
      if (isInactive) {
        setIsInactive(false);
      }
    };

    const checkInactivity = () => {
      if (Date.now() - lastActivity > timeout) {
        setIsInactive(true);
      }
    };

    const events = ['mousemove', 'keydown', 'click', 'scroll'];
    events.forEach((event) => window.addEventListener(event, handleActivity));

    const interval = setInterval(checkInactivity, 1000);

    return () => {
      events.forEach((event) => window.removeEventListener(event, handleActivity));
      clearInterval(interval);
    };
  }, [isInactive, timeout]);

  return isInactive;
};
