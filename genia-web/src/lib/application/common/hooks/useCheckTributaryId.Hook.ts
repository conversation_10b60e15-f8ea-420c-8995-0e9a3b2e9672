import { gql, useLazyQuery } from '@apollo/client';
import { useEffect, useMemo } from 'react';

import { useDebounce } from '#appComponent/common/hooks/useDebounce.Hook';

const CHECK_TRIBUTARY_ID_EXISTS = gql`
  query CheckTributaryIdExists($tributaryId: String!) {
    company_aggregate(where: { tributaryId: { _eq: $tributaryId } }) {
      aggregate {
        count
      }
    }
  }
`;

export interface TributaryIdValidationResult {
  exists: boolean;
}

export const useCheckTributaryId = (tributaryId: string) => {
  const { debouncedValue } = useDebounce(tributaryId, { delay: 800 });

  const [checkTributaryId, {
    data, loading, error, refetch,
  }] = useLazyQuery(CHECK_TRIBUTARY_ID_EXISTS, {
    variables: {
      tributaryId: debouncedValue?.trim(),
    },
    errorPolicy: 'all',
  });

  useEffect(() => {
    if (debouncedValue && debouncedValue.trim().length >= 3) {
      checkTributaryId();
    }
  }, [debouncedValue, checkTributaryId]);

  const validationResult: TributaryIdValidationResult | null = useMemo(() => {
    if (debouncedValue && debouncedValue.trim().length >= 3 && data !== undefined) {
      return {
        exists: (data?.company_aggregate?.aggregate?.count || 0) > 0,
      };
    }
    return null;
  }, [debouncedValue, data]);

  return {
    validationResult,
    isValidating: loading,
    validationError: error?.message || null,
    refetch,
  };
};
