export default interface PathService {
  useGoToPath: () => (path: string) => void,
  saleOrders: {
    viewSaleOrder: (id: string, includePrefix?: boolean) => string;
    addSaleOrder: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  }
  purchaseOrders: {
    viewPurchaseOrder: (id: string, includePrefix?: boolean) => string;
    addPurchaseOrder: (includePrefix?: boolean) => string;
    addPurchaseOrderFromExternal: (from: string, includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  }
  clients: {
    viewClient: (id: string, includePrefix?: boolean) => string;
    addClient: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  }
  providers: {
    viewProvider: (id: string, includePrefix?: boolean) => string;
    addProvider: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
    viewProviderCatalog: (id: string, includePrefix?: boolean) => string;
    providerCatalogBase: (includePrefix?: boolean) => string;
  }
  inventory: {
    viewInventory: (id: string, includePrefix?: boolean) => string;
    addInventory: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  }
  catalog: {
    viewCatalog: (id: string, includePrefix?: boolean) => string;
    addCatalog: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  }
  catalogDiscounts: {
    viewCatalogDiscount: (id: string, includePrefix?: boolean) => string;
    addCatalogDiscount: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  },
  storeDiscounts: {
    viewStoreDiscount: (id: string, includePrefix?: boolean) => string;
    addStoreDiscount: (includePrefix?: boolean) => string;
    base: (includePrefix?: boolean) => string;
  },
  store: {
    base: (includePrefix?: boolean) => string;
  },
  claudia: {
    base: (includePrefix?: boolean) => string;
  },
  users: {
    base: (includePrefix?: boolean) => string;
    viewUser: (id: string, includePrefix?: boolean) => string;
    addUser: (includePrefix?: boolean) => string;
  },
  company: {
    base: (includePrefix?: boolean) => string;
  },
  signedUrl: {
    base: () => string;
  },
  media: {
    base: () => string;
  },
  dashboard: {
    base: (includePrefix?: boolean) => string;
  },
  settings: {
    base: (includePrefix?: boolean) => string;
  },
}
