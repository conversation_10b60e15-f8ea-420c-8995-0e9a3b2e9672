export interface UploadResult {
  success: boolean;
  fileName: string;
  status?: number;
  error?: string;
}

export interface SignedUrlResponse {
  signedUrl: string;
  gcsPath: string;
  fullGcsUri: string;
}

export interface SignedUrlResult {
  success: boolean;
  fileName: string;
  fileObject: File;
  data?: SignedUrlResponse;
  status?: number;
  error?: string;
  message?: string;
}

export type MediaImage = File | { id: string; url: string; name: string };

export type MediaService = {
  getSignedUrlsInParallel: (props: {
    filesToUpload: MediaImage[];
    kind: string;
    entity: string;
    entityId: string;
    getSignedUrlEndpoint: string;
    token: string | null | undefined;
  }) => Promise<SignedUrlResult[]>;
  assignMediaToEntity: (
    entity: string,
    entityId: string,
    urlsArray: { url: string | undefined }[],
    token: string | null | undefined,
    postMediaEndpoint: string,
  ) => Promise<Response>;
  deleteMedia: (
    mediaIds: string[],
    entity: string,
    entityId: string,
    token: string | null | undefined,
  ) => Promise<Response>;
};
