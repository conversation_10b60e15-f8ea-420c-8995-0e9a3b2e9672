import {
  Button,
  CartCardSkeleton,
  IconImporter, Pagination, SearchBox,
} from '@pitsdepot/storybook';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import noImage from '#/assets/no-image.png';
import { Notification } from '#/lib/appComponent/common/Notification.Component';
import {
  DropDownSearchAppComponent,
  DropDownSearchAppComponentOption,
} from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import StoreCard from '#appComponent/common/StoreCard.Component';
import { StoreItem } from '#application/store/Store.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { PurchaseOrderItem } from '#infrastructure/api/http/OrderConditions.Http';
import { useActiveProviderCart } from '#infrastructure/implementation/application/hooks/useActiveProviderCart.Hook';
import { usePagination } from '#infrastructure/implementation/application/hooks/usePagination.Hook';
import useGetPurchaseOrderConditions from '#infrastructure/implementation/application/store/UseGetPurchaseOrderConditions';
import useGetStoreItems from '#infrastructure/implementation/application/store/UseGetStoreItems';
import { compareTwoArraysOfObjects } from '#infrastructure/implementation/application/utils/compareTwoArraysOfObjects';
import { tableTotalPages } from '#infrastructure/implementation/application/utils/TableTotalPages';
import { StoreProviderContext } from '#infrastructure/pages/app/pageContexts/StoreProvider.Context';

const itemsPerPage = 15;
const orderBy = 'createdAt';
const order = 'desc';

const text = TextService.getText();

export function StoreModule() {
  const [searchInput, setSearchInput] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [orderItems, setOrderItems] = useState<PurchaseOrderItem[]>([]);
  const [hasItemAdded, setHasItemAdded] = useState<boolean>(false);
  const [hasPriceChanged, setHasPriceChanged] = useState<boolean>(false);
  const [hasQuantityChanged, setHasQuantityChanged] = useState<boolean>(false);
  const [globalLoading, setGlobalLoading] = useState<boolean>(true);
  const [selectedProvider, setSelectedProvider] = useState<DropDownSearchAppComponentOption | null>(null);
  const [providerSearchText, setProviderSearchText] = useState<string>('');

  const {
    items: itemsContext,
    isCartOpen,
    cartFunctions,
    activeProviderId,
  } = useActiveProviderCart();

  const {
    addItem, updateItemPrices, updateSummary, toggleLoading, setActiveProvider,
  } = cartFunctions;

  const {
    currentPage, handlePageChange, setCurrentPage,
  } = usePagination(itemsPerPage);

  const { storeProvider: { id: providerId }, setStoreProvider } = useContext(StoreProviderContext);

  const {
    items: providers = [],
    loading: providersLoading,
    refetch: refetchProviders,
  } = ApplicationRegistry.ProviderService.useGetProvidersWithCompanyId({
    limit: 20,
    offset: 0,
    searchTerm: providerSearchText,
    orderBy: 'name',
    order: 'asc',
  });

  const providerOptions = useMemo(
    () => providers.map((provider) => ({
      id: provider.id,
      name: provider.name,
    })),
    [providers],
  );

  useEffect(() => {
    if (providers.length > 0 && !selectedProvider) {
      const firstProvider = {
        id: providers[0].id,
        name: providers[0].name,
      };

      setSelectedProvider(firstProvider);
      setStoreProvider(providers[0]); // Update the context with the full provider data
      setActiveProvider(providers[0].id); // Set as active provider in cart
      setCurrentPage(1);
    }
  }, [providers, selectedProvider, setCurrentPage, setStoreProvider, setActiveProvider]);

  useEffect(() => {
    if (selectedProvider) {
      setCurrentPage(1);
    }
  }, [selectedProvider?.id, setCurrentPage]);

  // Effect to sync cart state when active provider changes
  useEffect(() => {
    if (activeProviderId && itemsContext.length > 0) {
      // If the active provider has items, sync the order items and trigger price calculation
      const updatedItems = itemsContext.map((it) => ({ referenceId: it.referenceId, quantity: it.quantity }));
      setOrderItems(updatedItems);
      setHasItemAdded(true); // This will trigger price calculation
    } else if (activeProviderId && itemsContext.length === 0) {
      // If switching to a provider with no items, reset the local state
      setHasItemAdded(false);
      setHasPriceChanged(false);
      setHasQuantityChanged(false);
      setOrderItems([]);
      toggleLoading(false);
    }
  }, [activeProviderId, JSON.stringify(itemsContext), toggleLoading]);

  const providersDropdownFetch = useCallback((params: { searchText: string }) => {
    const { searchText } = params;

    return {
      options: providerOptions,
      refetch: () => {
        if (searchText !== providerSearchText) {
          setProviderSearchText(searchText);
        }
      },
      isLoading: providersLoading,
    };
  }, [providerOptions, providersLoading, providerSearchText]);

  useEffect(() => {
    refetchProviders();
  }, [providerSearchText, refetchProviders]);

  const onProviderChange = useCallback((provider: DropDownSearchAppComponentOption) => {
    setSelectedProvider(provider);
    setCurrentPage(1);

    // Find the full provider data from the providers list
    const fullProvider = providers.find((p) => p.id === provider.id);
    if (fullProvider) {
      setStoreProvider(fullProvider);
      // Set the active provider in the cart context
      setActiveProvider(provider.id);
    }
  }, [setCurrentPage, providers, setStoreProvider, setActiveProvider]);

  const {
    items, totalNumberOfItems, error, loading,
  } = useGetStoreItems({
    searchTerm: searchValue,
    pageSize: itemsPerPage,
    page: currentPage,
    orderBy,
    order,
    providers: selectedProvider?.id || providerId,
  });

  useEffect(() => {
    if (providerOptions.length === 0 && globalLoading) {
      setGlobalLoading(false);
    } else {
      const isLoading = loading;
      if (isLoading !== globalLoading) {
        setGlobalLoading(isLoading);
      }
    }
  }, [loading, providerOptions.length]);

  const { orderDetails } = useGetPurchaseOrderConditions({
    providerId: selectedProvider?.id || providerId,
    orderItems,
    isUpdate: hasQuantityChanged,
  });

  useEffect(() => {
    if (itemsContext.length === 0 && hasItemAdded) {
      // Only reset if we previously had items added (not when switching providers)
      setHasItemAdded(false);
      setHasPriceChanged(false);
      setHasQuantityChanged(false);
      setOrderItems([]);
      toggleLoading(false);
    }
  }, [JSON.stringify(itemsContext), hasItemAdded, toggleLoading]);

  useEffect(() => {
    if (orderDetails?.orderItems) {
      const testPriceChange = compareTwoArraysOfObjects(orderDetails.orderItems, itemsContext, 'unitPrice');
      toggleLoading(false);
      setHasPriceChanged(testPriceChange);
    }
  }, [orderDetails?.orderItems]);

  useEffect(() => {
    const testQuantityChange = compareTwoArraysOfObjects(itemsContext, orderItems, 'quantity');
    setHasQuantityChanged(testQuantityChange);
    if (testQuantityChange) {
      toggleLoading(true);
    }
  }, [JSON.stringify(itemsContext)]);

  useEffect(() => {
    if (hasItemAdded || hasQuantityChanged) {
      const updatedItems = itemsContext.map((it) => ({ referenceId: it.referenceId, quantity: it.quantity }));
      setOrderItems(updatedItems);
    }
  }, [hasItemAdded, hasQuantityChanged, JSON.stringify(itemsContext)]);

  useEffect(() => {
    if (hasPriceChanged || hasItemAdded || hasQuantityChanged) {
      orderDetails?.orderItems?.map((it) => updateItemPrices({
        referenceId: it.referenceId,
        unitPrice: it.unitPrice,
        unitPriceAfterDiscount: it.unitPriceAfterDiscount,
        subtotal: it.subtotal,
        appliedDiscount: it.discounts.appliedDiscount,
      }));
    }
  }, [JSON.stringify(orderDetails?.orderItems), hasPriceChanged, hasItemAdded, hasQuantityChanged]);

  useEffect(() => {
    if (hasQuantityChanged || hasItemAdded) {
      updateSummary({
        subtotal: orderDetails?.subtotal || 0,
        total: orderDetails?.total || 0,
        totalDiscount: orderDetails?.totalDiscount,
        totalTaxes: orderDetails?.totalTaxes,
        subtotalBeforeDiscount: orderDetails?.subtotalBeforeDiscount || 0,
      });
    }
  }, [JSON.stringify(orderDetails), hasQuantityChanged, hasItemAdded]);

  useEffect(() => {
    if (error) {
      Notification({ message: text.common.loadingErrorNotification, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [error]);

  useEffect(() => {
    if (searchValue) {
      setCurrentPage(1);
    }
  }, [searchValue]);

  const isButtonDisabled = useCallback((referenceId: string) => itemsContext.some((it) => it.referenceId === referenceId), [itemsContext]);

  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value);
    if (value.trim() === '') {
      setSearchValue('');
    }
  }, []);

  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const trimmedInput = searchInput.trim();

      if (trimmedInput.length < 1) {
        Notification({ message: text.common.minimumSearchLength, type: MSG_ERROR_TYPES.WARNING });
        return;
      }
      setSearchValue(trimmedInput);
    }
  }, [searchInput]);

  const handleAddItemToCart = useCallback((e: React.MouseEvent, item: StoreItem) => {
    e.preventDefault();
    toggleLoading(true);
    setHasItemAdded(true);

    addItem({
      referenceId: item.id,
      sku: item.readId,
      name: item.name,
      applicableDiscounts: item.applicableDiscounts,
      appliedDiscount: item.appliedDiscount,
      quantity: 1,
      imageUrl: item.media?.[0]?.url || noImage,
      unitPrice: 0,
      unitPriceAfterDiscount: 0,
      subtotal: 0,
      providerId: selectedProvider?.id || providerId, // Add providerId to the item
    });
  }, [addItem, selectedProvider?.id, providerId]);

  const renderAddToCartButton = useCallback((item: StoreItem, onAddToCart: (e: React.MouseEvent, item: StoreItem) => void, isDisabled: boolean) => (
    <Button
      variant='outlined'
      onClick={(e) => onAddToCart(e, item)}
      size='small'
      // eslint-disable-next-line max-len
      className='!p-2 !text-dark-700 !border-dark-700 hover:!bg-white hover:!text-primary hover:!border-primary disabled:!bg-dark-200 disabled:hover:!text-dark-700 disabled:hover:!border-dark-700'
      disabled={isDisabled}
    >
      <IconImporter size={18} name='shoppingCart' />
    </Button>
  ), [addItem, hasItemAdded]);

  const searchBox = useMemo(() => ({
    placeholder: 'Buscar',
    pressEnterLabel: 'Enter para buscar',
    value: searchInput,
    onValueChange: handleInputChange,
    onKeyDown: handleKeyDown,
    className: '!bg-white !max-w-[450px] w-full',
    searchValue,
  }), [searchInput, handleInputChange, handleKeyDown, searchValue]);

  const providerDropdown = useMemo(() => {
    if (providerOptions.length === 0) return null;
    const displayProvider = selectedProvider || { id: '', name: text.providers.providers };

    return (
      <DropDownSearchAppComponent
        key={`provider-dropdown-${displayProvider.id}`}
        selected={displayProvider}
        className='text-sm !min-w-[250px]'
        useFetch={providersDropdownFetch}
        onSelect={onProviderChange}
        disabled={false}
        position="bottom-right"
        />
    );
  }, [
    selectedProvider,
    text.providers.providers,
    providersDropdownFetch,
    onProviderChange,
    providerOptions.length,
  ]);

  const noMatchesFound = useMemo(() => items?.length === 0 && searchValue !== '' && (
    <div className='flex flex-col gap-4 items-center mx-auto mt-16'>
      <p className='text-2xl text-center'>{text.common.noMatchesFoundHeader}<span className='italic font-light'>{`"${searchValue}".`}</span></p>
      <p className='text-dark-500'>{text.common.noMatchesFoundBody}</p>
    </div>
  ), [items, searchValue]);

  // eslint-disable-next-line max-len
  const cardClassName = useMemo(() => `p-2 sm:pd-min-w-full sm:pd-max-w-full ${isCartOpen ? 'lg:min-w-middle lg:max-w-middle xl:min-w-third xl:max-w-third 2xl:max-w-quarter desktop:max-w-fifth 2xl:min-w-quarter desktop:min-w-fifth' : 'md:max-w-middle md:min-w-middle lg:min-w-third lg:max-w-third xl:min-w-quarter xl:max-w-quarter 2xl:min-w-fifth 2xl:max-w-fifth'}`, [isCartOpen]);

  const renderSkeleton = useMemo(() => Array.from({ length: Math.min(itemsPerPage) }, (_, index) => (
    <CartCardSkeleton key={`skeleton-${index}`} className={`h-[406px] ${cardClassName}`} />
  )), [itemsPerPage, cardClassName]);

  const renderItems = useCallback(() => items?.map((item) => {
    const {
      id,
    } = item;

    return (
      <StoreCard
        key={id}
        item={item}
        className={cardClassName}
        actionButton={renderAddToCartButton(item, handleAddItemToCart, isButtonDisabled(id))}
      />
    );
  }), [items, cardClassName, renderAddToCartButton, handleAddItemToCart, isButtonDisabled]);

  return (
    <div className='flex flex-col w-full pl-7 pt-0 gap-8'>
      <div className='flex justify-end items-center gap-4 w-full'>
        <div className='flex items-center gap-4'>
          {providerDropdown}
        </div>
        <div className='flex justify-center items-center gap-4 flex-1'>
          <SearchBox {...searchBox} />
        </div>
      </div>
      { !providersLoading && providerOptions.length === 0 && (
        <div className='flex flex-col items-center justify-center w-full mt-24'>
          <p className='text-2xl font-semibold text-center mb-2'>
            {text.providers.noProvidersTitle}
          </p>
          <p className='text-dark-500 text-center mb-4 max-w-lg'>
            {text.providers.noProvidersBody}
          </p>
        </div>
      )}
      { !globalLoading && providerOptions.length > 0 && items?.length === 0 && searchValue === '' && (
        <div className='flex flex-col items-center justify-center w-full mt-24'>
          <p className='text-2xl font-semibold text-center mb-2'>
            {text.providers.noProductsTitle}
          </p>
          <p className='text-dark-500 text-center mb-4 max-w-lg'>
            {text.providers.noProductsBody}
          </p>
        </div>
      )}
      <div className='flex flex-wrap'>
        {globalLoading && providerOptions.length > 0
          ? renderSkeleton
          : renderItems()}
        {noMatchesFound}
      </div>
      {items && items.length > 0 && (
        <Pagination
          initialPage={currentPage}
          totalPages={tableTotalPages(totalNumberOfItems, itemsPerPage)}
          onPageChange={handlePageChange}
          isLoading={loading}
          className='!px-4 mt-2 mb-4'
        />
      )}
    </div>
  );
}
