import { CatalogItem, Media } from '#application/deprecated/DashboardPages.Type';
import { CatalogDiscount } from '#application/discount/CatalogDiscount.Type';

import { StoreDiscount } from '../discount/StoreDiscount.Type';

export interface GetStoreItemsProps {
  pageSize?: number;
  page?: number;
  searchTerm?: string;
  orderBy?: string;
  order?: string;
  providers?: string;
}

export interface StoreCatalogDiscount extends Omit<CatalogDiscount, 'catalog_discount_clients'> {
  priceAfterDiscount: number;
}

export interface StoreStoreDiscount extends Omit<StoreDiscount, 'storeDiscountClients' | 'disabledAt'> {
  priceAfterDiscount: number;
}

export interface AppliedDiscount {
  catalogDiscount: StoreCatalogDiscount | null;
  storeDiscount: StoreStoreDiscount | null;
}

export interface ApplicableDiscounts {
  catalogDiscounts: StoreCatalogDiscount[];
  storeDiscounts: StoreStoreDiscount[];
}

export interface StoreProvider {
  id: string;
  name: string;
  companyId: string;
}

export interface StoreItem extends Omit<CatalogItem, 'stock' | 'requiresStock' | 'disabledAt' | 'catalog_media'> {
  provider: StoreProvider;
  media: (Media & { id: string })[];
  appliedDiscount: AppliedDiscount;
  applicableDiscounts: ApplicableDiscounts;
}

export interface Taxes {
  id: string;
  name: string;
  value: number;
  type: string;
  amount: 'amount' | 'percentage';
}

export interface OrderItem {
  referenceId: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  discounts: {
    appliedDiscount: AppliedDiscount;
    applicableDiscounts: ApplicableDiscounts;
  }
  taxes: Taxes[];
  subtotal: number;
  total: number;
}

export interface PurchaseOrderConditions {
  subtotalBeforeDiscount: number;
  providerId: string;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  total: number;
  taxes: Taxes[];
  orderItems: OrderItem[];
}
