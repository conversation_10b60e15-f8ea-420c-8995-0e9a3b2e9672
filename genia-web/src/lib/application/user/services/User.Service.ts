import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { CurrentUser, User } from '#application/user/User.Type';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import { CreateUserPayload, UpdateUserPayload } from '#infrastructure/api/http/Users.Http';

export interface UserService {
  useGetUsers: (props: GetTableDataProps) => GetTableDataResponse<User>;
  useGetUser: (id: string) => {
    user: User | null;
    loading: boolean;
    error: Error | null;
    refetch: () => void;
  };
  useGetCurrentUser: () => {
    user: CurrentUser | null;
    loading: boolean;
    error: Error | null;
    refetch: () => void;
  };
  useCreateUser: () => {
    createUser: (payload: CreateUserPayload[]) => Promise<User[]>;
  };
  useUpdateUser: () => {
    updateUser: (id: string, payload: UpdateUserPayload) => Promise<User>;
  };
  useUpdateUserState: () => {
    updateUserState: (userId: string, state: 'ACTIVE' | 'DISABLED') => Promise<void>;
  };
  useUpdateUserRole: () => {
    updateUserRole: (userId: string, role: UserRole) => Promise<void>;
  };
  useRefetchUsers: () => {
    refetchUsers: () => void;
  };
}
