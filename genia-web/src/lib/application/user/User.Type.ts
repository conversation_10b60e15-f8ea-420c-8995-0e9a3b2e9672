import { UserRole } from '#domain/aggregates/user/User.Entity';

export interface User {
  id: string;
  name: string;
  lastName: string;
  email: string;
  appPhoneNumber?: string;
  disabledAt?: Date | null;
  state?: 'ACTIVE' | 'DISABLED';
  userCompanies?: {
    company: {
      id: string;
      name: string;
    };
    role: UserRole;
  }[];
}

export interface CurrentUser {
  id: number;
  name: string;
  email: string;
  picture?: string;
  role?: string;
  companyId?: string;
  createdAt?: string;
  updatedAt?: string;
}
