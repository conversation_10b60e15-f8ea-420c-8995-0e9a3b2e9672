import { Column, Row } from '@pitsdepot/storybook';
import { useState } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { UserRoleDropdownAppComponent } from '#appComponent/table/UserRoleDropdown.AppComponent';
import { UserStatusSwitchAppComponent } from '#appComponent/table/UserStatusSwitch.AppComponent';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { User } from '#application/user/User.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import './UserList.styles.css';

export function UsersListModule() {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const text = TextService.getText();

  const { updateUserState } = ApplicationRegistry.UsersService.useUpdateUserState();
  const { updateUserRole } = ApplicationRegistry.UsersService.useUpdateUserRole();
  const { refetchUsers } = ApplicationRegistry.UsersService.useRefetchUsers();

  const handleStatusChange = async (userId: string, newState: 'ACTIVE' | 'DISABLED') => {
    setLoadingStates((prev) => ({ ...prev, [`status_${userId}`]: true }));

    try {
      await updateUserState(userId, newState);
      Notification({
        message: newState === 'ACTIVE' ? text.user.userActivatedSuccess : text.user.userDeactivatedSuccess,
        type: MSG_ERROR_TYPES.SUCCESS,
      });
      // Refrescar la lista de usuarios
      await refetchUsers();
    } catch (error) {
      Notification({
        message: text.user.errorChangingUserStatus,
        type: MSG_ERROR_TYPES.ERROR,
      });
    } finally {
      setLoadingStates((prev) => ({ ...prev, [`status_${userId}`]: false }));
    }
  };

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    setLoadingStates((prev) => ({ ...prev, [`role_${userId}`]: true }));

    try {
      await updateUserRole(userId, newRole);
      Notification({
        message: text.user.roleUpdatedSuccess,
        type: MSG_ERROR_TYPES.SUCCESS,
      });
      // Refrescar la lista de usuarios
      await refetchUsers();
    } catch (error) {
      Notification({
        message: text.user.errorChangingUserRole,
        type: MSG_ERROR_TYPES.ERROR,
      });
    } finally {
      setLoadingStates((prev) => ({ ...prev, [`role_${userId}`]: false }));
    }
  };

  const columns: Column[] = [
    {
      header: 'Nombre',
      dataKey: 'fullName',
      width: '25%',
    },
    {
      header: 'Email',
      dataKey: 'email',
      width: '20%',
    },
    {
      header: 'Telefono',
      dataKey: 'appPhoneNumber',
      width: '15%',
    },
    {
      header: 'Rol',
      dataKey: 'role',
      width: '25%',
      renderer: ({ value, id }) => (
        <div className="flex items-center justify-center w-full px-1">
          <UserRoleDropdownAppComponent
            isLoading={loadingStates[`role_${id}`] || false}
            currentRole={value || UserRole.USER}
            onRoleChange={(newRole) => handleRoleChange(id, newRole)}
          />
        </div>
      ),
    },
    {
      header: 'Estado',
      dataKey: 'userStatus',
      width: '15%',
      renderer: ({ value, id }) => (
        <div className="flex items-center justify-center">
          <UserStatusSwitchAppComponent
            userId={id}
            currentState={value || 'DISABLED'}
            isLoading={loadingStates[`status_${id}`] || false}
            onStatusChange={handleStatusChange}
          />
        </div>
      ),
    },
  ];

  function mapper(items: User[]): Row[] {
    if (!items || !Array.isArray(items)) {
      return [];
    }

    return items
      .filter((item) => item && item.id)
      .map((item) => ({
        id: item.id,
        fullName: `${item.name || ''} ${item.lastName || ''}`.trim() || 'Sin nombre',
        email: item.email || '-',
        appPhoneNumber: item.appPhoneNumber || '-',
        role: {
          value: item.userCompanies?.[0]?.role || UserRole.USER,
          id: item.id,
        },
        status: false,
        userStatus: {
          value: item.state || 'ACTIVE',
          id: item.id,
        },
      }));
  }

  return (
    <PaginatedListAppComponent
      title='Usuarios'
      columns={columns}
      useGetHook={ApplicationRegistry.UsersService.useGetUsers}
      mapper={mapper}
      showHeader={false}
      className="user-table"
    />
  );
}
