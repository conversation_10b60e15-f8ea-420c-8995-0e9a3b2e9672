import {
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';

import { Notification } from '#appComponent/common/Notification.Component';
import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import { InviteUserDetailModule } from '#application/common/modules/InviteUserDetail.Module';
import { UserFormData, userFormInitialState } from '#application/common/types/UserFormData';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

export function InviteUserModule() {
  const navigate = useNavigate();

  const [formState, setFormState] = useState<UserFormData>(userFormInitialState);
  const [hasError, setHasError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const usersPath = ApplicationRegistry.PathService.users.base();
  const { createUser } = ApplicationRegistry.UsersService.useCreateUser();

  const text = TextService.getText();

  const handleSubmit = async () => {
    setIsLoading(true);
    if (hasError) {
      Notification({ message: text.validation.nameRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsLoading(false);
      return;
    }

    if (!formState.phoneNumber?.trim()) {
      Notification({ message: text.user.phoneRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsLoading(false);
      return;
    }

    try {
      const fullPhoneNumber = `${formState.countryCode}${formState.phoneNumber?.trim()}`;
      const createPayload = [{
        name: formState.name?.trim(),
        lastName: formState.lastName?.trim(),
        email: formState.email?.trim(),
        phoneNumber: fullPhoneNumber,
        role: formState.role,
      }];
      await createUser(createPayload);
      Notification({ message: text.user.userInvitedSuccess, type: MSG_ERROR_TYPES.SUCCESS });
      navigate(usersPath);
    } catch (err: unknown) {
      Notification({ message: (err as Error).message, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsLoading(false);
    }
  };

  const actionButton = () => (
    <OrderActionButtonsAppComponent
      type='save'
      onClick={handleSubmit}
      isSaving={isLoading}
      disabled={isLoading || hasError || JSON.stringify(formState) === JSON.stringify(userFormInitialState)}
    />
  );

  return (
    <InviteUserDetailModule
      formState={formState}
      setFormState={setFormState}
      text={text}
      setHasError={setHasError}
      title={TextService.getText().user.new}
      actionButton={actionButton}
    />
  );
}
