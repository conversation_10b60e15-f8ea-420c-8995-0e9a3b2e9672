import {
  useEffect,
  useState,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Notification } from '#appComponent/common/Notification.Component';
import { OrderActionButtonsAppComponent } from '#appComponent/common/orders/actionButtons/OrderActionButtons.AppComponent';
import { InviteUserDetailModule } from '#application/common/modules/InviteUserDetail.Module';
import { UserFormData, userFormInitialState } from '#application/common/types/UserFormData';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { UserRole } from '#domain/aggregates/user/User.Entity';

export function UpdateUserModule() {
  const { id } = useParams();
  const navigate = useNavigate();

  const [isEditing, setIsEditing] = useState<boolean>(!id);
  const [formState, setFormState] = useState<UserFormData>(userFormInitialState);
  const [hasError, setHasError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const usersPath = ApplicationRegistry.PathService.users.base();
  const { user: userData, loading: userLoading, error: userError } = ApplicationRegistry.UsersService.useGetUser(id || '');
  const { updateUser } = ApplicationRegistry.UsersService.useUpdateUser();

  const text = TextService.getText();

  useEffect(() => {
    if (id && userData && !userLoading) {
      const userFormData: UserFormData = {
        name: userData.name,
        lastName: userData.lastName,
        email: userData.email,
        phoneNumber: userData.appPhoneNumber || '',
        countryCode: '',
        role: userData.userCompanies?.[0]?.role || UserRole.USER,
        disabledAt: userData.disabledAt ? new Date(userData.disabledAt) : null,
      };
      setFormState(userFormData);
      setIsEditing(false);
    }
  }, [id, JSON.stringify(userData), userLoading]);

  useEffect(() => {
    if (userError) {
      Notification({
        message: text.user.errorLoadingUser,
        type: MSG_ERROR_TYPES.ERROR,
      });
      navigate(usersPath);
    }
  }, [userError, navigate, usersPath]);

  const handleSubmit = async () => {
    setIsLoading(true);
    if (hasError) {
      Notification({ message: text.validation.nameRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsLoading(false);
      return;
    }

    // Validacion adicional para el telefono
    if (!formState.phoneNumber?.trim()) {
      Notification({ message: text.user.phoneRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsLoading(false);
      return;
    }

    try {
      if (id) {
        const updatePayload = {
          name: formState.name?.trim(),
          lastName: formState.lastName?.trim(),
          phoneNumber: formState.phoneNumber?.trim(),
          role: formState.role,
          disabledAt: formState.disabledAt,
        };

        await updateUser(id, updatePayload);
        Notification({ message: text.user.userUpdatedSuccess, type: MSG_ERROR_TYPES.SUCCESS });
      }
      navigate(usersPath);
    } catch (err: unknown) {
      Notification({ message: (err as Error).message, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsLoading(false);
    }
  };

  const handleActionButtonClick = (currentType: 'edit' | 'save') => {
    if (currentType === 'save') {
      handleSubmit();
    } else {
      setIsEditing(true);
    }
  };

  const actionButton = () => {
    const type = isEditing ? 'save' : 'edit';
    const disabled = isLoading || (type === 'save' && hasError);

    return (
      <OrderActionButtonsAppComponent
        type={type}
        onClick={() => handleActionButtonClick(type)}
        isSaving={isLoading}
        disabled={disabled}
        />
    );
  };

  if (userLoading || isLoading) {
    return (
      <div className='flex flex-col w-full gap-4 p-7'>
        <div className='flex justify-center items-center h-64'>
          <div className='text-lg text-gray-600'>{text.user.loadingUserData}</div>
        </div>
      </div>
    );
  }

  return (
    <InviteUserDetailModule
      isEditing={isEditing}
      formState={formState}
      setFormState={setFormState}
      text={text}
      setHasError={setHasError}
      title={TextService.getText().user.edit}
      actionButton={actionButton}
      disabledFields={['email']}
    />
  );
}
