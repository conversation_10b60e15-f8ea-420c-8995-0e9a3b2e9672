import { Column, Row } from '@pitsdepot/storybook';

import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import { AnchorCell, TributaryIdCell } from '#appComponent/table/TableCells.Component';
import { Client } from '#application/client/Client.Type';
import ApplicationRegistry from '#composition/Application.Registry';

const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: 'name',
    width: '40%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Id tributario',
    dataKey: 'tributaryId',
    renderer: TributaryIdCell,
  },
];

function mapper(items: Client[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    name: { value: item.name, url: ApplicationRegistry.PathService.clients.viewClient(item.id) },
    tributaryId: item.tributaryId,
  }));
}

function ClientsListModule() {
  return (
    <PaginatedListAppComponent
      title={'Clientes'}
      columns={columns}
      useGetHook={ApplicationRegistry.ClientService.useGetClients}
      mapper={mapper}
      showHeader={false}
    />
  );
}

export default ClientsListModule;
