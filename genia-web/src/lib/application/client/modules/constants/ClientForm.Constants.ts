import { Client } from '#application/client/Client.Type';

export const clientFormInitialState: Partial<Client> = {
  id: '',
  name: '',
  tributaryId: '',
  client_company: null,
};

export const TITLE = 'Cliente';
export const NEW_CLIENT_TITLE = 'Nuevo Cliente';
export const EDIT_CLIENT_TITLE = 'Editar Cliente';
export const PROPERTIES_LABEL = 'Propiedades';
export const SAVE_BTN_LABEL = 'Guardar';

export const CLIENT_ADD_SUCCES = 'Cliente agregado con éxito';
export const CLIENT_ADD_FAILED = 'Error al agregar el cliente';
export const CLIENT_UPDATE_SUCCES = 'Cliente actualizado con éxito';
export const CLIENT_UPDATE_FAILED = 'Error al actualizar el cliente';

export const STORE_DISCOUNT_LIST_TOOLTIP = {
  // eslint-disable-next-line max-len
  content: 'Esta es la lista de descuentos de tienda que se han creado para este cliente. Puedes ver los detalles de cada descuento haciendo clic en el nombre del descuento.',
};

export const LABELS = {
  NAME: 'Nombre',
  TRIBUTARY_ID: 'ID Tributario',
  MEDIA: 'Agregar Imagen',
};

export const INPUT_NAME = {
  NAME: 'name',
  TRIBUTARY_ID: 'tributaryId',
  MEDIA: 'image',
};

export const PLACEHOLDERS = {
  NAME: 'Ejemplo: Cliente marca Acme',
  TRIBUTARY_ID: 'Ejemplo: XAXX010101000',
};

export const TOOLTIPS = {
  NAME: 'Ingresa el nombre de tu cliente. Ejemplo: Cliente marca Acme',
  TRIBUTARY_ID: 'Ingresa el Identificador Tributario de tu cliente de acuerdo a las leyes fiscales de tu país. Ejemplo: XAXX010101000',
  MEDIA: 'Sube una imagen clara y representativa de la identidad gráfica de tu cliente. Funcionalidad temporalmente no disponible',
};
