import {
  Button, FormComponent, IconImporter, Title,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { AxiosResponse } from 'axios';
import {
  useContext, useEffect, useState,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { Client } from '#application/client/Client.Type';
import { StoreDiscountsListModule } from '#application/discount/modules/StoreDiscountList.Module';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { ClientsHttps, ClientsPayload } from '#infrastructure/api/http/Clients.Https';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { useClientFormInputs } from '#infrastructure/implementation/application/hooks/useClientFormInputs.Hook';
import { useGetClient } from '#infrastructure/implementation/application/hooks/useGetClient.Hook';

export interface CustomAxiosResponse extends AxiosResponse {
  statusCode?: number;
}

export const clientFormInitialState: Partial<Client> = {
  name: '',
  tributaryId: '',
};

export function ClientFormModule() {
  const { id } = useParams();
  const [isEditing, setIsEditing] = useState<boolean>(!id);
  const [defaultInputs, setDefaultInputs] = useState<Partial<Client>>({
    id: '',
    name: '',
    tributaryId: '',
    client_company: null,
  });
  const { client, loading, error } = useGetClient(id);
  const { inputs, formState, resetForm } = useClientFormInputs(defaultInputs, isEditing);
  const { getToken } = useContext(AuthContext);
  const [isLoading, setIsloading] = useState(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const navigate = useNavigate();
  const formHasErrors = (formError: boolean) => setHasError(formError);
  const clientsPath = ApplicationRegistry.PathService.clients.base();
  const text = TextService.getText();

  useEffect(() => {
    if (id && client) {
      setDefaultInputs({
        ...client,
      });
    }
  }, [id, client]);

  useEffect(() => {
    if (error) {
      Notification({ message: text.common.loadingErrorNotification, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [error]);

  const clientFormInputSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    setIsloading(true);
    if (hasError) {
      Notification({ message: text.common.formFieldsRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return;
    }

    const token = await getToken();

    const payload: ClientsPayload = {
      name: formState.name?.trim() || '',
      tributaryId: formState.tributaryId === '' ? null : formState?.tributaryId?.trim(),
      clientCompanyId: null,
      storeDiscounts: [],
      contactInformation: {
        billingEmail: '',
        billingPhoneNumber: '',
        billingWhatsapp: '',
        purchasesEmail: '',
        purchasesPhoneNumber: '',
        purchasesWhatsapp: '',
        salesEmail: '',
        salesPhoneNumber: '',
        salesWhatsapp: '',
        shippingAddress: '',
        billingAddress: '',
      },
    };

    try {
      const response: CustomAxiosResponse = !id ? await ClientsHttps.postClients(token, [payload]) : await ClientsHttps.updateClient(token, id, payload);
      if (!response?.status || response?.status === 500) {
        Notification({ message: !id ? text.clients.addFailed : text.clients.updateFailed, type: MSG_ERROR_TYPES.ERROR });
        return;
      }

      Notification({ message: !id ? text.clients.addSuccess : text.clients.updateSuccess, type: MSG_ERROR_TYPES.SUCCESS });
      navigate(clientsPath);
      resetForm();
    } catch (err) {
      Notification({ message: !id ? text.clients.addFailed : text.clients.updateFailed, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleSubmitClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    clientFormInputSubmit(e);
  };

  const saveButton = isEditing
    && <Button onClick={handleSubmitClick} disabled={isLoading || hasError}>
      <div className='flex gap-2 items-center'>
        <IconImporter size={24} name='floppyDisk' />
        <span>{text.common.save}</span>
      </div>
    </Button>;

  const editButton = id && !isEditing
    && <IconImporter
      name='pencil'
      size={24}
      color={Theme.colors.dark[500]}
      onClick={() => setIsEditing(!isEditing)}
      className='text-dark-500 hover:text-dark-700 transitin-all cursor-pointer ease-in-out border border-dark-400 rounded-full p-1'
    />;

  return (
    <div className='flex flex-col w-full gap-4 p-7'>
      {loading && <p>{`${text.common.loading}...`}</p>}
      <div className='flex justify-between'>
        <Title as='h2'>{id ? text.clients.edit : text.clients.addClient}</Title>
        {saveButton}
      </div>
      <div className='bg-white rounded-2xl w-3/4 p-8 max-h-min shadow-lg flex flex-col gap-6'>
        <div className='flex justify-between'>
          <div>{text.common.properties}</div>
          {editButton}
        </div>
        <FormComponent
          inputs={inputs}
          formValidation={formHasErrors}
          orientation='horizontal'
          columnsNumber={2}
        />
      </div>
      {Boolean(id) && <StoreDiscountsListModule className='!p-0 mt-4' showHeader={false} id={id} />}
    </div>
  );
}
