import { Client } from '#application/client/Client.Type';
import { Company } from '#application/company/Company.Type';

type DiscountTypes = 'percentage' | 'amount';

interface CatalogDiscountClient {
  client: Pick<Client, 'id' | 'name'> & {
    client_company?: Company;
  };
}

export interface CatalogDiscount {
  id: string;
  name: string;
  requiredQuantity?: number;
  startDate?: string;
  endDate?: string;
  discountType?: DiscountTypes;
  discountValue: number;
  catalog_discount_clients: CatalogDiscountClient[];
  disabledAt?: string;
}
