import {
  IconImporter, OptionsDropdownProps,
} from '@pitsdepot/storybook';
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { RelationComponentWithDropDownSearch } from '#/lib/appComponent/common/RelationComponentWithDropDownSearch';
import AddNewDiscount from '#appComponent/addDiscount/AddNewDiscount.Component';
import { FORM_VALIDATION_MESSAGES } from '#appComponent/addDiscount/AddNewDiscount.Constant';
import { CustomDropdownOptionRenderer } from '#appComponent/common/CustomDropdownOptionRenderer';
import { PROPERTIES } from '#application/catalog/modules/Catalog.Constants';
import {
  NEW_DISCOUNT_TITLE,
  UPDATE_DISCOUNT_TITLE,
} from '#application/constants/texts/CatalogDiscount.Constants';
import { CatalogDiscountItem } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import { useGetCatalog } from '#infrastructure/implementation/application/catalog/UseGetCatalog.Hook';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { useCatalogDiscountForm } from '#infrastructure/implementation/application/hooks/useCatalogDiscountForm';
import { useCatalogDiscountInputs } from '#infrastructure/implementation/application/hooks/useCatalogDiscountInputs.Hook';
import { useGetCatalogDiscount } from '#infrastructure/implementation/application/hooks/useGetCatalogDiscount';
import { validateDiscountForm } from '#infrastructure/implementation/application/utils/discountFormUtils';
import { NotificableError } from '#infrastructure/implementation/application/utils/Error.Util';

const catalogDiscountInitialState: Partial<CatalogDiscountItem> = {
  name: '',
  discountType: 'percentage',
  discountValue: 0,
  requiredQuantity: 0,
  startDate: '',
  endDate: '',
  disabledAt: '',
  active: false,
};

export function CatalogDiscountFormModule({ id }: { id?: string }) {
  const { catalogDiscount } = useGetCatalogDiscount(id);
  const [catalogDiscountFormFields, setCatalogDiscountFormFields] = useState<Partial<CatalogDiscountItem>>(catalogDiscountInitialState);
  const [initialCatalogsRelated, setInitialCatalogsRelated] = useState<OptionsDropdownProps[]>([]);
  const [initialClientsRelated, setInitialClientsRelated] = useState<OptionsDropdownProps[]>([]);
  const [isEditing, setIsEditing] = useState<boolean>(Boolean(!id));
  const navigate = useNavigate();

  const {
    inputs,
    formState,
  } = useCatalogDiscountInputs({
    initialState: catalogDiscountFormFields,
    editMode: isEditing,
    id,
  });

  const {
    saveDiscount,
    assignDiscountClients,
    unAssignDiscountClients,
    assignDiscountCatalogs,
    unAssignDiscountCatalogs,
  } = useCatalogDiscountForm();

  useEffect(() => {
    if (id && catalogDiscount) {
      setCatalogDiscountFormFields({ ...catalogDiscount });
    }
  }, [id, catalogDiscount]);

  const derivedCatalogs = useMemo(() => (
    catalogDiscount?.catalogs?.map((catalog) => ({
      id: catalog.catalog.id,
      name: catalog.catalog.name,
      catalog_media: catalog.catalog.catalog_media || [],
      readId: catalog.catalog.readId,
      renderer: CustomDropdownOptionRenderer as (value: unknown) => React.ReactNode,
    })) || []
  ), [catalogDiscount]);

  const derivedClients = useMemo(() => (
    catalogDiscount?.catalog_discount_clients?.map((client) => ({ id: client.clientId, name: client.client.name })) || []
  ), [catalogDiscount]);

  useEffect(() => {
    setInitialCatalogsRelated(derivedCatalogs);
    setInitialClientsRelated(derivedClients);
  }, [derivedCatalogs, derivedClients]);

  const onSaveDiscount = useCallback(async () => {
    if (!catalogDiscountFormFields) {
      Notification({ message: FORM_VALIDATION_MESSAGES.INCOMPLETE_FORM, type: MSG_ERROR_TYPES.ERROR });
      return;
    }

    const validationError = validateDiscountForm(catalogDiscountFormFields);
    if (validationError) {
      Notification({ message: validationError, type: MSG_ERROR_TYPES.ERROR });
      return;
    }

    const disabledAt = () => {
      if (catalogDiscountFormFields.active === undefined) {
        return catalogDiscountFormFields.disabledAt;
      }
      return catalogDiscountFormFields.active ? null : new Date().toISOString();
    };

    const payload: Omit<CatalogDiscountItem, 'id'> = {
      name: catalogDiscountFormFields?.name || '',
      discountType: catalogDiscountFormFields?.discountType || 'amount',
      discountValue: Number(catalogDiscountFormFields?.discountValue) || 0,
      requiredQuantity: Number(catalogDiscountFormFields?.requiredQuantity) || 0,
      startDate: new Date(catalogDiscountFormFields?.startDate || '').toISOString(),
      endDate: catalogDiscountFormFields?.endDate ? new Date(catalogDiscountFormFields?.endDate).toISOString() : null,
      disabledAt: disabledAt(),
    };

    try {
      const response = await saveDiscount(payload as CatalogDiscountItem);
      if (response.status === 201 || response.status === 200) {
        Notification({
          message: id ? 'Actualizado exitosamente' : 'Creado exitosamente',
          type: MSG_ERROR_TYPES.SUCCESS,
        });
        setIsEditing(false);
      }

      if (response?.status === 201) {
        navigate(CorePath.catalogDiscounts.viewCatalogDiscount(response?.data?.[0]?.id));
      }
    } catch (error) {
      const message = error instanceof NotificableError ? error.message : 'Error al guardar los cambios';
      const type = error instanceof NotificableError ? error.type : MSG_ERROR_TYPES.ERROR;
      Notification({ message, type });
    }
  }, [catalogDiscountFormFields, id, navigate, saveDiscount]);

  const handleToogleMode = useCallback(() => {
    if (isEditing) {
      onSaveDiscount();
      return;
    }
    setIsEditing((prev) => !prev);
  }, [isEditing, onSaveDiscount]);

  return (
    <div className="w-full px-7 h-full pb-7">
      <h1 className="font-semibold text-xl">
        {id ? UPDATE_DISCOUNT_TITLE : NEW_DISCOUNT_TITLE}
      </h1>

      <div className="flex gap-8 py-7">
        <div className="w-3/4 bg-white rounded-2xl p-8 shadow-lg flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg ">{PROPERTIES}</h2>
            <IconImporter
              name={isEditing ? 'check' : 'pencil'}
              // eslint-disable-next-line max-len
              className={`pd-rounded-full pd-border pd-p-1 pd-cursor-pointer pd-transition-all pd-ease-in-out ${!isEditing ? 'pd-text-dark-500 hover:pd-text-dark-700 pd-border pd-border-dark-400' : 'pd-text-white pd-bg-primary pd-border-primary hover:pd-text-primary hover:pd-bg-white'}`}
              size={24}
              onClick={handleToogleMode}
            />
          </div>

          <AddNewDiscount
            setNewDiscount={setCatalogDiscountFormFields}
            formState={formState}
            inputs={inputs}
          />
        </div>

        <RelationComponentWithDropDownSearch
          id={id}
          title='Catálogos'
          useGetHook={useGetCatalog}
          useAssignHook={assignDiscountCatalogs}
          useUnAssignHook={unAssignDiscountCatalogs}
          initialValue={initialCatalogsRelated}
          setNewValues={setInitialCatalogsRelated}
        />
      </div>

      <RelationComponentWithDropDownSearch
        id={id}
        title='Clientes'
        useGetHook={ApplicationRegistry.ClientService.useGetClients}
        useAssignHook={assignDiscountClients}
        useUnAssignHook={unAssignDiscountClients}
        initialValue={initialClientsRelated}
        setNewValues={setInitialClientsRelated}
      />
    </div>
  );
}
