import { IconImporter, OptionsDropdownProps } from '@pitsdepot/storybook';
import {
  useCallback, useEffect, useMemo, useState,
} from 'react';
import { useNavigate } from 'react-router-dom';

import AddNewDiscount from '#appComponent/addDiscount/AddNewDiscount.Component';
import { FORM_VALIDATION_MESSAGES } from '#appComponent/addDiscount/AddNewDiscount.Constant';
import { Notification } from '#appComponent/common/Notification.Component';
import { RelationComponentWithDropDownSearch } from '#appComponent/common/RelationComponentWithDropDownSearch';
import { CREATE_STORE_DISCOUNT, PROPERTIES_SUBHEADER, UPDATE_STORE_DISCOUNT } from '#application/constants/texts/StoreDiscount.Constants';
import { StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import { useGetStoreDiscount } from '#infrastructure/implementation/application/hooks/useGetStoreDiscount';
import { useStoreDiscountForm } from '#infrastructure/implementation/application/hooks/useStoreDiscountForm';
import { useStoreDiscountInputs } from '#infrastructure/implementation/application/hooks/useStoreDiscountInputs.Hook';
import { validateDiscountForm } from '#infrastructure/implementation/application/utils/discountFormUtils';
import { NotificableError } from '#infrastructure/implementation/application/utils/Error.Util';

const storeDiscountInitialState: Partial<StoreDiscountItem> = {
  name: '',
  discountType: 'percentage',
  discountValue: 0,
  requiredAmount: 0,
  startDate: '',
  endDate: '',
  disabledAt: '',
  active: false,
};

export const StoreDiscountFormModule = ({ id }: { id?: string }) => {
  const { storeDiscount } = useGetStoreDiscount(id);
  const [storeDiscountFormFields, setStoreDiscountFormFields] = useState<Partial<StoreDiscountItem>>(storeDiscountInitialState);
  const [initialClientsRelated, setInitialClientsRelated] = useState<OptionsDropdownProps[]>([]);
  const [isEditing, setIsEditing] = useState<boolean>(Boolean(!id));
  const navigate = useNavigate();

  const {
    inputs,
    formState,
  } = useStoreDiscountInputs({
    initialState: storeDiscountFormFields,
    editMode: isEditing,
    id,
  });

  const {
    saveDiscount,
    assignDiscountClients,
    unAssignDiscountClients,
  } = useStoreDiscountForm();

  useEffect(() => {
    if (id && storeDiscount) {
      setStoreDiscountFormFields({
        ...storeDiscount,
      });
    }
  }, [id, storeDiscount]);

  const derivedClients = useMemo(() => (
    storeDiscount?.storeDiscountClients?.map((client) => ({ id: client.clientId, name: client.client.name })) || []
  ), [storeDiscount]);

  useEffect(() => {
    setInitialClientsRelated(derivedClients);
  }, [derivedClients]);

  const onSaveDiscount = useCallback(async () => {
    if (!storeDiscountFormFields) {
      Notification({ message: FORM_VALIDATION_MESSAGES.INCOMPLETE_FORM, type: MSG_ERROR_TYPES.ERROR });
      return;
    }

    const validationError = validateDiscountForm(storeDiscountFormFields);
    if (validationError) {
      Notification({ message: validationError, type: MSG_ERROR_TYPES.ERROR });
      return;
    }

    const disabledAt = () => {
      if (storeDiscountFormFields?.active === undefined) {
        return storeDiscountFormFields.disabledAt;
      }
      return storeDiscountFormFields.active ? null : new Date().toISOString();
    };

    const payload: Omit<StoreDiscountItem, 'id'> = {
      name: storeDiscountFormFields?.name || '',
      discountType: storeDiscountFormFields?.discountType || 'percentage',
      discountValue: Number(storeDiscountFormFields?.discountValue),
      requiredAmount: Number(storeDiscountFormFields?.requiredAmount),
      startDate: new Date(storeDiscountFormFields?.startDate || '').toISOString(),
      endDate: storeDiscountFormFields?.endDate ? new Date(storeDiscountFormFields?.endDate).toISOString() : null,
      disabledAt: disabledAt(),
    };

    try {
      const response = await saveDiscount(payload as StoreDiscountItem);
      if (response.status === 201 || response.status === 200) {
        Notification({
          message: id ? 'Actualizado exitosamente' : 'Creado exitosamente',
          type: MSG_ERROR_TYPES.SUCCESS,
        });
        setIsEditing(false);
      }

      if (response?.status === 201) {
        const storeDiscountPath = ApplicationRegistry.PathService.storeDiscounts.base();
        navigate(`${storeDiscountPath}/${response?.data?.[0]?.id}`);
      }
    } catch (error) {
      const message = error instanceof NotificableError ? error.message : 'Error al guardar los cambios';
      const type = error instanceof NotificableError ? error.type : MSG_ERROR_TYPES.ERROR;
      Notification({ message, type });
    }
  }, [storeDiscountFormFields, id, navigate, saveDiscount]);

  const handleToogleMode = useCallback(() => {
    if (isEditing) {
      onSaveDiscount();
      return;
    }
    setIsEditing((prev) => !prev);
  }, [isEditing, onSaveDiscount]);

  return (
    <div className="w-full px-7 h-full pb-7">
      <h1 className="font-semibold text-xl">
        {id ? UPDATE_STORE_DISCOUNT : CREATE_STORE_DISCOUNT}
      </h1>

      <div className="flex gap-8 py-7">
        <div className="w-3/4 bg-white rounded-2xl p-8 shadow-lg flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg">{PROPERTIES_SUBHEADER}</h2>
            <IconImporter
              name={isEditing ? 'check' : 'pencil'}
              className={`
                pd-rounded-full pd-border
                pd-p-1 pd-cursor-pointer
                pd-transition-all
                pd-ease-in-out 
                ${!isEditing
                ? 'pd-text-dark-500 hover:pd-text-dark-700 pd-border pd-border-dark-400'
                : 'pd-text-white pd-bg-primary pd-border-primary hover:pd-text-primary hover:pd-bg-white'}`}
              size={24}
              onClick={handleToogleMode}
            />
          </div>

          <AddNewDiscount
            setNewDiscount={setStoreDiscountFormFields}
            formState={formState}
            inputs={inputs}
          />
        </div>

        <RelationComponentWithDropDownSearch
          id={id}
          title='Clientes'
          useGetHook={ApplicationRegistry.ClientService.useGetClients}
          useAssignHook={assignDiscountClients}
          useUnAssignHook={unAssignDiscountClients}
          initialValue={initialClientsRelated}
          setNewValues={setInitialClientsRelated}
        />
      </div>
    </div>
  );
};
