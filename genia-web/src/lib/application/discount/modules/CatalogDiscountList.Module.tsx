import { Column, Row } from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';

import { ChipCellComponent } from '#appComponent/common/ChipCell.component';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import {
  AnchorCell,
  DiscountValueCell, GenericCell, ProductTypeCell, StoreDiscountsClientsCell,
} from '#appComponent/table/TableCells.Component';
import { CatalogDiscount } from '#application/discount/CatalogDiscount.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

const text = TextService.getText();

export const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: 'product',
    width: '20%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Tipo',
    dataKey: 'type',
    width: '10%',
    renderer: ProductTypeCell,
  },
  {
    header: 'Valor',
    dataKey: 'value',
    width: '8%',
    renderer: DiscountValueCell,
  },
  {
    header: 'Cantidad requerida',
    dataKey: 'requiredQuantity',
    width: '8%',
    renderer: GenericCell,
  },
  {
    header: 'Clientes',
    dataKey: 'clients',
    width: '14%',
    renderer: StoreDiscountsClientsCell,
  },
  {
    header: 'Fecha inicial',
    dataKey: 'startDate',
    width: '20%',
    renderer: (value) => (value === null || !value ? undefined : <ChipCellComponent color={Theme.colors.dark[200]} text={value} />),
  },
  {
    header: 'Fecha final',
    dataKey: 'endDate',
    width: '20%',
    renderer: (value) => (value === null || !value ? undefined : <ChipCellComponent color={Theme.colors.dark[200]} text={value} />),
  },
];

function mapper(items: CatalogDiscount[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    product: { value: item.name, url: ApplicationRegistry.PathService.catalogDiscounts.viewCatalogDiscount(item.id) },
    type: item.discountType,
    value: { value: item.discountValue || '', type: item.discountType || '' },
    requiredQuantity: item.requiredQuantity,
    clients: item.catalog_discount_clients?.map((client) => ({
      id: client.client.id,
      name: client.client.name,
    })),
    startDate: item.startDate && ApplicationRegistry.TimeService.toLocalDateFormat(item.startDate),
    endDate: item.endDate && ApplicationRegistry.TimeService.toLocalDateFormat(item.endDate),
    status: item.disabledAt,
  }));
}

interface CatalogDiscountsListModuleProps {
  className?: string;
  topRightButton?: React.ReactNode;
  id?: string;
}

export function CatalogDiscountListModule({
  className, id,
}: CatalogDiscountsListModuleProps) {
  return (
    <PaginatedListAppComponent
      title={text.discounts.catalogDiscounts}
      columns={columns}
      useGetHook={ApplicationRegistry.CatalogDiscountService.useGetCatalogDiscounts}
      mapper={mapper}
      className={className}
      showHeader={false}
      id={id}
    />
  );
}
