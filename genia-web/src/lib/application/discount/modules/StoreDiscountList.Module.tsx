import { Column, Row } from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';

import { ChipCellComponent } from '#appComponent/common/ChipCell.component';
import { PlusRoundedButton } from '#appComponent/common/PlusRoundedButton.Component';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import {
  AnchorCell,
  DiscountValueCell, GenericCell,
  ProductTypeCell,
  StoreDiscountsClientsCell,
} from '#appComponent/table/TableCells.Component';
import { StoreDiscount } from '#application/discount/StoreDiscount.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

const text = TextService.getText();

const DATA_KEYS = {
  NAME: 'name',
  TYPE: 'type',
  VALUE: 'value',
  REQUIRED_AMOUNT: 'requiredAmount',
  CLIENTS: 'clients',
  START_DATE: 'startDate',
  END_DATE: 'endDate',
};

const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: DATA_KEYS.NAME,
    width: '20%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Tipo',
    dataKey: DATA_KEYS.TYPE,
    width: '10%',
    renderer: ProductTypeCell,
  },
  {
    header: 'Valor',
    dataKey: DATA_KEYS.VALUE,
    width: '10%',
    renderer: DiscountValueCell,
  },
  {
    header: 'Monto requerido',
    dataKey: DATA_KEYS.REQUIRED_AMOUNT,
    width: '15%',
    renderer: GenericCell,
  },
  {
    header: 'Clientes',
    dataKey: DATA_KEYS.CLIENTS,
    width: '15%',
    renderer: StoreDiscountsClientsCell,
  },
  {
    header: text.common.startDate,
    dataKey: DATA_KEYS.START_DATE,
    width: '15%',
    renderer: (value) => (value === null || !value ? undefined : <ChipCellComponent color={Theme.colors.dark[200]} text={value} />),
  },
  {
    header: text.common.endDate,
    dataKey: DATA_KEYS.END_DATE,
    width: '15%',
    renderer: (value) => (value === null || !value ? undefined : <ChipCellComponent color={Theme.colors.dark[200]} text={value} />),
  },
];

interface StoreDiscountsListModuleProps {
  className?: string;
  showHeader?: boolean;
  topRightButton?: React.ReactNode;
  id?: string;
}

function mapper(items: StoreDiscount[]):Row[] {
  return items.map((item) => ({
    id: item.id,
    name: { value: item.name, url: ApplicationRegistry.PathService.storeDiscounts.viewStoreDiscount(item.id) },
    type: item.discountType,
    value: { value: item.discountValue || '', type: item.discountType || '' },
    requiredAmount: item.requiredAmount,
    clients: item.storeDiscountClients?.map((client) => ({
      id: client.client.id,
      name: client.client.name,
    })),
    startDate: item.startDate && ApplicationRegistry.TimeService.toLocalDateFormat(item.startDate),
    endDate: item.endDate && ApplicationRegistry.TimeService.toLocalDateFormat(item.endDate),
    status: item.disabledAt,
  }));
}

const addStoreDiscount = ApplicationRegistry.PathService.storeDiscounts.addStoreDiscount();

const addNewDiscountButton = <PlusRoundedButton to={addStoreDiscount} />;

export function StoreDiscountsListModule({
  className, id,
}: StoreDiscountsListModuleProps) {
  return (
    <PaginatedListAppComponent
      title={text.discounts.storeDiscounts}
      columns={columns}
      useGetHook={ApplicationRegistry.StoreDiscountService.useGetStoreDiscounts}
      mapper={mapper}
      className={className}
      showHeader={false}
      topRightButton={addNewDiscountButton}
      orderBy='name'
      id={id}
    />
  );
}
