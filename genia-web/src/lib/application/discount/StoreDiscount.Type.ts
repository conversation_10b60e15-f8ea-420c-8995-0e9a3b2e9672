import { Client } from '#application/client/Client.Type';

export type DiscountTypes = 'percentage' | 'amount';

interface StoreDiscountClient {
  client: Pick<Client, 'id' | 'name'>;
}

export interface StoreDiscount {
  id: string;
  name: string;
  discountType: DiscountTypes;
  discountValue: number;
  requiredAmount: number;
  startDate: string;
  endDate?: string;
  disabledAt?: string;
  storeDiscountClients: StoreDiscountClient[];
}
