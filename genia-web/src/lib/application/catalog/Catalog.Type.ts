import { InventoryItem } from '#application/inventory/Inventory.Type';

type CatalogTypeProps = 'product' | 'service' | 'bundle';
type MediaType = 'image' | 'video';

interface CatalogMedia {
  id: string;
  mediaType: MediaType;
  url: string;
}

interface CatalogTax {
  taxId: string;
}

export interface InventoryCatalogs {
  id: string;
  inventoryId: string;
  catalogId: string;
  quantity: number;
  inventory?: InventoryItem;
}

export type CatalogItem = Omit<InventoryItem, 'type' | 'measurementUnit' | 'hasStockValidation' | 'standardIdentifier' | 'inventoryMedia' | 'sku'> & {
  type: CatalogTypeProps;
  price: number;
  requiresStock: boolean;
  readId: string;
  catalog_media: CatalogMedia[];
  catalogTax: CatalogTax[];
  inventoryCatalogs: InventoryCatalogs[];
  media?: { url: string }[];
};
