import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';
import { CatalogInventory } from '#domain/aggregates/catalog/CatalogInventory.ValueObject';

export interface CatalogService {
  useGetCatalogs: (props: GetTableDataProps) => GetTableDataResponse<CatalogItem>
  useGetCatalogItem: (id?: string) => GetTableDataResponse<CatalogItem>
  useGetCatalogInventoryByIds: (ids: string[]) => {items: CatalogInventory[], loading: boolean, error: Error | null, refetch: () => void}
  useGetCatalogByReadId: (props: Omit<GetTableDataProps, 'orderBy' | 'order'> & {
    searchTerm?: string;
    providerId?: string;
    enabled?: boolean;
  }) => GetTableDataResponse<ProviderCatalogItem>;
}
