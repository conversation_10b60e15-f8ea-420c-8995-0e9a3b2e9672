import { CatalogItem } from '#application/catalog/Catalog.Type';

export const TITLE = 'Catálogo';
export const ADD_PRODUCT_BTN_LABEL = 'Agregar nuevo Item al Catálogo';
export const CATALOG_ADD_FAILED = 'Error al agregar al Catalogo';
export const RESPONSE_ERROR = 'Error en la respuesta';
export const CATALOG_ADD_SUCCESS = 'Agregado al catalogo';
export const CATALOG_UPDATE_SUCCESS = 'Item del catalogo actualizado';
export const CATALOG_NOT_INFO = 'No has agregado ninguna informacion';
export const NEW_CATALOG_ITEM = 'Nueva pieza de catálogo';
export const UPDATE_CATALOG_ITEM = 'Actualizar pieza de catálogo';
export const PROPERTIES = 'Propiedades';
export const UPDATE = 'Actualizar';
export const SAVE = 'Guardar';
export const CATALOG_UPDATE_FAILED = 'Error al actualizar en el inventario';
export const INVALID_CHARACTER_PRICE = '';
export const INVALID_PRICE = 'El precio debe ser mayor a 0';
export const ERROR_PRICE = 'El precio es requerido';

export const LABELS = {
  PRODUCT_NAME: 'Nombre del Producto',
  DISCOUNT_NAME: 'Nombre del Descuento',
  TYPE: 'Tipo',
  ID: 'ID',
  DESCRIPTION: 'Descripción',
  ADD_IMAGE: 'Agregar Imagen',
  PRICE: 'Precio',
  STOCK: 'Stock',
  STOCK_DISABLED: 'Desactivar Stock',
  ENABLE_DISABLE: 'Activo/Inactivo',
  ID_AUTOGENERATE: 'Autogenerar',
  VALUE: 'Valor',
  REQUIRED_AMOUNT: 'Monto requerido',
  REQUIRED_QUANTITY: 'Cantidad requerida',
  START_DATE: 'Fecha de Inicio',
  END_DATE: 'Fecha de Fin',
  DISCOUNT_TYPE: 'Tipo de Descuento',
};

export const PLACEHOLDERS = {
  PRODUCT_NAME: 'Ej: Pastillas de Freno Delanteras - Marca Acme - Toyota Corolla 2018...',
  ID: 'CAT-12345',
  DESCRIPTION: 'Ej: Diseñadas para soportar altas temperaturas y frenadas bruscas...',
  PRICE: 'Ej: 199.99',
  ID_AUTOGENERATE: 'Autogenerado...',
};

export const TOOLTIPS = {
  PRODUCT_NAME: 'Introduzca un nombre descriptivo para identificar el producto.',
  ID: 'Código único para identificar el producto en el inventario.',
  TYPE: 'Selecciona el tipo al que pertenece el producto.',
  DESCRIPTION: 'Proporciona una breve descripción del producto.',
  PRICE: 'Introduce el precio del producto en formato numérico.',
  STOCK: 'Cantidad actual del producto disponible en el inventario.',
  ACTIVE: 'Define si el producto está activo en el catálogo.',
  MEASUREMENT_UNIT: 'Define cómo se mide el producto.',
  ADD_IMAGE: 'Sube una imagen clara y representativa del producto. Funcionalidad temporalmente no disponible',
};

export const INPUT_NAME = {
  NAME: 'name',
  READ_ID: 'readId',
  TYPE: 'type',
  DESCRIPTION: 'description',
  PRICE: 'price',
  STOCK: 'stock',
  MEASUREMENT_UNIT: 'measurementUnit',
  IMAGE: 'image',
  ENABLE_DISABLE: 'active',
  DISCOUNT_TYPE: 'discountType',
  DISABLED_AT: 'disabledAt',
};

export const catalogInitialState: Partial<CatalogItem> & {active: boolean} = {
  name: '',
  readId: '',
  type: 'product',
  description: '',
  price: 0,
  stock: 0,
  active: false,
  catalogTax: [],
};
