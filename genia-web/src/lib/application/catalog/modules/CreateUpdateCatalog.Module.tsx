import { FormLabelProps } from '@pitsdepot/storybook';
import { AxiosResponse } from 'axios';
import {
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useParams } from 'react-router-dom';

import CatalogForm from '#appComponent/catalog/CatalogForm.Component';
import CreateUpdateItem from '#appComponent/common/CreateUpdateItem';
import { Notification } from '#appComponent/common/Notification.Component';
import { useMedia } from '#appComponent/common/hooks/useMedia.Hook';
import { MediaToRemoveProvider, useMediaToRemove } from '#appComponent/common/media/MediaToRemove.Context';
import { CatalogItem, InventoryCatalogs } from '#application/catalog/Catalog.Type';
import { CategoryAttributes, NewFormCatalogProps } from '#application/deprecated/DashboardPages.Type';
import InventoryRelation from '#application/deprecated/InventoryRelation.Component';
import { CatalogDiscountListModule } from '#application/discount/modules/CatalogDiscountList.Module';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { CatalogHttps } from '#infrastructure/api/http/Catalog.Http';

import {
  catalogInitialState,
  INPUT_NAME,
} from './Catalog.Constants';

const inventoryRelationlabel: FormLabelProps = {
  title: 'Inventario relacionado',
  tooltip: {
    // eslint-disable-next-line max-len
    content: 'A cada pieza de catálogo se le puede relacionar una o varias piezas de inventario. Esto permite la actualización automática de inventario al momento de realizar una orden de compra o venta.',
  },
  icon: {
    name: 'info',
  },
};

interface CustomAxiosResponse extends AxiosResponse {
  statusCode?: number;
}

const CreateUpdateCatalogContent = () => {
  const { id } = useParams();
  const { getToken } = useContext(ApplicationRegistry.AuthContext);
  const goToPath = ApplicationRegistry.PathService.useGoToPath();
  const { imagesToRemove } = useMediaToRemove();
  const { handleMediaOperations } = useMedia();

  const [editMode, setEditMode] = useState(!id || false);
  const [defaultInputs, setDefaultInputs] = useState<Partial<CatalogItem>>(catalogInitialState);
  const [attributeFormInputs, setAttributeFormInputs] = useState<CategoryAttributes[]>([]);
  const [isLoading, setIsloading] = useState(false);
  const [dataErrorName, setDataErrorName] = useState<string>('');
  const [newAttributesForm, setNewAttributesForm] = useState<CategoryAttributes[]>([]);
  const [newForm, setNewForm] = useState<NewFormCatalogProps & { readId?: string | null, images?:(File | { id: string; url: string; name: string })[] } | null>(null);
  const [inputEmptyError, setInputEmptyError] = useState<boolean>(false);
  const [relatedInventory, setRelatedInventory] = useState<Pick<InventoryCatalogs, 'inventoryId' | 'quantity' >[]>([]);

  const { items: catalogItems, loading, refetch: refetchCatalog } = ApplicationRegistry.CatalogService.useGetCatalogItem(id);
  const { items: taxes } = ApplicationRegistry.TaxesService.useGetTaxes(true);

  const catalogItem = catalogItems?.[0];

  useEffect(() => {
    if (id && catalogItem) {
      setDefaultInputs({
        ...catalogItem,
        description: catalogItem.description || '',
      });

      setAttributeFormInputs(catalogItem?.attributes || []);
      setNewAttributesForm(catalogItem?.attributes || []);
    }
  }, [catalogItem, id]);

  useEffect(() => {
    if (catalogItem && catalogItem.inventoryCatalogs) {
      setRelatedInventory(catalogItem.inventoryCatalogs);
    }
  }, [catalogItem]);

  const {
    validation: {
      nameRequired,
      idRequired,
      descriptionRequired,
      lengthDescriptionFailed,
      priceRequired,
      invalidPrice,
    },
    catalog: {
      addCatalogFailed,
      addCatalogSuccess,
      updateCatalogSuccess,
      updateCatalogFailed,
      updateCatalogPiece,
      newCatalogPiece,
    },
    error: {
      responseError,
    },
  } = TextService.getText();

  const checkValidations = async () => {
    if (!newForm?.name) {
      Notification({ message: nameRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      setDataErrorName(INPUT_NAME.NAME);
      return true;
    }

    if (!newForm?.isIdAutoGenerated && !newForm?.readId) {
      Notification({ message: idRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      setDataErrorName(INPUT_NAME.READ_ID);
      return true;
    }

    if (!newForm.description) {
      Notification({ message: descriptionRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return true;
    }

    if ((newForm?.description ?? '').length > 500) {
      Notification({ message: lengthDescriptionFailed, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return true;
    }

    if (!newForm?.price) {
      Notification({ message: priceRequired, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      setDataErrorName(INPUT_NAME.PRICE);
      return true;
    }

    if (newForm?.price < 0) {
      Notification({ message: invalidPrice, type: MSG_ERROR_TYPES.ERROR });
      setDataErrorName(INPUT_NAME.PRICE);
      setIsloading(false);

      return true;
    }

    return false;
  };

  const onSave = async () => {
    const token = await getToken();
    const taxPayload = newForm?.isTaxEnable ? [taxes[0].id] : [];

    const disabledAt = () => {
      if (newForm?.active === undefined) {
        return newForm?.disabledAt;
      }
      return newForm?.active ? null : new Date().toISOString();
    };

    const { images: _, ...rest } = newForm ?? {};

    const payload = {
      ...rest,
      name: newForm?.name?.trim() || '',
      description: newForm?.description?.trim() || null,
      requiresStock: !newForm?.hasStockValidation,
      disabledAt: disabledAt(),
      standardIdentifier: newForm?.standardIdentifier ?? null,
      attributes: newAttributesForm?.length === 0 ? null : newAttributesForm,
      price: Number(newForm?.price),
      taxIds: taxPayload,
      inventoryRelations: id ? relatedInventory : relatedInventory?.map((item) => ({ id: item.inventoryId, quantity: item.quantity })),
    };

    try {
      const response: CustomAxiosResponse = id ? await CatalogHttps.updateCatalogItem(token, id, payload) : await CatalogHttps.createCatalogItem(token, payload);

      if (response.statusCode === 409) return;

      if (!response?.status) {
        Notification({ message: !id ? addCatalogFailed : updateCatalogFailed, type: MSG_ERROR_TYPES.ERROR });
        return;
      }

      const entityId = id ? response?.data?.id : response.data?.[0].id;

      if ((imagesToRemove.length > 0 || newForm?.images?.length) && entityId && token) {
        await handleMediaOperations({
          entity: 'catalog',
          entityId,
          images: newForm?.images,
          token,
        });
      }

      Notification({ message: id ? updateCatalogSuccess : addCatalogSuccess, type: MSG_ERROR_TYPES.SUCCESS });

      setEditMode(false);

      if (id) {
        refetchCatalog();
      }

      const catalogId = id || response.data?.[0].id;
      if (catalogId) {
        goToPath(ApplicationRegistry.PathService.catalog.viewCatalog(catalogId));
      } else {
        goToPath(ApplicationRegistry.PathService.catalog.base());
      }
    } catch (err) {
      Notification({ message: responseError, type: MSG_ERROR_TYPES.ERROR });
    } finally {
      setIsloading(false);
    }
  };

  const handleChildStateChange = (newState: NewFormCatalogProps) => {
    setNewForm(newState);
  };

  const handleAddRelatedInventory = useCallback((item: Pick<InventoryCatalogs, 'inventoryId' | 'quantity'>) => {
    setRelatedInventory((prev) => {
      if (prev.some((related) => related.inventoryId === item.inventoryId)) return prev;
      return [...prev, item];
    });
  }, []);

  const handleRemoveRelatedInventory = useCallback((inventoryId: string) => {
    setRelatedInventory((prev) => prev.filter((related) => related.inventoryId !== inventoryId));
  }, []);

  const handleChangeRelatedInventoryQuantity = useCallback((inventoryId: string, quantity: number) => {
    setRelatedInventory((prev) => prev.map((item) => {
      if (item.inventoryId === inventoryId) {
        return { ...item, quantity };
      }
      return item;
    }));
  }, []);

  const inventoryRelation = useCallback(() => (
    <InventoryRelation
      label={inventoryRelationlabel}
      disabled={!editMode}
      relatedInventory={relatedInventory}
      onAddRelatedInventory={handleAddRelatedInventory}
      onRemoveRelatedInventory={handleRemoveRelatedInventory}
      onChangeRelatedInventoryQuantity={handleChangeRelatedInventoryQuantity}
    />
  ), [editMode, relatedInventory, handleAddRelatedInventory, handleRemoveRelatedInventory, handleChangeRelatedInventoryQuantity]);

  return (
    <div className='gap-8 pl-7 w-full'>
      <CreateUpdateItem
        title={id ? updateCatalogPiece : newCatalogPiece}
        addButtonLabel={id ? 'Actualizar' : 'Guardar'}
        checkValidations={checkValidations}
        editMode={editMode}
        setEditMode={setEditMode}
        isLoading={isLoading || loading}
        setIsloading={setIsloading}
        attributeFormInputs={attributeFormInputs}
        setNewAttributesForm={setNewAttributesForm}
        setInputEmptyError={setInputEmptyError}
        newAttributesForm={newAttributesForm}
        inputEmptyError={inputEmptyError}
        onSave={onSave}
        aditionalChildren={inventoryRelation()}
      >
        <CatalogForm
          handleChildStateChange={handleChildStateChange}
          defaultInputs={defaultInputs}
          editMode={editMode}
          dataErrorName={dataErrorName}
        />
      </CreateUpdateItem>

      {Boolean(id) && <CatalogDiscountListModule className='!p-0 mt-4' id={id}/>
      }
    </div>
  );
};

const CreateUpdateCatalogModule = () => (
  <MediaToRemoveProvider>
    <CreateUpdateCatalogContent />
  </MediaToRemoveProvider>
);

export default CreateUpdateCatalogModule;
