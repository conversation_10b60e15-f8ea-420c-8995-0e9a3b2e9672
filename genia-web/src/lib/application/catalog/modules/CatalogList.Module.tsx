import { Column, Row } from '@pitsdepot/storybook';

import ProductImg from '#/assets/icon.png';
import { PaginatedListAppComponent } from '#appComponent/table/PaginatedList.AppComponent';
import {
  AttributesCell, CatalogProductCell, DescriptionProductCell, PriceCell, ProductTypeCell, RequiresStockCell, StatusCell,
} from '#appComponent/table/TableCells.Component';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import ApplicationRegistry from '#composition/Application.Registry';

const columns: Column[] = [
  {
    header: 'Producto',
    dataKey: 'product',
    width: '15%',
    renderer: ({ value, url, id }) => <CatalogProductCell name={value} imgUrl={url} id={id} />,
  },
  {
    header: 'Descripción',
    dataKey: 'description',
    width: '20%',
    renderer: DescriptionProductCell,
  },
  {
    header: 'Identificador',
    dataKey: 'readId',
    width: '10%',
  },
  {
    header: 'Precio',
    dataKey: 'price',
    width: '5%',
    renderer: PriceCell,
  },
  {
    header: 'Tipo de producto',
    dataKey: 'type',
    width: '10%',
    renderer: ProductTypeCell,
  },
  {
    header: 'Requiere stock',
    dataKey: 'requiresStock',
    width: '10%',
    renderer: RequiresStockCell,
  },
  {
    header: 'Atributos',
    dataKey: 'attributes',
    width: '20%',
    renderer: AttributesCell,
  },
  {
    header: 'Estado',
    dataKey: 'status',
    width: '10%',
    renderer: StatusCell,
  },
];

function mapper(items: CatalogItem[]): Row[] {
  return items.map((item) => ({
    id: item.id,
    product: { id: item.id, value: item.name, url: item.catalog_media?.[0]?.url || ProductImg },
    description: item.description,
    readId: item.readId,
    attributes: item.attributes,
    status: item.disabledAt,
    price: item.price,
    requiresStock: item.requiresStock,
    type: item.type,
  }));
}

function CatalogListModule() {
  return (
    <PaginatedListAppComponent
      title='Catálogo'
      columns={columns}
      useGetHook={ApplicationRegistry.CatalogService.useGetCatalogs}
      mapper={mapper}
      searchTitle='Buscar por nombre, descripción, identificador...'
      showHeader={false}
    />
  );
}

export default CatalogListModule;
