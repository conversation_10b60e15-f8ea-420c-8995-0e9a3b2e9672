import { motion } from 'framer-motion';

import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import { PurchaseOrderListModule } from '#application/purchaseOrders/modules/PurchaseOrdersList.Module';
import ApplicationRegistry from '#composition/Application.Registry';

export function PurchaseOrdersListLayout() {
  const addPurchaseOrderPath = ApplicationRegistry.PathService.purchaseOrders.addPurchaseOrder();
  return (
    <motion.div
      className='flex flex-col w-full pl-7 gap-4 pt-0'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        <AddNewItemButton path={addPurchaseOrderPath} label='Agregar orden de compra' />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        <PurchaseOrderListModule />
      </motion.div>
    </motion.div>
  );
}
