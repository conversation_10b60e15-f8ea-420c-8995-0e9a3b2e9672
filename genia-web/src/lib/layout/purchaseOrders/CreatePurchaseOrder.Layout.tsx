import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';

import { CreatePurchaseOrderModule } from '#application/purchaseOrders/modules/CreatePurchaseOrder.Module';

export function CreatePurchaseOrderLayoutProps() {
  const { from } = useParams<{from: string, id: string}>();

  return (
    <motion.div
      className='flex flex-col w-full pl-7 pb-8'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <CreatePurchaseOrderModule from={from || null}/>
      </motion.div>
    </motion.div>
  );
}
