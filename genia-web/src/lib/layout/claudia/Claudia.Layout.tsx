'use client';

import { useChat } from '@ai-sdk/react';
import type { Attachment, UIMessage } from 'ai';
import { motion } from 'framer-motion';
import { useContext, useEffect, useState } from 'react';

import { Messages } from '#appComponent/claudia/Messages.Component';
import { MultimodalInput } from '#appComponent/claudia/MultiModalInput.Component';
import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { ClaudiaHttp } from '#infrastructure/api/http/Claudia.Http';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';

const { CLAUDIA_NODE_API } = GetEnv();

function ClaudiaLayout() {
  const { getToken } = useContext(AuthContext);
  const [initialMessages, setInitialMessages] = useState<Array<UIMessage>>([]);

  useEffect(() => {
    const fetchInitialMessages = async () => {
      const token = await getToken();
      const messages = await ClaudiaHttp.getUserMessages(token);

      const userMessages: UIMessage[] = messages.map((message) => ({
        id: message.id,
        content: message.content,
        role: message.role,
        parts: [{
          type: 'text',
          text: message.content,
        }],
      }));

      setInitialMessages(userMessages);
    };
    fetchInitialMessages();
  }, [getToken]);

  const id = 'claudia';

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
  } = useChat({
    id,
    api: `${CLAUDIA_NODE_API}/workflows/company_workflow/execute`,
    streamProtocol: 'data',
    initialMessages,
    sendExtraMessageFields: true,
    experimental_throttle: 100,
    experimental_prepareRequestBody({ messages: newMessages, ...rest }) {
      return { messages: [newMessages[newMessages.length - 1]], ...rest };
    },
    fetch: async (url, requestParams) => {
      const token = await getToken();
      const headers = {
        ...(requestParams?.headers || {}),
        Authorization: `Bearer ${token}`,
      };
      return fetch(url, {
        ...requestParams,
        headers,
      });
    },
    onError: () => {
      Notification({ message: 'An error occured, please try again!', type: MSG_ERROR_TYPES.ERROR });
    },
  });

  const onSubmit: typeof handleSubmit = async (event) => {
    handleSubmit(event);
  };
  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  // const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  return (
    <motion.div
      className='pl-7 w-full pt-0 flex flex-col h-[calc(100vh-76px-2rem)]'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex flex-col min-w-0 w-full h-full bg-white rounded-2xl"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <Messages
          chatId={id}
          status={status}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isArtifactVisible={false}
        />

        <motion.form
          className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.3 }}
        >
          <MultimodalInput
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={onSubmit}
            status={status}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
          />
        </motion.form>
      </motion.div>

      {/* <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        isReadonly={isReadonly}
      /> */}
    </motion.div>
  );
}

export default ClaudiaLayout;
