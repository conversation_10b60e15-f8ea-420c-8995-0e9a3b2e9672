import { motion } from 'framer-motion';
import { useContext } from 'react';

import { CreateUpdateInventoryModule } from '#application/inventory/modules/CreateUpdateInventory.Module';
import InventoryHistoryModule from '#application/inventory/modules/InventoryHistory.Module';
import InventoryProvidersModule from '#application/inventory/modules/InventoryProviders.Module';
import ApplicationRegistry from '#composition/Application.Registry';

export function CreateUpdateInventoryLayout() {
  const { inventoryId } = useContext(ApplicationRegistry.InventoryContext);

  return (
    <motion.div
      className='flex flex-wrap gap-8 pl-7 pb-7 w-full'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <CreateUpdateInventoryModule />
      </motion.div>
      {inventoryId && (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
        >
          <InventoryProvidersModule />
        </motion.div>
      )}
      {inventoryId && (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.4 }}
        >
          <InventoryHistoryModule />
        </motion.div>
      )}
    </motion.div>
  );
}
