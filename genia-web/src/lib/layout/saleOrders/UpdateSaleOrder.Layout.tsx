import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';

import { UpdateSaleOrderModule } from '#application/saleOrders/modules/UpdateSaleOrder.Module';

export interface UpdateSaleOrderLayoutProps {
  id: string;
}

export function SaleOrderUpdateLayout() {
  // mover arriba
  const { id } = useParams<{id: string}>();

  return (
    <motion.div
      className='flex flex-col w-full pb-8'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <UpdateSaleOrderModule saleOrderId={id!}/>
      </motion.div>
    </motion.div>
  );
}
