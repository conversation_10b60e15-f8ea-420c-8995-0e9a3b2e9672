import { motion } from 'framer-motion';

import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import { SaleOrderListModule } from '#application/saleOrders/modules/SaleOrdersList.Module';
import LastQuarterSalesChart from '#application/stats/modules/LastQuarterSalesChart.Module';
import TopClientsSalesChart from '#application/stats/modules/TopClientsSalesChart.Module';
import TotalSalesChartModule from '#application/stats/modules/TotalSalesChart.Module';
import ApplicationRegistry from '#composition/Application.Registry';

export function SaleOrdersListLayout() {
  const addSaleOrderPath = ApplicationRegistry.PathService.saleOrders.addSaleOrder();

  return (
    <motion.div
      className='flex flex-col w-full pl-7 gap-4 pt-0'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="self-end"
      >
        <AddNewItemButton path={addSaleOrderPath} label='Agregar orden de venta'/>
      </motion.div>
      <div className='flex flex-wrap'>
        <TotalSalesChartModule />
        <LastQuarterSalesChart className='pl-4 lg:px-4' />
        <TopClientsSalesChart className='py-4 lg:py-0' />
      </div>
      <SaleOrderListModule />
    </motion.div>
  );
}
