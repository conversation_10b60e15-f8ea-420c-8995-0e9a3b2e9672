import { motion } from 'framer-motion';

import { CreateSaleOrderModule } from '#application/saleOrders/modules/CreateSaleOrder.Module';

export function CreateSaleOrderLayout() {
  return (
    <motion.div
      className='flex flex-col w-full pl-7 pb-8'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <CreateSaleOrderModule/>
      </motion.div>
    </motion.div>
  );
}
