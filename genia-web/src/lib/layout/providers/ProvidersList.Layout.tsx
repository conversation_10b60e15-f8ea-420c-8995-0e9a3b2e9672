import { motion } from 'framer-motion';

import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import { ProvidersListModule } from '#application/provider/modules/ProvidersList.Module';
import ApplicationRegistry from '#composition/Application.Registry';

export function ProvidersListLayout() {
  const addProviderPath = ApplicationRegistry.PathService.providers.addProvider();

  return (
    <motion.div
      className='flex flex-col w-full pl-7 gap-4 pt-0'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.3 }}
        className='self-end'
      >
        <AddNewItemButton label='Agregar Proveedor' path={addProviderPath}/>
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        <ProvidersListModule />
      </motion.div>
    </motion.div>
  );
}
