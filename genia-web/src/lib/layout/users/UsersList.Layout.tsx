import { motion } from 'framer-motion';

import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import { UsersListModule } from '#application/user/UserList.Module';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

export function UsersListLayout() {
  const addUserPath = ApplicationRegistry.PathService.users.addUser();
  const text = TextService.getText();

  return (
    <motion.div
      className='flex flex-col w-full pl-7 gap-4 pt-0'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        <AddNewItemButton label={text.user.inviteButton} path={addUserPath} />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        <UsersListModule />
      </motion.div>
    </motion.div>
  );
}
