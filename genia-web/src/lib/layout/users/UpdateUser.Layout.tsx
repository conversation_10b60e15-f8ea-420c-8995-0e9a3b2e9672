import { motion } from 'framer-motion';

import { UpdateUserModule } from '#application/user/modules/UpdateUser.Module';

export function UpdateUserLayout() {
  return (
    <motion.div
      className='flex flex-col w-full gap-4 pb-7 pl-8'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <UpdateUserModule />
      </motion.div>
    </motion.div>
  );
}
