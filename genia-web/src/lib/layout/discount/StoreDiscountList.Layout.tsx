import { motion } from 'framer-motion';

import { StoreDiscountsListModule } from '#application/discount/modules/StoreDiscountList.Module';

const StoreDiscountListLayout = () => (
  <motion.div
    className='flex flex-col w-full pl-7 gap-4 pt-0'
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2, duration: 0.4 }}
    >
      <StoreDiscountsListModule/>
    </motion.div>
  </motion.div>
);

export default StoreDiscountListLayout;
