import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';

import { CatalogDiscountFormModule } from '#application/discount/modules/CatalogDiscountForm.Module';

const CreateUpdateCatalogDiscountLayout = () => {
  const { id } = useParams();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <CatalogDiscountFormModule id={id} />
    </motion.div>
  );
};

export default CreateUpdateCatalogDiscountLayout;
