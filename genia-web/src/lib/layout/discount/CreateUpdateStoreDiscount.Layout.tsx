import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';

import { StoreDiscountFormModule } from '#application/discount/modules/StoreDiscountForm.Module';

const CreateUpdateStoreDiscount = () => {
  const { id } = useParams();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <StoreDiscountFormModule id={id} />
    </motion.div>
  );
};

export default CreateUpdateStoreDiscount;
