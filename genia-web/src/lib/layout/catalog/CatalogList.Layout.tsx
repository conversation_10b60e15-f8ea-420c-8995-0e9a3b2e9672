import { motion } from 'framer-motion';

import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import CatalogListModule from '#application/catalog/modules/CatalogList.Module';
import ApplicationRegistry from '#composition/Application.Registry';

function CatalogListLayout() {
  const addCatalogPath = ApplicationRegistry.PathService.catalog.addCatalog();
  return (
    <motion.div
      className='flex flex-col w-full pl-7 gap-4 pt-0'
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.3 }}
        className='self-end'
      >
        <AddNewItemButton label='Agregar Item al Catálogo' path={addCatalogPath} />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        <CatalogListModule/>
      </motion.div>
    </motion.div>
  );
}

export default CatalogListLayout;
