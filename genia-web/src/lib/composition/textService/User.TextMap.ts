export interface UserTextMap {
  title: string;
  assignedUser: string;
  user: string;
  admin: string;
  new: string;
  edit: string;
  inviteButton: string;
  phoneRequired: string;
  userInvitedSuccess: string;
  userUpdatedSuccess: string;
  errorLoadingUser: string;
  loadingUserData: string;
  phoneRequiredInline: string;
  invalidPhoneNumber: string;
  invalidEmail: string;
  confirmRoleChange: string;
  confirmRoleChangeMessage: string;
  confirmStatusChange: string;
  confirmStatusChangeMessage: string;
  activate: string;
  deactivate: string;
  cancel: string;
  confirm: string;
  userActivatedSuccess: string;
  userDeactivatedSuccess: string;
  errorChangingUserStatus: string;
  errorChangingUserRole: string;
  roleUpdatedSuccess: string;
  invitationInfo: string;
}

export const UserTextMap = {
  title: {
    spanish: 'Usuario',
  },
  assignedUser: {
    spanish: 'Asignado a',
  },
  user: {
    spanish: 'Usuario',
  },
  admin: {
    spanish: 'Administrador',
  },
  new: {
    spanish: 'Invitar usuario',
  },
  edit: {
    spanish: 'Editar  usuario',
  },
  inviteButton: {
    spanish: 'Invitar Usuario',
  },
  phoneRequired: {
    spanish: 'El telefono es requerido',
  },
  userInvitedSuccess: {
    spanish: 'Usuario invitado exitosamente',
  },
  userUpdatedSuccess: {
    spanish: 'Usuario actualizado exitosamente',
  },
  errorLoadingUser: {
    spanish: 'Error al cargar los datos del usuario',
  },
  loadingUserData: {
    spanish: 'Cargando datos del usuario...',
  },
  phoneRequiredInline: {
    spanish: 'El telefono es requerido',
  },
  invalidPhoneNumber: {
    spanish: 'Numero de telefono invalido',
  },
  invalidEmail: {
    spanish: 'Correo electronico invalido',
  },
  confirmRoleChange: {
    spanish: 'Confirmar cambio de rol',
  },
  confirmRoleChangeMessage: {
    spanish: 'Estas seguro de que deseas cambiar el rol del usuario a',
  },
  confirmStatusChange: {
    spanish: 'Confirmar cambio de estado',
  },
  confirmStatusChangeMessage: {
    spanish: 'Estas seguro de que deseas',
  },
  activate: {
    spanish: 'activar',
  },
  deactivate: {
    spanish: 'desactivar',
  },
  cancel: {
    spanish: 'Cancelar',
  },
  confirm: {
    spanish: 'Confirmar',
  },
  userActivatedSuccess: {
    spanish: 'Usuario activado exitosamente',
  },
  userDeactivatedSuccess: {
    spanish: 'Usuario desactivado exitosamente',
  },
  errorChangingUserStatus: {
    spanish: 'Error al cambiar el estado del usuario',
  },
  errorChangingUserRole: {
    spanish: 'Error al cambiar el rol del usuario',
  },
  roleUpdatedSuccess: {
    spanish: 'Rol de usuario actualizado exitosamente',
  },
  invitationInfo: {
    // eslint-disable-next-line max-len
    spanish: 'Una vez enviada la invitacion, el usuario recibira un enlace de registro a traves de WhatsApp o correo electronico para completar su registro en la plataforma.',
  },
};
