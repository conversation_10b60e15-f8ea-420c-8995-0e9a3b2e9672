export interface InventoryTextMap {
  title: string;
  searchPlaceholder: string;
  stockDangerTooltip: string;
  stockEmptyTooltip: string;
  stockWarningTooltip: string;
  dangerBadgeStatus: string;
  stockValidationDisabled: string;
  automaticUpdate: string;
  update: string;
  newItem: string;
  quantity: string;
  addItem: string;
  addInventoryFailed: string;
  addInventorySuccess: string;
  updateInventoryFailed: string;
  updateInventorySuccess: string;
  historyModificationWarning: string;
  save: string;
  reasonPlaceholder: string;
}

export const InventoryTextMap = {
  title: {
    spanish: 'Inventario',
  },
  searchPlaceholder: {
    spanish: 'Buscar por SKU, nombre, descripción...',
  },
  stockDangerTooltip: {
    spanish: 'Alerta: Tienes pocas unidades disponibles. Compra de inmediato.',
  },
  stockEmptyTooltip: {
    spanish: 'Sin stock. Compra de inmediato.',
  },
  stockWarningTooltip: {
    spanish: 'Advertencia: Tienes pocas unidades disponibles. Compra pronto.',
  },
  dangerBadgeStatus: {
    spanish: 'Compra ya',
  },
  stockValidationDisabled: {
    spanish: 'Validación de stock deshabilitada',
  },
  automaticUpdate: {
    spanish: 'Actualizacion automatica de inventario',
  },
  update: {
    spanish: 'Actualizacion de inventario',
  },
  newItem: {
    spanish: 'Nuevo item de inventario',
  },
  quantity: {
    spanish: 'Cantidad',
  },
  addItem: {
    spanish: 'Agregar ítem',
  },
  addInventoryFailed: {
    spanish: 'Error al agregar el inventario',
  },
  addInventorySuccess: {
    spanish: 'Inventario agregado exitosamente',
  },
  updateInventoryFailed: {
    spanish: 'Error al actualizar el inventario',
  },
  updateInventorySuccess: {
    spanish: 'Inventario actualizado exitosamente',
  },
  historyModificationWarning: {
    spanish: 'Las modificaciones de inventario tienen un impacto operativo. Solo debes guardar si estás seguro de los cambios',
  },
  save: {
    spanish: 'Guardar',
  },
  reasonPlaceholder: {
    spanish: 'Ej: Ajuste de inventario, devolución de proveedor, etc.',
  },
};
