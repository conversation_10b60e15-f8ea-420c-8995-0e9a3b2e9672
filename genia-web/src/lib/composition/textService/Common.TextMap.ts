export interface CommonTextMap {
  name: string;
  loading: string;
  loadingWithEllipsis: string;
  logout: string;
  formFieldsRequired: string;
  loadingErrorMsg: string;
  loadingErrorNotification: string;
  temporarilyUnavailable: string;
  noMatchesFoundHeader: string;
  noMatchesFoundBody: string;
  minimumSearchLength: string;
  noDataToSearch: string;
  filterNoMatchesFound: string;
  saving: string;
  edit: string;
  save: string;
  select: string;
  autoGenerate: string;
  requiredField: string;
  actions: string;
  search: string;
  loadMore: string;
  properties: string;
  status: string;
  state: string;
  price: string;
  description: string;
  type: string;
  value: string;
  id: string;
  user: string;
  assignedUser: string;
  creationDate: string;
  lastUpdate: string;
  startDate: string;
  endDate: string;
  add: string;
  delete: string;
  connectionSuccessful: string;
  connectionSuccessfulDescription: string;
  connectionError: string;
  connectionErrorDescription: string;
  connected: string;
  connecting: string;
  disconnected: string;
  connect: string;
  disconnect: string;
  close: string;
}

export const CommonTextMap = {
  name: {
    spanish: 'Nombre',
  },
  loading: {
    spanish: 'Cargando',
  },
  loadingWithEllipsis: {
    spanish: 'Cargando...',
  },
  logout: {
    spanish: 'Salir',
  },
  formFieldsRequired: {
    spanish: 'Todos los campos son requeridos',
  },
  loadingErrorMsg: {
    spanish: 'Upss! Algo salió mal... intenta más tarde',
  },
  loadingErrorNotification: {
    spanish: 'No se encontraron datos',
  },
  temporarilyUnavailable: {
    spanish: 'Función temporalmente no disponible',
  },
  noMatchesFoundHeader: {
    spanish: 'No hay resultados para:',
  },
  noMatchesFoundBody: {
    spanish: 'Por favor, intenta con otro término de búsqueda',
  },
  minimumSearchLength: {
    spanish: 'El término debe tener al menos un carácter',
  },
  noDataToSearch: {
    spanish: 'No hay datos en la tabla para realizar la búsqueda',
  },
  filterNoMatchesFound: {
    spanish: 'No hay resultados para el filtro seleccionado',
  },
  saving: {
    spanish: 'Guardando',
  },
  edit: {
    spanish: 'Editar',
  },
  save: {
    spanish: 'Guardar',
  },
  select: {
    spanish: 'Seleccionar',
  },
  autoGenerate: {
    spanish: 'Generar automáticamente',
  },
  requiredField: {
    spanish: 'Campo requerido',
  },
  actions: {
    spanish: 'Acciones',
  },
  search: {
    spanish: 'Buscar por nombre, sku o descripción...',
  },
  loadMore: {
    spanish: 'Cargar más',
  },
  properties: {
    spanish: 'Propiedades',
  },
  status: {
    spanish: 'Status',
  },
  state: {
    spanish: 'Estado',
  },
  price: {
    spanish: 'Precio',
  },
  description: {
    spanish: 'Descripción',
  },
  type: {
    spanish: 'Tipo',
  },
  value: {
    spanish: 'Valor',
  },
  id: {
    spanish: 'Id',
  },
  user: {
    spanish: 'Usuario',
  },
  assignedUser: {
    spanish: 'Usuario asignado',
  },
  creationDate: {
    spanish: 'Fecha de creación',
  },
  lastUpdate: {
    spanish: 'Última actualización',
  },
  startDate: {
    spanish: 'Fecha de inicio',
  },
  endDate: {
    spanish: 'Fecha de fin',
  },
  add: {
    spanish: 'Agregar',
  },
  delete: {
    spanish: 'Eliminar',
  },
  connectionSuccessful: {
    spanish: 'La conexión fue exitosa, ya puedes cerrar esta ventana',
  },
  connectionSuccessfulDescription: {
    spanish: 'La conexión se ha establecido correctamente',
  },
  connectionError: {
    spanish: 'Ocurrió un error en la conexión, intenta de nuevo o contacta al equipo de soporte',
  },
  connectionErrorDescription: {
    spanish: 'No se pudo completar la conexión',
  },
  connected: {
    spanish: 'Conectado',
  },
  connecting: {
    spanish: 'Conectando',
  },
  disconnected: {
    spanish: 'Desconectado',
  },
  connect: {
    spanish: 'Conectar',
  },
  disconnect: {
    spanish: 'Desconectar',
  },
  close: {
    spanish: 'Cerrar',
  },
};
