export interface IntegrationTextMap {
  integrationLoadError: string;
  integrationError: string;
  loadingIntegrations: string;
  disconnectError: string;
  availableIntegrations: string;
  availableIntegrationsDescription: string;
  apiConfiguration: string;
  apiConfigurationDescription: string;
  whatsappBusinessToken: string;
  whatsappPhoneNumberId: string;
  whatsappTokenPlaceholder: string;
  phoneIdPlaceholder: string;
  saveConfiguration: string;
  gmailDescription: string;
  whatsappDescription: string;
  whatsappVerificationToken: string;
  whatsappWebhook: string;
  whatsappWebhookDescription: string;
  configureParametersToSeeUrl: string;
}
export const IntegrationTextMap = {
  integrationLoadError: {
    spanish: 'Ha ocurrido un error al cargar las integraciones, recarga la página o intenta más tarde, si el problema persiste contacta al soporte técnico.',
  },
  integrationError: {
    spanish: 'Error de integración',
  },
  loadingIntegrations: {
    spanish: 'Estamos cargando las integraciones, por favor espera un momento.',
  },
  disconnectError: {
    spanish: 'Error en la desconexión',
  },
  availableIntegrations: {
    spanish: 'Integraciones Disponibles',
  },
  availableIntegrationsDescription: {
    spanish: 'Conecta tus herramientas favoritas para potenciar tu agente de IA',
  },
  apiConfiguration: {
    spanish: 'Configuración de API de WhatsApp Business',
  },
  apiConfigurationDescription: {
    spanish: 'Configura las credenciales para la integración.',
  },
  whatsappBusinessToken: {
    spanish: 'Token de autenticación de WhatsApp Business',
  },
  whatsappPhoneNumberId: {
    spanish: 'ID de número de teléfono de WhatsApp',
  },
  whatsappTokenPlaceholder: {
    spanish: 'Tu token de autenticación de WhatsApp Business',
  },
  phoneIdPlaceholder: {
    spanish: 'ID del número de teléfono',
  },
  saveConfiguration: {
    spanish: 'Guardar configuración',
  },
  gmailDescription: {
    spanish: 'Conecta tu cuenta de Gmail para que biizi pueda enviar y leer correos.',
  },
  whatsappDescription: {
    spanish: 'Conecta tu cuenta de WhatsApp Business para interactuar con tus clientes.',
  },
  whatsappVerificationToken: {
    spanish: 'Token de verificación de WhatsApp Business',
  },
  whatsappWebhook: {
    spanish: 'Webhook de WhatsApp',
  },
  whatsappWebhookDescription: {
    spanish: 'URL para recibir mensajes de WhatsApp. Debes agregar esta URL en tu configuración de WhatsApp Business.',
  },
  configureParametersToSeeUrl: {
    spanish: 'Configura los parámetros para ver tu URL',
  },
};
