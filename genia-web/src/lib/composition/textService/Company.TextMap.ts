export interface CompanyTextMap {
  title: string;
  profile: string;
  information: string;
  name: string;
  // email: string; // TODO: Uncomment when email field is added to schema
  phone: string;
  address: string;
  website: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  noInformation: string;
}

export const CompanyTextMap = {
  title: {
    spanish: 'Compañía',
  },
  profile: {
    spanish: 'Perfil de la Compañía',
  },
  information: {
    spanish: 'Información de la Compañía',
  },
  name: {
    spanish: 'Nombre',
  },
  // email: {
  //   spanish: 'Correo Electrónico',
  // }, // TODO: Uncomment when email field is added to schema
  phone: {
    spanish: 'Teléfono',
  },
  address: {
    spanish: 'Dirección',
  },
  website: {
    spanish: 'Sitio Web',
  },
  description: {
    spanish: 'Descripción',
  },
  createdAt: {
    spanish: 'Fecha de Creación',
  },
  updatedAt: {
    spanish: 'Última Actualización',
  },
  noInformation: {
    spanish: 'no esta disponible',
  },
};
