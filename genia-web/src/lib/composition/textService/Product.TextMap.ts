export interface ProductTextMap {
  selection: string;
  number: string;
  product: string;
  addNew: string;
  measurement: string;
  stock: string;
  sku: string;
  readId: string;
  attributes: string;
  noAttributes: string;
  productType: string;
  requiresStock: string;
  searchSku: string;
}

export const ProductTextMap = {
  selection: {
    spanish: 'Seleccionar producto',
  },
  number: {
    spanish: 'Número de producto',
  },
  product: {
    spanish: 'Producto',
  },
  addNew: {
    spanish: 'Agregar nuevo producto',
  },
  measurement: {
    spanish: 'Unidad de Medida',
  },
  stock: {
    spanish: 'Stock',
  },
  sku: {
    spanish: 'SKU',
  },
  readId: {
    spanish: 'Identificador',
  },
  attributes: {
    spanish: 'Atributos',
  },
  noAttributes: {
    spanish: 'Sin atributos',
  },
  productType: {
    spanish: 'Tipo de Producto',
  },
  requiresStock: {
    spanish: 'Requiere Stock',
  },
  searchSku: {
    spanish: 'Buscar SKU',
  },
};
