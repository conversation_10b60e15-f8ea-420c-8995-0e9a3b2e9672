export interface MediaTextMap {
  processing: string;
  remove: string;
  processingTitle: string;
  processingMessage: string;
  noImages: string;
  processingImageError: string;
  cover: string;
  coverDescription: string;
  coverPlaceholder: string;
  select: string;
}

export const MediaTextMap = {
  processing: {
    spanish: 'Procesando...',
  },
  remove: {
    spanish: 'Se removerá al guardar',
  },
  processingTitle: {
    spanish: '¡Tus imágenes están casi listas!',
  },
  processingMessage: {
    spanish: 'Las estamos procesando para asegurar la mejor calidad. Por favor, actualiza la página en unos segundos para verlas.',
  },
  noImages: {
    spanish: 'No hay imágenes',
  },
  processingImageError: {
    spanish: 'Error al procesar las imágenes',
  },
  cover: {
    spanish: 'Portada',
  },
  coverDescription: {
    spanish: 'Selecciona la imagen que será la portada de tu producto',
  },
  coverPlaceholder: {
    spanish: 'Selecciona la imagen que será la portada de tu producto',
  },
  select: {
    spanish: 'Seleccionar',
  },
};
