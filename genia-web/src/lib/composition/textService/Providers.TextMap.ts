export interface ProvidersTextMap {
  provider: string;
  providers: string;
  catalog: string;
  totalItemsProvided: string;
  noItemsProvided: string;
  noProvidersTitle: string;
  noProvidersBody: string;
  noProductsTitle: string;
  noProductsBody: string;
  title: string;
  newProviderTitle: string;
  editProviderTitle: string;
  propertiesLabel: string;
  saveBtnLabel: string;
  connectBtnLabel: string;
  inviteBtnLabel: string;
  continueBtnLabel: string;
  duplicateProviderNameError: string;
  providerAddSuccess: string;
  providerAddFailed: string;
  providerUpdateSuccess: string;
  providerUpdateFailed: string;
  providerConnectSuccess: string;
  providerConnectFailed: string;
  providerInviteSuccess: string;
  providerInviteFailed: string;
  stepTitleTributaryId: string;
  stepTitleProviderDetails: string;
  stepDescriptionTributaryId: string;
  stepDescriptionProviderDetails: string;
  labelName: string;
  labelTributaryId: string;
  labelPhone: string;
  labelAddress: string;
  labelNotificationEmail: string;
  labelManagerName: string;
  labelMedia: string;
  placeholderName: string;
  placeholderTributaryId: string;
  placeholderPhone: string;
  placeholderAddress: string;
  placeholderNotificationEmail: string;
  placeholderManagerName: string;
  tooltipName: string;
  tooltipTributaryId: string;
  tooltipPhone: string;
  tooltipAddress: string;
  tooltipNotificationEmail: string;
  tooltipManagerName: string;
  tooltipMedia: string;
  emailPhoneRequiredError: string;
  stepLabelTributaryId: string;
  stepLabelContactInfo: string;
  stepLabelCommercialInfo: string;
  whatIsTributaryIdTitle: string;
  whatIsTributaryIdDescription: string;
  validatingTributaryId: string;
  providerFoundMessage: string;
  providerNotFoundMessage: string;
  createWithoutInviteLabel: string;
  contactInfoTitle: string;
  companyInfoTitle: string;
  fiscalAddressTitle: string;
  tributaryIdSectionTitle: string;
  inviteContactDescription: string;
  providerInvitationTitle: string;
  providerInvitationDescription: string;
  newProviderDescription: string;
  backButtonLabel: string;
  createProviderLabel: string;
}

export const ProvidersTextMap = {
  provider: {
    spanish: 'Proveedor',
  },
  providers: {
    spanish: 'Proveedores',
  },
  catalog: {
    spanish: 'Catálogo de proveedores',
  },
  totalItemsProvided: {
    spanish: 'Total de artículos suministrados',
  },
  noItemsProvided: {
    spanish: 'Sin artículos suministrados',
  },
  noProvidersTitle: {
    spanish: 'Aún no tienes proveedores',
  },
  noProvidersBody: {
    spanish: 'Para ver la tienda de proveedores, primero debes agregar uno. Ve a la sección de Proveedores para comenzar.',
  },
  noProductsTitle: {
    spanish: 'Este proveedor no tiene productos',
  },
  noProductsBody: {
    spanish: 'El proveedor seleccionado aún no tiene productos en su catálogo.',
  },
  title: {
    spanish: 'Proveedor',
  },
  newProviderTitle: {
    spanish: 'Nuevo Proveedor',
  },
  editProviderTitle: {
    spanish: 'Editar Proveedor',
  },
  propertiesLabel: {
    spanish: 'Propiedades',
  },
  saveBtnLabel: {
    spanish: 'Guardar',
  },
  connectBtnLabel: {
    spanish: 'Conectar',
  },
  inviteBtnLabel: {
    spanish: 'Invitar',
  },
  continueBtnLabel: {
    spanish: 'Continuar',
  },
  duplicateProviderNameError: {
    spanish: 'Ya existe un proveedor con ese nombre, por favor ingresa uno diferente',
  },
  providerAddSuccess: {
    spanish: 'Proveedor agregado con éxito',
  },
  providerAddFailed: {
    spanish: 'Error al agregar el proveedor',
  },
  providerUpdateSuccess: {
    spanish: 'Proveedor actualizado con éxito',
  },
  providerUpdateFailed: {
    spanish: 'Error al actualizar proveedor',
  },
  providerConnectSuccess: {
    spanish: 'Proveedor conectado con éxito',
  },
  providerConnectFailed: {
    spanish: 'Error al conectar con el proveedor',
  },
  providerInviteSuccess: {
    spanish: 'Invitación enviada con éxito',
  },
  providerInviteFailed: {
    spanish: 'Error al enviar la invitación',
  },
  stepTitleTributaryId: {
    spanish: 'Identificación del Proveedor',
  },
  stepTitleProviderDetails: {
    spanish: 'Detalles del Proveedor',
  },
  stepDescriptionTributaryId: {
    spanish: 'Ingresa el ID tributario del proveedor para verificar si ya está registrado en la plataforma',
  },
  stepDescriptionProviderDetails: {
    spanish: 'Completa la información comercial y de contacto del proveedor',
  },
  labelName: {
    spanish: 'Nombre de la Empresa',
  },
  labelTributaryId: {
    spanish: 'ID Tributario',
  },
  labelPhone: {
    spanish: 'Teléfono',
  },
  labelAddress: {
    spanish: 'Dirección',
  },
  labelNotificationEmail: {
    spanish: 'Email de notificaciones',
  },
  labelManagerName: {
    spanish: 'Nombre del encargado',
  },
  labelMedia: {
    spanish: 'Logo de la Empresa',
  },
  placeholderName: {
    spanish: 'Ej: Distribuidora ABC S.A. de C.V.',
  },
  placeholderTributaryId: {
    spanish: 'Ej: ABC123456789 (sin espacios)',
  },
  placeholderPhone: {
    spanish: 'Ej: +52 55 1234 5678',
  },
  placeholderAddress: {
    spanish: 'Ej: Av. Insurgentes Sur 123, Col. Roma Norte, CDMX, CP 06700',
  },
  placeholderNotificationEmail: {
    spanish: 'Ej: <EMAIL>',
  },
  placeholderManagerName: {
    spanish: 'Ej: María González López',
  },
  tooltipName: {
    spanish: 'Razón social o nombre comercial completo de la empresa proveedora',
  },
  tooltipTributaryId: {
    spanish: 'ID tributario o identificador fiscal según las leyes de tu país. Sin espacios ni guiones. Ejemplo: ABC123456789',
  },
  tooltipPhone: {
    spanish: 'Número telefónico principal para contacto comercial. Incluye código de país si es internacional',
  },
  tooltipAddress: {
    spanish: 'Dirección fiscal completa donde está registrada la empresa. Incluye calle, número, colonia, ciudad y código postal',
  },
  tooltipNotificationEmail: {
    spanish: 'Correo electrónico donde se enviarán facturas, órdenes de compra y notificaciones importantes del sistema',
  },
  tooltipManagerName: {
    spanish: 'Nombre completo del representante legal o persona autorizada para tomar decisiones comerciales',
  },
  tooltipMedia: {
    spanish: 'Logo oficial de la empresa en formato PNG o JPG. Se mostrará en documentos y reportes',
  },
  emailPhoneRequiredError: {
    spanish: 'Email y telefono son requeridos para enviar la invitacion',
  },
  stepLabelTributaryId: {
    spanish: 'ID Tributario',
  },
  stepLabelContactInfo: {
    spanish: 'Informacion de Contacto',
  },
  stepLabelCommercialInfo: {
    spanish: 'Informacion Comercial',
  },
  whatIsTributaryIdTitle: {
    spanish: 'Que es el ID Tributario?',
  },
  whatIsTributaryIdDescription: {
    spanish: 'Es el ID tributario o identificador fiscal oficial de la empresa segun las leyes de tu pais. '
      + 'Este numero nos permite verificar si el proveedor ya esta registrado en nuestra plataforma.',
  },
  validatingTributaryId: {
    spanish: 'Verificando ID tributario...',
  },
  providerFoundMessage: {
    spanish: 'Proveedor encontrado',
  },
  providerNotFoundMessage: {
    spanish: 'Proveedor no encontrado. Puedes crear uno nuevo o enviar una invitacion.',
  },
  createWithoutInviteLabel: {
    spanish: 'Crear sin invitar',
  },
  contactInfoTitle: {
    spanish: 'Informacion de Contacto',
  },
  companyInfoTitle: {
    spanish: 'Informacion de la Empresa',
  },
  fiscalAddressTitle: {
    spanish: 'Direccion Fiscal',
  },
  tributaryIdSectionTitle: {
    spanish: 'ID Tributario',
  },
  inviteContactDescription: {
    spanish: 'Proporciona el email y telefono para enviar la invitacion al proveedor',
  },
  providerInvitationTitle: {
    spanish: 'Invitacion de Proveedor',
  },
  providerInvitationDescription: {
    spanish: 'Se enviara una invitacion al proveedor para que se registre en la plataforma. '
      + 'Una vez que complete su registro, podras acceder a su catalogo y realizar pedidos.',
  },
  newProviderDescription: {
    spanish: 'Se creara el proveedor directamente en tu sistema. Podras comenzar a agregar productos a su catalogo inmediatamente.',
  },
  backButtonLabel: {
    spanish: 'Atras',
  },
  createProviderLabel: {
    spanish: 'Crear Proveedor',
  },
};
