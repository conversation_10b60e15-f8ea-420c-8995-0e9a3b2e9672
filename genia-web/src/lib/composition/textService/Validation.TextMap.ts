export interface ValidationTextMap {
  idRequired: string;
  nameRequired: string;
  skuRequired: string;
  priceRequired: string;
  descriptionRequired: string;
  invalidPrice: string;
  lengthDescriptionFailed: string;
  stockValueError: string;
  quiantityRequired: string;
}

export const ValidationTextMap = {
  idRequired: {
    spanish: 'El identificador es requerido',
  },
  nameRequired: {
    spanish: 'El nombre es requerido',
  },
  skuRequired: {
    spanish: 'El SKU es requerido',
  },
  priceRequired: {
    spanish: 'El precio es requerido',
  },
  descriptionRequired: {
    spanish: 'La descripcion es requerida',
  },
  invalidPrice: {
    spanish: 'El precio no puede ser menor a 0',
  },
  lengthDescriptionFailed: {
    spanish: 'La descripcion no puede ser mayor a 500 caracteres',
  },
  stockValueError: {
    spanish: 'El stock no puede ser menor a 0',
  },
  quiantityRequired: {
    spanish: 'La cantidad es requerida',
  },
};
