export interface DiscountsTextMap {
  storeDiscounts: string;
  catalogDiscounts: string;
  relationError: string;
  addFailed: string;
  updateSuccess: string;
  addSuccess: string;
  responseError: string;
  existsError: string;
}

export const DiscountsTextMap = {
  storeDiscounts: {
    spanish: 'Descuentos de Tienda',
  },
  catalogDiscounts: {
    spanish: 'Descuentos de Catálogo',
  },
  relationError: {
    spanish: 'Primero debe asociar un catálogo',
  },
  addFailed: {
    spanish: 'Error al agregar el descuento',
  },
  updateSuccess: {
    spanish: 'Descuento de catalogo actualizado',
  },
  addSuccess: {
    spanish: 'Descuento de catalogo agregado exitosamente',
  },
  responseError: {
    spanish: 'Error en la respuesta',
  },
  existsError: {
    spanish: 'El descuento ya existe',
  },
};
