import { DashboardTextMap } from '#composition/textService/Dashboard.TextMap';
import { IntegrationTextMap } from '#composition/textService/Integration.TextMap';
import { SettingsTextMap } from '#composition/textService/Settings.TextMap';

import { CatalogTextMap } from './Catalog.TextMap';
import { ClientsTextMap } from './Clients.TextMap';
import { CommonTextMap } from './Common.TextMap';
import { CompanyTextMap } from './Company.TextMap';
import { DiscountsTextMap } from './Discounts.TextMap';
import { ErrorTextMap } from './Error.TextMap';
import { InventoryTextMap } from './Inventory.TextMap';
import { MediaTextMap } from './Media.TextMap';
import { NotificationsTextMap } from './Notifications.TextMap';
import { OrdersTextMap } from './Orders.TextMap';
import { ProductTextMap } from './Product.TextMap';
import { ProvidersTextMap } from './Providers.TextMap';
import { PurchaseOrdersTextMap } from './PurchaseOrders.TextMap';
import { ResultsTextMap } from './Results.TextMap';
import { SaleOrdersTextMap } from './SaleOrders.TextMap';
import { ShippingTextMap } from './Shipping.TextMap';
import { SummaryTextMap } from './Summary.TextMap';
import { TaxesTextMap } from './Taxes.TextMap';
import { UserTextMap } from './User.TextMap';
import { ValidationTextMap } from './Validation.TextMap';

const unLocalizedTextMap = {
  common: CommonTextMap,
  clients: ClientsTextMap,
  company: CompanyTextMap,
  providers: ProvidersTextMap,
  orders: OrdersTextMap,
  saleOrders: SaleOrdersTextMap,
  product: ProductTextMap,
  summary: SummaryTextMap,
  shipping: ShippingTextMap,
  inventory: InventoryTextMap,
  catalog: CatalogTextMap,
  discounts: DiscountsTextMap,
  results: ResultsTextMap,
  validation: ValidationTextMap,
  error: ErrorTextMap,
  purchaseOrders: PurchaseOrdersTextMap,
  taxes: TaxesTextMap,
  user: UserTextMap,
  media: MediaTextMap,
  notifications: NotificationsTextMap,
  integration: IntegrationTextMap,
  settings: SettingsTextMap,
  dashboard: DashboardTextMap,
};

export interface TextMap {
  common: CommonTextMap;
  clients: ClientsTextMap;
  company: CompanyTextMap;
  orders: OrdersTextMap;
  saleOrders: SaleOrdersTextMap;
  purchaseOrders: PurchaseOrdersTextMap;
  product: ProductTextMap;
  summary: SummaryTextMap;
  shipping: ShippingTextMap;
  inventory: InventoryTextMap;
  catalog: CatalogTextMap;
  discounts: DiscountsTextMap;
  results: ResultsTextMap ;
  providers: ProvidersTextMap;
  validation: ValidationTextMap;
  user: UserTextMap;
  error: ErrorTextMap;
  taxes: TaxesTextMap;
  media: MediaTextMap;
  notifications: NotificationsTextMap;
  integration: IntegrationTextMap;
  settings: SettingsTextMap;
  dashboard: DashboardTextMap
}

const localizedText = Object.entries(unLocalizedTextMap).reduce((textMap, [section, texts]) => {
  const localizedTexts = Object.entries(texts).reduce((localizedTextMap, [text, localization]) => {
    const newLocalizedTextMap = localizedTextMap;
    newLocalizedTextMap[text] = localization.spanish;

    return localizedTextMap;
  }, {} as Record<string, string>);

  const newTextMap = textMap;

  newTextMap[section] = localizedTexts;

  return newTextMap;
}, {} as Record<string, Record<string, string>>);

function getText(): TextMap {
  return localizedText as unknown as TextMap;
}

export default {
  getText,
};
