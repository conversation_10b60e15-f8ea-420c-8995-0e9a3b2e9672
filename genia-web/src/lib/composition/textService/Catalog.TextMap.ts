export interface CatalogTextMap {
  title: string;
  searchPlaceholder: string;
  discounts: string;
  addCatalogFailed: string;
  addCatalogSuccess: string;
  updateCatalogFailed: string;
  updateCatalogSuccess: string;
  priceWithoutIva: string;
  updateCatalogPiece: string;
  newCatalogPiece: string;
}

export const CatalogTextMap = {
  title: {
    spanish: 'Catálogo',
  },
  searchPlaceholder: {
    spanish: 'Buscar por nombre, descripción, identificador...',
  },
  discounts: {
    spanish: 'Descuentos de Catálogo',
  },
  addCatalogFailed: {
    spanish: 'Error al agregar el catálogo',
  },
  addCatalogSuccess: {
    spanish: 'Catálogo agregado exitosamente',
  },
  updateCatalogFailed: {
    spanish: 'Error al actualizar en el catálogo',
  },
  updateCatalogSuccess: {
    spanish: 'Catálogo actualizado exitosamente',
  },
  priceWithoutIva: {
    spanish: 'Introduce el precio del producto sin IVA en formato numérico.',
  },
  updateCatalogPiece: {
    spanish: 'Actualizar pieza de catálogo',
  },
  newCatalogPiece: {
    spanish: 'Nueva pieza de catálogo',
  },
};
