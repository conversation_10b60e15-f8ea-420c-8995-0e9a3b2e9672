export interface ClientsTextMap {
  client: string;
  clients: string;
  addClient: string;
  clientCompanyName: string;
  noCompanyName: string;
  tributaryId: string;
  noTributaryId: string;
  addFailed: string;
  updateFailed: string;
  updateSuccess: string;
  addSuccess: string;
  edit: string;
  // Form labels
  mainInformation: string;
  mainInformationDescription: string;
  clientType: string;
  clientTypeDescription: string;
  personOption: string;
  companyOption: string;
  contactPersonName: string;
  firstName: string;
  lastName: string;
  companyName: string;
  tributaryIdLabel: string;
  tributaryIdPlaceholder: string;
  clientAddress: string;
  clientAddressPlaceholder: string;
  email: string;
  emailPlaceholder: string;
  phone: string;
  phonePlaceholder: string;
  isWhatsApp: string;
  whatsappNumber: string;
  whatsappPlaceholder: string;
  location: string;
  locationPlaceholder: string;
  alternativeContacts: string;
  alternativeContactsDescription: string;
  billing: string;
  purchases: string;
  emailLabel: string;
  phoneLabel: string;
  whatsappLabel: string;
  notes: string;
  notesDescription: string;
  notesPlaceholder: string;
  fullName: string;
  contactPersonPlaceholder: string;
  firstNamePlaceholder: string;
  lastNamePlaceholder: string;
  companyNamePlaceholder: string;
}

export const ClientsTextMap = {
  client: {
    spanish: 'Cliente',
  },
  clients: {
    spanish: 'Clientes',
  },
  addClient: {
    spanish: 'Agrega un cliente',
  },
  clientCompanyName: {
    spanish: 'Nombre de la Empresa',
  },
  noCompanyName: {
    spanish: 'Sin empresa asociada',
  },
  tributaryId: {
    spanish: 'ID Tributario',
  },
  noTributaryId: {
    spanish: 'Sin ID tributario',
  },
  addFailed: {
    spanish: 'Error al agregar el cliente',
  },
  updateFailed: {
    spanish: 'Error al actualizar el cliente',
  },
  updateSuccess: {
    spanish: 'Cliente actualizado con éxito',
  },
  addSuccess: {
    spanish: 'Cliente agregado con éxito',
  },
  edit: {
    spanish: 'Editar Cliente',
  },
  // Form labels
  mainInformation: {
    spanish: 'Información Principal',
  },
  mainInformationDescription: {
    spanish: 'Datos principales del cliente',
  },
  clientType: {
    spanish: 'Tipo de cliente',
  },
  clientTypeDescription: {
    spanish: 'Selecciona si es una persona o empresa',
  },
  personOption: {
    spanish: 'Persona',
  },
  companyOption: {
    spanish: 'Empresa',
  },
  contactPersonName: {
    spanish: 'Nombre de encargado',
  },
  firstName: {
    spanish: 'Nombre',
  },
  lastName: {
    spanish: 'Apellido',
  },
  companyName: {
    spanish: 'Nombre de la empresa',
  },
  tributaryIdLabel: {
    spanish: 'ID Tributario',
  },
  tributaryIdPlaceholder: {
    spanish: 'Ingresa el ID tributario de la empresa',
  },
  clientAddress: {
    spanish: 'Dirección del cliente',
  },
  clientAddressPlaceholder: {
    spanish: 'Dirección completa',
  },
  email: {
    spanish: 'Email principal',
  },
  emailPlaceholder: {
    spanish: '<EMAIL>',
  },
  phone: {
    spanish: 'Teléfono / WhatsApp principal',
  },
  phonePlaceholder: {
    spanish: '+52 ************',
  },
  isWhatsApp: {
    spanish: '¿Este número es también WhatsApp?',
  },
  whatsappNumber: {
    spanish: 'Número de WhatsApp',
  },
  whatsappPlaceholder: {
    spanish: '+52 ************',
  },
  location: {
    spanish: 'Ubicación',
  },
  locationPlaceholder: {
    spanish: 'Ciudad, Estado, País',
  },
  alternativeContacts: {
    spanish: 'Información de contacto por area de negocio',
  },
  alternativeContactsDescription: {
    spanish: `
      Puedes agregar email, teléfono y WhatsApp alternativos para Cobranza y Compras. 
      Si algún contacto alternativo no está definido, se usará el contacto principal del cliente.
    `,
  },
  billing: {
    spanish: 'Cobranza',
  },
  purchases: {
    spanish: 'Compras',
  },
  emailLabel: {
    spanish: 'Email',
  },
  phoneLabel: {
    spanish: 'Teléfono',
  },
  whatsappLabel: {
    spanish: 'WhatsApp',
  },
  notes: {
    spanish: 'Notas',
  },
  notesDescription: {
    spanish: 'Información adicional sobre el cliente',
  },
  notesPlaceholder: {
    spanish: 'Escribe notas adicionales sobre el cliente...',
  },
  fullName: {
    spanish: 'Nombre completo',
  },
  contactPersonPlaceholder: {
    spanish: 'Nombre del encargado',
  },
  firstNamePlaceholder: {
    spanish: 'Nombre',
  },
  lastNamePlaceholder: {
    spanish: 'Apellido',
  },
  companyNamePlaceholder: {
    spanish: 'Nombre de la empresa',
  },
};
