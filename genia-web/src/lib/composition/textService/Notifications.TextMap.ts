export interface NotificationsTextMap {
  title: string;
  noNotifications: string;
  moreNotifications: string;
  accepting: string;
  accept: string;
  rejecting: string;
  reject: string;
  invitacionProcesada: string;
}

export const NotificationsTextMap = {
  title: {
    spanish: 'Notificaciones',
  },
  noNotifications: {
    spanish: 'No hay notificaciones',
  },
  moreNotifications: {
    spanish: 'mas notificaciones',
  },
  accepting: {
    spanish: 'Aceptando...',
  },
  accept: {
    spanish: 'Aceptar',
  },
  rejecting: {
    spanish: 'Rechazando...',
  },
  reject: {
    spanish: '<PERSON><PERSON><PERSON>',
  },
  invitacionProcesada: {
    spanish: 'Invitacion procesada',
  },
};
