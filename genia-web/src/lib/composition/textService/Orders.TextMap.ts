export interface OrdersTextMap {
  order: string;
  orders: string;
  orderNumber: string;
  orderDate: string;
  orderStatus: string;
  orderTotal: string;
  orderItems: string;
  orderDetails: string;
  orderSummary: string;
  orderHistory: string;
  quantity: string;
  unitPrice: string;
  unitPriceShort: string;
  discountPrice: string;
  discountPriceShort: string;
  total: string;
  unitaryTotal: string;
  deliveryDate: string;
  notes: string;
  notesPlaceholder: string;
  orderNumberPlaceholder: string;
  notesLabel: string;
  notesAdditional: string;
  notesInstructions: string;
  notesInternal: string;
  tooltipDeliveryDate: string;
  tooltipNotes: string;
  statusClientApproval: string;
  statusApprovedByClient: string;
  statusPending: string;
  statusProcessing: string;
  statusInTransit: string;
  statusCompleted: string;
  statusCancelled: string;
  statusInReview: string;
  errorMissingInventory: string;
  errorSave: string;
  errorDuplicatedOrderNumber: string;
  errorDeliveryDateSaleOrder: string;
  errorDeliveryDatePurchaseOrder: string;
  loadError: string;
  orderUpdated: string;
  errorRepeatedItems: string;
  itemDiscounts: string;
  itemSummary: string;
  orderCreated: string;
  noInventoryRelations: string;
  unitPriceAfterDiscountError: string;
  DUPLICATED_ITEM: string;
}

export const OrdersTextMap = {
  order: {
    spanish: 'Orden',
  },
  orders: {
    spanish: 'Órdenes',
  },
  orderNumber: {
    spanish: 'Número de orden',
  },
  orderDate: {
    spanish: 'Fecha de orden',
  },
  orderStatus: {
    spanish: 'Estado de la orden',
  },
  orderTotal: {
    spanish: 'Total de la orden',
  },
  orderItems: {
    spanish: 'Artículos de la orden',
  },
  orderDetails: {
    spanish: 'Detalles de la orden',
  },
  orderSummary: {
    spanish: 'Resumen de la orden',
  },
  orderHistory: {
    spanish: 'Historial de órdenes',
  },
  quantity: {
    spanish: 'Cantidad',
  },
  unitPrice: {
    spanish: 'Precio unitario',
  },
  unitPriceShort: {
    spanish: 'Precio unit.',
  },
  discountPrice: {
    spanish: 'Precio con descuento',
  },
  discountPriceShort: {
    spanish: 'Precio c/desc.',
  },
  total: {
    spanish: 'Total',
  },
  unitaryTotal: {
    spanish: 'Total unitario',
  },
  deliveryDate: {
    spanish: 'Fecha de entrega',
  },
  notes: {
    spanish: 'Notas',
  },
  notesPlaceholder: {
    spanish: 'Ingrese notas adicionales aquí...',
  },
  orderNumberPlaceholder: {
    spanish: 'EJ: SALE-0001',
  },
  notesLabel: {
    spanish: 'Nota:',
  },
  notesAdditional: {
    spanish: 'Notas adicionales',
  },
  notesInstructions: {
    spanish: 'Instrucciones especiales',
  },
  notesInternal: {
    spanish: 'Notas internas',
  },
  tooltipDeliveryDate: {
    spanish: 'Fecha en la que se entregará la orden',
  },
  tooltipNotes: {
    spanish: 'Agrega notas para guardar información adicional relevante para la orden como instrucciones especiales, requerimientos o comentarios importantes',
  },
  statusClientApproval: {
    spanish: 'Aprobación del cliente',
  },
  statusApprovedByClient: {
    spanish: 'Aprobado por el cliente',
  },
  statusPending: {
    spanish: 'Pendiente',
  },
  statusProcessing: {
    spanish: 'Procesando',
  },
  statusInTransit: {
    spanish: 'En tránsito',
  },
  statusCompleted: {
    spanish: 'Completada',
  },
  statusCancelled: {
    spanish: 'Cancelada',
  },
  statusInReview: {
    spanish: 'En revisión',
  },
  errorMissingInventory: {
    spanish: 'Todos los productos deben tener un inventario seleccionado',
  },
  errorSave: {
    spanish: 'Error al guardar la orden',
  },
  errorDuplicatedOrderNumber: {
    spanish: 'El número de orden ya existe',
  },
  errorDeliveryDateSaleOrder: {
    spanish: 'No puedes modificar la fecha de entrega de una orden vinculada a una orden de venta',
  },
  errorDeliveryDatePurchaseOrder: {
    spanish: 'No puedes modificar la fecha de entrega de una orden vinculada a una orden de compra',
  },
  loadError: {
    spanish: 'Error al cargar la orden',
  },
  orderUpdated: {
    spanish: 'Orden actualizada exitosamente',
  },
  orderCreated: {
    spanish: 'Orden creada exitosamente',
  },
  errorRepeatedItems: {
    spanish: 'No puedes agregar el mismo producto más de una vez a la orden',
  },
  itemDiscounts: {
    spanish: 'Descuentos del producto',
  },
  itemSummary: {
    spanish: 'Resumen del producto',
  },
  noInventoryRelations: {
    spanish: 'No hay piezas de inventario asociadas',
  },
  unitPriceAfterDiscountError: {
    spanish: 'No puede ser mayor que el precio unitario',
  },
  DUPLICATED_ITEM: {
    spanish: 'Elemento duplicado',
  },
  UNIT_PRICE_REQUIRED: {
    spanish: 'Precio unitario es requerido',
  },
  NAME_REQUIRED: {
    spanish: 'Nombre es requerido',
  },
  PRODUCT_NUMBER_REQUIRED: {
    spanish: 'Número de producto es requerido',
  },
  QUANTITY_REQUIRED: {
    spanish: 'Cantidad es requerida',
  },
  UNIT_PRICE_AFTER_DISCOUNT_REQUIRED: {
    spanish: 'Precio con descuento es requerido',
  },
  UNIT_PRICE_AFTER_DISCOUNT_INVALID: {
    spanish: 'Precio con descuento no puede ser mayor que el precio unitario',
  },
};
