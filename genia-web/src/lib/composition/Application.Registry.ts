import ClientRepository from '#/lib/biizi/application/Client/repositories/Client.Repository';
import IntegrationRepository from '#/lib/biizi/application/Integration/repositories/Integration.Repository';
import IntegrationConnectionService from '#/lib/biizi/application/Integration/services/IntegrationConnection.Service';
import { CatalogService } from '#application/catalog/services/Catalog.Service';
import { ClientService } from '#application/client/services/Client.Service';
import { CountriesService } from '#application/common/services/Countries.Service';
import { IdService } from '#application/common/services/Id.Service';
import { MediaService } from '#application/common/services/Media.Service';
import PathService from '#application/common/services/Path.Service';
import { TimeService } from '#application/common/services/Time.Service';
import { CompanyService } from '#application/company/services/Company.Service';
import { CatalogDiscountService } from '#application/discount/services/CatalogDiscount.Service';
import { StoreDiscountService } from '#application/discount/services/StoreDiscount.Service';
import { InventoryService } from '#application/inventory/services/Inventory.Service';
import { ProviderService } from '#application/provider/services/Provider.Service';
import { PurchaseOrderService } from '#application/purchaseOrders/services/PurchaseOrder.Service';
import { SaleOrderService } from '#application/saleOrders/services/SaleOrder.Service';
import { StatsService } from '#application/stats/services/Stats.Service';
import { TaxesService } from '#application/taxes/services/Taxes.Service';
import { UserService } from '#application/user/services/User.Service';
import { AuthContext } from '#infrastructure/AuthState.Context';
import ReadModelCatalogService from '#infrastructure/implementation/application/catalog/ReadModelCatalog.Service';
import ReadModelClientService from '#infrastructure/implementation/application/client/ReadModelClient.Service';
import ServerClientRepository from '#infrastructure/implementation/application/client/ServerClient.Repository';
import CoreIdService from '#infrastructure/implementation/application/common/CoreId.Service';
import CoreMediaService from '#infrastructure/implementation/application/common/CoreMedia.Service';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import CoreTimeService from '#infrastructure/implementation/application/common/CoreTime.Service';
import ReadModelCountriesService from '#infrastructure/implementation/application/common/ReadModelCountries.Service';
import ReadModelCompanyService from '#infrastructure/implementation/application/company/ReadModelCompany.Service';
import ReadModelCatalogDiscountService from '#infrastructure/implementation/application/discount/ReadModelCatalogDiscount.Service';
import ReadModelStoreDiscountService from '#infrastructure/implementation/application/discount/ReadModelStoreDiscounts.Service';
import CoreIntegrationConnectionService from '#infrastructure/implementation/application/integration/CoreIntegrationConnection.Service';
import ServerIntegrationRepository from '#infrastructure/implementation/application/integration/ServerIntegration.Repository';
import ReadModelInventoryService from '#infrastructure/implementation/application/inventory/ReadModelInventory.Service';
import ReadModelProviderService from '#infrastructure/implementation/application/provider/ReadModelProvider.Service';
import CorePurchaseOrderService from '#infrastructure/implementation/application/purchaseOrder/CorePurchaseOrder.Service';
import CoreSaleOrderService from '#infrastructure/implementation/application/saleOrder/CoreSaleOrder.Service';
import ReadModelStatsService from '#infrastructure/implementation/application/stats/ReadModelStats.Service';
import ReadModelTaxesService from '#infrastructure/implementation/application/taxes/ReadModelTaxes.Service';
import ReadModelUserOrderService from '#infrastructure/implementation/application/user/User.Service';
import { CartContext } from '#infrastructure/pages/app/pageContexts/CartState.Context';
import { InventoryContext } from '#infrastructure/pages/app/pageContexts/Inventory.Context';
import { StoreProviderContext } from '#infrastructure/pages/app/pageContexts/StoreProvider.Context';

export interface Registry {
  PathService: PathService;
  ClientService: ClientService;
  ProviderService: ProviderService;
  InventoryService: InventoryService;
  PurchaseOrderService: PurchaseOrderService;
  SaleOrderService: SaleOrderService;
  CompanyService: CompanyService;
  CatalogService: CatalogService;
  CatalogDiscountService: CatalogDiscountService;
  StoreDiscountService: StoreDiscountService;
  TaxesService: TaxesService;
  AuthContext: typeof AuthContext;
  CartContext: typeof CartContext;
  StoreProviderContext: typeof StoreProviderContext;
  InventoryContext: typeof InventoryContext;
  TimeService: TimeService;
  IdService: IdService;
  StatsService: StatsService;
  UsersService: UserService;
  MediaService: MediaService;
  CountriesService: CountriesService;
  IntegrationRepository: IntegrationRepository;
  IntegrationConnectionService: IntegrationConnectionService; // Assuming this is the correct type for integration connection
  ClientRepository: ClientRepository;
}

const ApplicationRegistry: Registry = {
  PathService: CorePath,
  ClientService: ReadModelClientService,
  ProviderService: ReadModelProviderService,
  InventoryService: ReadModelInventoryService,
  PurchaseOrderService: CorePurchaseOrderService,
  SaleOrderService: CoreSaleOrderService,
  CompanyService: ReadModelCompanyService,
  CatalogService: ReadModelCatalogService,
  CatalogDiscountService: ReadModelCatalogDiscountService,
  StoreDiscountService: ReadModelStoreDiscountService,
  TaxesService: ReadModelTaxesService,
  AuthContext,
  CartContext,
  StoreProviderContext,
  InventoryContext,
  TimeService: CoreTimeService,
  IdService: CoreIdService,
  StatsService: ReadModelStatsService,
  UsersService: ReadModelUserOrderService,
  MediaService: CoreMediaService,
  CountriesService: ReadModelCountriesService,
  IntegrationRepository: ServerIntegrationRepository,
  IntegrationConnectionService: CoreIntegrationConnectionService,
  ClientRepository: ServerClientRepository,
};

export default ApplicationRegistry;
