import {
  AppState,
  Auth0Context, Auth0Provider,
  CacheLocation,
  User,
} from '@auth0/auth0-react';
import {
  createContext, FC, useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { InvitationsHttp } from '#infrastructure/api/http/Invitations.Http';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';
import { AUTH_ERROR_PATH, COMPANY_ERROR } from '#infrastructure/pages/login/SignIn.Constant';

const LOGIN_PATH = '/login';

const {
  AUTH0_DOMAIN,
  AUTH0_CLIENT_ID,
  AUTH0_AUDIENCE,
  AUTH0_SCOPE,
  AUTH0_CACHE_LOCATION,
  SUPLIFAI_CLAIMS_NAMESPACE,
} = GetEnv();

export interface SystemUser {
  userId: string;
  companyId: string;
}

type TAuthContext = {
  isAuthenticated: boolean;
  userInfo?: Partial<User>;
  logOut: () => Promise<void>;
  triggerLogIn: () => Promise<void>;
  isAuthenticating: boolean;
  getToken: () => Promise<string | void>;
  systemUser: SystemUser;
};

export const AuthContext = createContext<TAuthContext>({
  isAuthenticated: false,
  userInfo: {},
  isAuthenticating: false,
  logOut: () => Promise.resolve(),
  triggerLogIn: () => Promise.resolve(),
  getToken: () => Promise.resolve(''),
  systemUser: {
    userId: '',
    companyId: '',
  },
});

interface AuthProviderProps {
  children: React.ReactNode;
}

const InternalProvider: FC<AuthProviderProps> = ({ children }) => {
  const {
    loginWithRedirect, isAuthenticated, logout: logoutAuth0, isLoading, user, getAccessTokenSilently,
  } = useContext(Auth0Context);

  const [systemUser, setSystemUser] = useState<SystemUser>({
    userId: '',
    companyId: '',
  });

  // Revisar invitationId cuando el usuario se logee
  useEffect(() => {
    if (isAuthenticated) {
      const invitationId = localStorage.getItem('invitationId');

      if (!invitationId) return;

      if (invitationId) {
        const processInvitation = async () => {
          const token = await getAccessTokenSilently();
          await InvitationsHttp.signupInvitations(token, invitationId);
          localStorage.removeItem('invitationId');
        };

        processInvitation();
      }
    }
  }, [isAuthenticated, getAccessTokenSilently]);

  const [urlParams] = useSearchParams();
  const hasAuthenticationError = !!urlParams.get('error');
  const showError = !!urlParams.get('show_error');
  const host = window.location.origin;

  useEffect(() => {
    if (hasAuthenticationError) {
      logoutAuth0({
        logoutParams: {
          returnTo: `${host}${LOGIN_PATH}${AUTH_ERROR_PATH}`,
        },
      });
    }
  }, [hasAuthenticationError]);

  useEffect(() => {
    if (showError) {
      Notification({ message: COMPANY_ERROR, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [showError]);

  const functions = useMemo<Pick<TAuthContext, 'triggerLogIn' | 'logOut' | 'getToken'>>(() => ({
    async triggerLogIn() {
      await loginWithRedirect({
        appState: { returnTo: '/' },
      });
    },
    async logOut() {
      await logoutAuth0({
        logoutParams: {
          returnTo: `${host}${LOGIN_PATH}`,
        },
      });
    },
    async getToken() {
      const token = await getAccessTokenSilently({
        authorizationParams: {
          scope: AUTH0_SCOPE,
        },
      })
        .catch(() => logoutAuth0());

      if (token) {
        const decodedClaims = JSON.parse(atob(token.split('.')[1]));
        const claims = decodedClaims[SUPLIFAI_CLAIMS_NAMESPACE];

        if (claims && claims['company-id'] && claims['user-id']) {
          setSystemUser((prev) => {
            if (prev.companyId !== claims['company-id'] || prev.userId !== claims['user-id']) {
              return {
                userId: claims['user-id'],
                companyId: claims['company-id'],
              };
            }

            return prev;
          });
        }
      }

      return token;
    },
  }), []);

  return (
    <AuthContext.Provider value={{
      ...functions, isAuthenticated, isAuthenticating: isLoading, userInfo: user, systemUser,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const AuthProvider: FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate();

  const onRedirectCallback = (appState?: AppState) => {
    navigate(appState?.returnTo || window.location.pathname);
  };

  const [urlParams] = useSearchParams();

  const invitation = urlParams.get('invitation') || undefined;
  const organization = urlParams.get('organization') || undefined;

  return (
    <Auth0Provider
      domain={AUTH0_DOMAIN || ''}
      clientId={AUTH0_CLIENT_ID || ''}
      useRefreshTokens={true}
      cacheLocation={AUTH0_CACHE_LOCATION as CacheLocation}
      onRedirectCallback={onRedirectCallback}
      authorizationParams={{
        redirect_uri: `${window.location.origin}${LOGIN_PATH}`,
        audience: AUTH0_AUDIENCE,
        scope: AUTH0_SCOPE,
        invitation,
        organization,
      }}
    >
      <InternalProvider>{children}</InternalProvider>
    </Auth0Provider>
  );
};
