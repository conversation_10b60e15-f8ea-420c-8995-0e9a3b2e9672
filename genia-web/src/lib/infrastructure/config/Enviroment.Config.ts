import assert from 'assert';

export function GetEnv() {
  const {
    VITE_GENIA_NODE_API,
    VITE_AUTH0_DOMAIN,
    VITE_AUTH0_CLIENT_ID,
    VITE_AUTH0_AUDIENCE,
    VITE_AUTH0_SCOPE,
    VITE_GENIA_WSP_NUMBER,
    VITE_REQUEST_DATA_API,
    VITE_AUTH0_CACHE_LOCATION,
    VITE_PITSDEPOT_COMPANY_ID,
    VITE_CLAUDIA_NODE_API,
    VITE_SUPLIFAI_CLAIMS_NAMESPACE,
    VITE_GOOGLE_OAUTH_CLIENT_ID,
  } = import.meta.env;

  assert(VITE_GENIA_NODE_API, 'env var VITE_GENIA_NODE_API is required');
  assert(VITE_AUTH0_DOMAIN, 'env var VITE_AUTH0_DOMAIN is required');
  assert(VITE_AUTH0_CLIENT_ID, 'env var VITE_AUTH0_CLIENT_ID is required');
  assert(VITE_AUTH0_AUDIENCE, 'env var VITE_AUTH0_AUDIENCE is required');
  assert(VITE_AUTH0_SCOPE, 'env var VITE_AUTH0_SCOPE is required');
  assert(VITE_GENIA_WSP_NUMBER, 'env var VITE_GENIA_WSP_NUMBER is required');
  assert(VITE_REQUEST_DATA_API, 'env var VITE_REQUEST_DATA_API is required');
  assert(VITE_AUTH0_CACHE_LOCATION, 'env var VITE_AUTH0_CACHE_LOCATION is required');
  assert(VITE_PITSDEPOT_COMPANY_ID, 'env var VITE_PITSDEPOT_COMPANY_ID is required');
  assert(VITE_CLAUDIA_NODE_API, 'env var VITE_CLAUDIA_NODE_API is required');
  assert(VITE_SUPLIFAI_CLAIMS_NAMESPACE, 'env var VITE_SUPLIFAI_CLAIMS_NAMESPACE is required');
  assert(VITE_GOOGLE_OAUTH_CLIENT_ID, 'env var VITE_GOOGLE_OAUTH_CLIENT_ID is required');

  return {
    GENIA_NODE_API: VITE_GENIA_NODE_API,
    AUTH0_DOMAIN: VITE_AUTH0_DOMAIN,
    AUTH0_CLIENT_ID: VITE_AUTH0_CLIENT_ID,
    AUTH0_AUDIENCE: VITE_AUTH0_AUDIENCE,
    AUTH0_SCOPE: VITE_AUTH0_SCOPE,
    GENIA_WSP_NUMBER: VITE_GENIA_WSP_NUMBER,
    REQUEST_DATA_API: VITE_REQUEST_DATA_API,
    AUTH0_CACHE_LOCATION: VITE_AUTH0_CACHE_LOCATION,
    PITSDEPOT_COMPANY_ID: VITE_PITSDEPOT_COMPANY_ID,
    CLAUDIA_NODE_API: VITE_CLAUDIA_NODE_API,
    SUPLIFAI_CLAIMS_NAMESPACE: VITE_SUPLIFAI_CLAIMS_NAMESPACE,
    GOOGLE_OAUTH_CLIENT_ID: VITE_GOOGLE_OAUTH_CLIENT_ID,
  };
}
