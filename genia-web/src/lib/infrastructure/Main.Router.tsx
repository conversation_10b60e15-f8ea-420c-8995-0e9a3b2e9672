import { FC, ReactNode, useContext } from 'react';
import {
  BrowserRouter,
  Navigate,
  Route,
  Routes,
} from 'react-router-dom';

import { BiiziRouter } from '#/lib/biizi/ui/Biizi.Router';
import { ModuleLoaderAppComponent } from '#appComponent/common/components/moduleLoader/ModuleLoader.AppComponent';
import { AuthContext, AuthProvider } from '#infrastructure/AuthState.Context';
import { AppRoutes } from '#infrastructure/pages/app/App.Routes';
import AuthCallbackPage from '#infrastructure/pages/auth/AuthCallback.Page';
import { LoginPage } from '#infrastructure/pages/login/Login.Page';

type ProviderProps = {
  children: ReactNode;
};

type ProviderComponent = FC<ProviderProps>;

const composeProviders = (...providers: ProviderComponent[]): FC<ProviderProps> => ({ children }) => providers.reduceRight(
  (child, Provider) => <Provider>{child}</Provider>,
  children,
);

const Providers = composeProviders(AuthProvider);

function CurrentRoutes() {
  const { isAuthenticated, isAuthenticating } = useContext(AuthContext);

  if (isAuthenticating) {
    return <ModuleLoaderAppComponent />;
  }

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path='/login' element={<LoginPage/>} />
        <Route path='/integrations/auth/callback' element={<AuthCallbackPage />} />

        <Route path='*' element={<Navigate to='/login' />} />

      </Routes>
    );
  }
  return (
    <Routes>
      <Route path='/app/biizi/*' element={<BiiziRouter />} />
      <Route path='/app/*' element={<AppRoutes />} />
      <Route path='*' element={<Navigate to={'/app'} replace />} />
    </Routes>
  );
}

function MainRouter() {
  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
    >
      <Providers>
        <CurrentRoutes />
      </Providers>
    </BrowserRouter>
  );
}

export default MainRouter;
