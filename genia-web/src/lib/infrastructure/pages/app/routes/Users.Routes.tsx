import { Route, Routes } from 'react-router-dom';

import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { InviteUserLayout } from '#layout/users/InviteUser.Layout';
import { UpdateUserLayout } from '#layout/users/UpdateUser.Layout';
import { UsersListLayout } from '#layout/users/UsersList.Layout';

export function UsersRoutes() {
  const { users } = CorePath;

  return (
    <Routes>
      <Route index element={<UsersListLayout />} />
      <Route path={users.addUser(false).split('/').pop()} element={<InviteUserLayout />} />
      <Route path={users.viewUser(':id', false).split('/').pop()} element={<UpdateUserLayout />} />
    </Routes>
  );
}
