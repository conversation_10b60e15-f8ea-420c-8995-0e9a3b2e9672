import { Route, Routes } from 'react-router-dom';

import { ProvidersListLayout } from '#/lib/layout/providers/ProvidersList.Layout';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { AddProviderFormLayout } from '#layout/providers/AddProviderForm.Layout';
import { ProviderFormLayout } from '#layout/providers/ProviderForm.Layout';
import ProvidersCatalogDetailsLayout from '#layout/providers/ProvidersCatalogDetails.Layout';
import ProvidersCatalogListLayout from '#layout/providers/ProvidersCatalogList.Layout';

export function ProvidersRoutes() {
  const { providers } = CorePath;

  return (
    <Routes>
      <Route index element={<ProvidersListLayout />} />
      <Route path={providers.addProvider(false).split('/').pop()} element={<AddProviderFormLayout />} />
      <Route path={providers.viewProvider(':id', false).split('/').pop()} element={<ProviderFormLayout />} />
      <Route path={providers.viewProviderCatalog(':id', false).replace(/\/providers/g, '')} element={<ProvidersCatalogDetailsLayout />} />
      <Route path={providers.providerCatalogBase(false).split('/').pop()} element={<ProvidersCatalogListLayout />} />
    </Routes>
  );
}
