import { Route, Routes } from 'react-router-dom';

import { CreateUpdateInventoryLayout } from '#/lib/layout/inventory/CreateUpdateInventory.Layout';
import { InventoryListLayout } from '#/lib/layout/inventory/InventoryList.Layout';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { InventoryProvider } from '#infrastructure/pages/app/pageContexts/Inventory.Context';

export function InventoryRoutes() {
  const { inventory } = CorePath;

  return (
    <Routes>
      <Route index element={<InventoryListLayout />} />
      <Route path={inventory.addInventory(false).split('/').pop()} element={<CreateUpdateInventoryLayout />} />
      <Route path={inventory.viewInventory(':id', false).split('/').pop()}
        element=
          {
            <InventoryProvider>
              <CreateUpdateInventoryLayout />
            </InventoryProvider>
          }/>
    </Routes>
  );
}
