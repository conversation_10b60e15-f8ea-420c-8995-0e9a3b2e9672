import { Route, Routes } from 'react-router-dom';

import AddClientLayout from '#/lib/biizi/ui/layout/AddClient.Layout';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import ClientFormLayout from '#layout/clients/ClientForm.Layout';
import ClientsListLayout from '#layout/clients/ClientsList.Layout';

export function ClientsRoutes() {
  const { clients } = CorePath;

  return (
    <Routes>
      <Route index element={<ClientsListLayout />} />
      <Route path='new' element={<AddClientLayout />} />
      <Route path={clients.viewClient(':id', false).split('/').pop()} element={<ClientFormLayout />} />
    </Routes>
  );
}
