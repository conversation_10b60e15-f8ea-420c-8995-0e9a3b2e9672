import { Route, Routes } from 'react-router-dom';

import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { CreatePurchaseOrderLayoutProps } from '#layout/purchaseOrders/CreatePurchaseOrder.Layout';
import { PurchaseOrdersListLayout } from '#layout/purchaseOrders/PurchaseOrdersList.Layout';
import { UpdatePurchaseOrderLayout } from '#layout/purchaseOrders/UpdatePurchaseOrder.Layout';

export function PurchaseOrdersRoutes() {
  const { purchaseOrders } = CorePath;

  return (
    <Routes>
      <Route index element={<PurchaseOrdersListLayout />} />
      <Route path={purchaseOrders.addPurchaseOrder(false).split('/').pop()} element={<CreatePurchaseOrderLayoutProps />} />
      <Route path={purchaseOrders.viewPurchaseOrder(':id', false).split('/').pop()} element={<UpdatePurchaseOrderLayout />} />
      <Route path={purchaseOrders.addPurchaseOrderFromExternal(':from', false)} element={<CreatePurchaseOrderLayoutProps />} />
    </Routes>
  );
}
