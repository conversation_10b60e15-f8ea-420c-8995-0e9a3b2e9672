import { Route, Routes } from 'react-router-dom';

import CreateUpdateStoreDiscount from '#/lib/layout/discount/CreateUpdateStoreDiscount.Layout';
import StoreDiscountListLayout from '#/lib/layout/discount/StoreDiscountList.Layout';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';

export function StoreDiscountRoutes() {
  const { storeDiscounts } = CorePath;

  return (
    <Routes>
      <Route index element={<StoreDiscountListLayout />} />
      <Route path={storeDiscounts.addStoreDiscount(false).split('/').pop()} element={<CreateUpdateStoreDiscount />} />
      <Route path={storeDiscounts.viewStoreDiscount(':id', false).split('/').pop()} element={<CreateUpdateStoreDiscount />} />
    </Routes>
  );
}
