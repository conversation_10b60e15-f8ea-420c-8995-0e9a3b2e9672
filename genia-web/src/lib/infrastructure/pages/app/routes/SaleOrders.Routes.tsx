import { Route, Routes } from 'react-router-dom';

import ApplicationRegistry from '#composition/Application.Registry';
import { CreateSaleOrderLayout } from '#layout/saleOrders/CreateSaleOrder.Layout';
import { SaleOrdersListLayout } from '#layout/saleOrders/SaleOrdersList.Layout';
import { SaleOrderUpdateLayout } from '#layout/saleOrders/UpdateSaleOrder.Layout';

export function SaleOrdersRoutes() {
  const { saleOrders } = ApplicationRegistry.PathService;

  return (
    <Routes>
      <Route index element={<SaleOrdersListLayout />} />
      <Route path={saleOrders.addSaleOrder(false).split('/').pop()} element={<CreateSaleOrderLayout />} />
      <Route path='new/:from' element={<CreateSaleOrderLayout />} />
      <Route path='/:id' element={<SaleOrderUpdateLayout />} />
      <Route path='*' element={<SaleOrdersListLayout />} />
    </Routes>
  );
}
