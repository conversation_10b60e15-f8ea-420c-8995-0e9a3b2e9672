import { Route, Routes } from 'react-router-dom';

import CatalogDiscountListLayout from '#/lib/layout/discount/CatalogDiscountList.Layout';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import CreateUpdateCatalogDiscountLayout from '#layout/discount/CreateUpdateCatalogDiscount.Layout';

export function CatalogDiscountRoutes() {
  const { catalogDiscounts } = CorePath;

  return (
    <Routes>
      <Route index element={<CatalogDiscountListLayout />} />
      <Route path={catalogDiscounts.addCatalogDiscount(false).split('/').pop()} element={<CreateUpdateCatalogDiscountLayout />} />
      <Route path={catalogDiscounts.viewCatalogDiscount(':id', false).split('/').pop()} element={<CreateUpdateCatalogDiscountLayout />} />
    </Routes>
  );
}
