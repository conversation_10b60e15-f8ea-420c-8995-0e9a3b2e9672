import { Route, Routes } from 'react-router-dom';

import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import CatalogFormLayout from '#layout/catalog/CatalogForm.Layout';
import CatalogListLayout from '#layout/catalog/CatalogList.Layout';

export function CatalogRoutes() {
  const { catalog } = CorePath;

  return (
    <Routes>
      <Route index element={<CatalogListLayout />} />
      <Route path={catalog.addCatalog(false).split('/').pop()} element={<CatalogFormLayout />} />
      <Route path={catalog.viewCatalog(':id', false).split('/').pop()} element={<CatalogFormLayout />} />
    </Routes>
  );
}
