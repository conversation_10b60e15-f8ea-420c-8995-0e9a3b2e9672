import {
  Discount,
  ShoppingCart,
  ShoppingCartSummaryProps,
  Sidebar,
  SortAndMergeDiscounts,
} from '@pitsdepot/storybook';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  ReactNode,
  useContext, useEffect, useState,
} from 'react';
import {
  Link,
  Outlet,
  useLocation, useNavigate,
} from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import logo from '#/assets/logo.png';
import { AppNotificationModule } from '#application/notifications/AppNotification.Module';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { UserTextMap } from '#composition/textService/User.TextMap';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { useActiveProviderCart } from '#infrastructure/implementation/application/hooks/useActiveProviderCart.Hook';
import { stringToSentenceCase } from '#infrastructure/implementation/application/utils/stringToSentenceCase';
import 'dayjs/locale/es-mx';

dayjs.locale('es-mx');
dayjs.extend(utc);
dayjs.extend(timezone);

const { GENIA_WSP_NUMBER } = GetEnv();

const WSPMESSAGE = 'Hola, necesito ayuda con mi cuenta de Genia.';

const textService = TextService.getText();

const {
  catalog,
  catalogDiscounts,
  claudia,
  clients,
  company,
  inventory,
  store,
  providers,
  storeDiscounts,
  saleOrders,
  purchaseOrders,
  users,
  dashboard,
  settings,
} = CorePath;

const sidebarGroups = [
  {
    groupLabel: 'Espacio de trabajo',
    items: [
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: 'house' as const,
        to: dashboard.base(),
      },
      {
        id: 'store',
        label: 'Tienda',
        icon: 'storefront' as const,
        to: store.base(),
      },
      {
        id: 'biizi',
        label: 'Biizi',
        icon: 'brain' as const,
        to: claudia.base(),
      },
      {
        id: 'inventory',
        label: 'Inventario',
        icon: 'stack' as const,
        to: inventory.base(),
      },
      {
        id: 'catalog',
        label: 'Catálogo',
        icon: 'package' as const,
        to: catalog.base(),
      },
      {
        id: 'clients',
        label: 'Clientes',
        icon: 'addressBook' as const,
        to: clients.base(),
      },
      {
        id: 'providers',
        label: 'Proveedores',
        icon: 'factory' as const,
        subItems: [
          { id: 'my-providers', label: 'Mis Proveedores', to: providers.base() },
          { id: 'provider-catalog', label: 'Catálogo de Proveedores', to: providers.providerCatalogBase() },
        ],
      },
      {
        id: 'orders',
        label: 'Órdenes',
        icon: 'listChecks' as const,
        subItems: [
          { id: 'purchase-orders', label: 'Órdenes de Compra', to: purchaseOrders.base() },
          { id: 'sale-orders', label: 'Órdenes de Venta', to: saleOrders.base() },
        ],
      },
      {
        id: 'discounts',
        label: 'Descuentos',
        icon: 'tag' as const,
        subItems: [
          { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: catalogDiscounts.base() },
          { id: 'store-discounts', label: 'Descuentos de Tienda', to: storeDiscounts.base() },
        ],
      },
    ],
  },
  {
    groupLabel: 'Configuración',
    items: [
      {
        id: 'users',
        label: 'Usuarios',
        icon: 'users' as const,
        to: users.base(),
      },
      {
        id: 'company',
        label: 'Compañía',
        icon: 'buildings' as const,
        to: company.base(),
      },
      {
        id: 'settings',
        label: 'Configuración',
        icon: 'gear' as const,
        to: settings.base(),
      },
    ],
  },
];

export function AppPage() {
  const { logOut, userInfo } = useContext(AuthContext);
  const { user: currentUser } = ApplicationRegistry.UsersService.useGetCurrentUser();

  const name = currentUser?.name || userInfo?.name || 'Guest';
  const userPhoto = currentUser?.picture || userInfo?.picture || '';
  const userRolKey = stringToSentenceCase(currentUser?.role || userInfo?.rol || '');
  const userRol = textService.user[userRolKey.toLowerCase() as unknown as keyof UserTextMap] || userRolKey;
  const userEmail = currentUser?.email || userInfo?.email || '';
  const currentPath = useLocation().pathname;

  const {
    items,
    summary: {
      total, subtotal, totalTaxes, totalDiscount, subtotalBeforeDiscount,
    },
    isCartOpen,
    isLoading,
    cartFunctions,
  } = useActiveProviderCart();

  const [width, setWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  const {
    toggleCart, removeItem, incrementQuantity, decreaseQuantity, changeQuantity,
  } = cartFunctions;

  useEffect(() => {
    const handleResize = () => setWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (items?.length === 0) {
      toggleCart(false);
    } else {
      toggleCart(true);
    }
  }, [items]);

  const HeaderSection = () => (
    <div className="flex items-center justify-between gap-3 px-2 py-3 mb-2 rounded">
      <div className="flex items-center gap-2">
        <img src={logo} alt="Logo" className="w-full object-contain" />
      </div>

      <div className="flex items-center">
        <AppNotificationModule />
      </div>
    </div>
  );

  const LinkComponent = ({ to, children, className }: { to: string; children: ReactNode; className?: string }) => (
    <Link to={to} className={className}>
      {children}
    </Link>
  );

  const mappedItems = items.map((item) => ({
    ...item,
    id: item.referenceId,
    onRemove: () => removeItem(item.referenceId),
    onIncrement: () => incrementQuantity(item.referenceId),
    onDecrement: () => decreaseQuantity(item.referenceId),
    onQuantityChange: changeQuantity,
    applicableDiscounts: SortAndMergeDiscounts(item?.applicableDiscounts),
    appliedDiscount: item?.appliedDiscount?.catalogDiscount || item?.appliedDiscount?.storeDiscount as Discount,
  }));

  const cartSummary: ShoppingCartSummaryProps = {
    checkoutButton: {
      children: 'Crear orden',
      onClick: () => navigate(`${purchaseOrders.addPurchaseOrder()}/fromCart`),
    },
    total,
    subTotal: subtotal,
    taxesValue: totalTaxes,
    discountValue: totalDiscount,
    subtotalBeforeDiscount,
  };

  const [profileUserInfo, setProfileUserInfo] = useState({
    userName: '', userProfilePhoto: '', userRol: '', userEmail: '',
  });

  useEffect(() => {
    setProfileUserInfo({
      ...profileUserInfo, userName: name, userProfilePhoto: userPhoto, userRol, userEmail,
    });
  }, [name, userPhoto, userRol]);

  let sidebarWidth = '0px';
  if (width <= 2145) {
    sidebarWidth = isCartOpen ? '256px' : '0px';
  }

  return (
    <>
      <ToastContainer/>
      <div className='mi-h-screen flex'>
        <div className='pl-4 pr-4 w-full'>
          <div className='max-w-[1660px] mx-auto flex flex-col h-screen'>
            <div className='flex h-screen pb-[2rem] pt-[2rem] w-full bg-dark-200'>
              <Sidebar
                sidebarGroups={sidebarGroups}
                path={currentPath}
                className='bg-dark-300/25 rounded-lg h-full'
                LinkComponent={LinkComponent}
                profileUserInfo={profileUserInfo}
                logoutButton={{
                  onClick: logOut,
                  children: textService.common.logout,
                }}
                userMenuItems={[
                  {
                    id: '1',
                    label: 'Ayuda',
                    icon: 'question' as const,
                    onClick: () => window.open(
                      `https://api.whatsapp.com/send?phone=+${GENIA_WSP_NUMBER}&text=${WSPMESSAGE}`,
                      '_blank',
                    ),
                  },
                ]}
                headerSection={<HeaderSection />}
            />
              <div className='overflow-auto w-full' style={{ paddingRight: currentPath === store.base(true) ? `${sidebarWidth}` : '' }}>
                <Outlet />
              </div>
            </div>
          </div>
        </div>
        {currentPath === store.base() && (

        <ShoppingCart
          items={mappedItems}
          cartSummary={cartSummary}
          isOpen={isCartOpen}
          isLoading={isLoading}
            />
        )}
      </div>
    </>
  );
}
