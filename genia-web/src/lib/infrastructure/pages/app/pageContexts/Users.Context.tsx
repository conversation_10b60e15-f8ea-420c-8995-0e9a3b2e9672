import React, { createContext, useState } from 'react';

const defaultUsersContext = {
  refetchUsers: false,
  setRefetchUsers: () => null,
};

interface UsersContextProps {
  refetchUsers: boolean;
  setRefetchUsers: (refetch: boolean) => void;
}

export const UsersContext = createContext<UsersContextProps>(defaultUsersContext);

export const UsersProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [refetchUsers, setRefetchUsers] = useState(false);

  return (
    <UsersContext.Provider value={{
      refetchUsers,
      setRefetchUsers,
    }}>
      {children}
    </UsersContext.Provider>
  );
};
