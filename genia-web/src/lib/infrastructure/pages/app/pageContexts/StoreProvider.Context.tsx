import {
  createContext, FC, useEffect, useState,
} from 'react';

import { ProviderResponse } from '#application/provider/Provider.Type';
import { useGetCompanyId } from '#infrastructure/implementation/application/hooks/UseGetCompanyId.Hook';
import { useGetProviderByCompanyId } from '#infrastructure/implementation/application/hooks/useGetProvider.Hook';

export interface StoreProviderContext {
  storeProvider: ProviderResponse;
  setStoreProvider: (provider: ProviderResponse) => void;
}

const defaultStoreProvider: StoreProviderContext = {
  storeProvider: {
    id: '',
    name: '',
    tributaryId: '',
    companyId: '',
    providerCompanyId: '',
    totalItemsProvided: 0,
  },
  setStoreProvider: () => {},
};

interface StoreProviderProps {
  children: React.ReactNode;
}

export const StoreProviderContext = createContext<StoreProviderContext>(defaultStoreProvider);

export const StoreProviderProvider: FC<StoreProviderProps> = ({ children }) => {
  const [storeProvider, setStoreProviderState] = useState<ProviderResponse>(defaultStoreProvider.storeProvider);
  const { companyId } = useGetCompanyId();

  const { provider: defaultProvider } = useGetProviderByCompanyId({
    companyId,
    autoFetch: true,
  });

  // Initialize with default provider when it loads
  useEffect(() => {
    if (defaultProvider && storeProvider.id === '') {
      setStoreProviderState(defaultProvider);
    }
  }, [JSON.stringify(defaultProvider), storeProvider.id]);

  const setStoreProvider = (provider: ProviderResponse) => {
    setStoreProviderState(provider);
  };

  const contextValue: StoreProviderContext = {
    storeProvider,
    setStoreProvider,
  };

  return (
    <StoreProviderContext.Provider value={contextValue}>
      {children}
    </StoreProviderContext.Provider>
  );
};
