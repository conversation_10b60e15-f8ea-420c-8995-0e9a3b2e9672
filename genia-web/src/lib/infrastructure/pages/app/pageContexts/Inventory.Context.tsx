import React, { createContext, useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';

const defaultInventoryContext = {
  inventoryId: null,
  isEditingHistory: false,
  setIsEditingHistory: () => null,
  isSavingHistory: false,
  startEditingHistory: () => null,
  cancelEditingHistory: () => null,
  setIsSavingHistory: () => null,
};

interface InventoryContextProps {
  inventoryId: string | null;
  isEditingHistory: boolean;
  isSavingHistory: boolean;
  setIsEditingHistory: (editing: boolean) => void;
  startEditingHistory: () => void;
  cancelEditingHistory: () => void;
  setIsSavingHistory: (saving: boolean) => void;
  refetchInventory?: boolean;
  setRefetchInventory?: (refetch: boolean) => void;
}

export const InventoryContext = createContext<InventoryContextProps>(defaultInventoryContext);

export const InventoryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { id } = useParams();
  const [isEditingHistory, setIsEditingHistory] = useState(false);
  const [isSavingHistory, setIsSavingHistory] = useState(false);
  const [refetchInventory, setRefetchInventory] = useState(false);

  const startEditingHistory = useCallback(() => {
    setIsEditingHistory(true);
  }, []);

  const cancelEditingHistory = useCallback(() => {
    setIsEditingHistory(false);
  }, []);

  return (
    <InventoryContext.Provider value={{
      inventoryId: id || null,
      isEditingHistory,
      setIsEditingHistory,
      isSavingHistory,
      startEditingHistory,
      cancelEditingHistory,
      setIsSavingHistory,
      refetchInventory,
      setRefetchInventory,
    }}>
      {children}
    </InventoryContext.Provider>
  );
};
