import {
  createContext, FC,
  useEffect,
  useMemo,
  useReducer,
} from 'react';

import { ApplicableDiscounts, AppliedDiscount } from '#application/store/Store.Type';

interface CartProviderProps {
  children: React.ReactNode;
}

interface CartItem {
  referenceId: string;
  sku?: string;
  name: string;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  subtotal?: number;
  quantity: number;
  imageUrl?: string;
  appliedDiscount: AppliedDiscount;
  applicableDiscounts: ApplicableDiscounts;
  providerId: string; // Add providerId to track which provider this item belongs to
}

interface CartSummary {
  subtotalBeforeDiscount?: number;
  subtotal: number;
  total: number;
  totalDiscount?: number;
  totalTaxes?: number;
}

interface ProviderCart {
  items: CartItem[];
  summary: CartSummary;
  isLoading?: boolean;
}

interface CartState {
  providerCarts: Record<string, ProviderCart>; // Carts indexed by providerId
  activeProviderId: string; // Currently active provider
  isCartOpen: boolean;
}

const defaultProviderCart: ProviderCart = {
  items: [],
  summary: {
    subtotal: 0,
    total: 0,
  },
  isLoading: false,
};

const defaultCart: CartState = {
  providerCarts: {},
  activeProviderId: '',
  isCartOpen: false,
};

interface CartStateFunctions {
  addItem: (item: CartItem) => void;
  removeItem: (referenceId: string) => void;
  incrementQuantity: (referenceId: string) => void;
  decreaseQuantity: (referenceId: string) => void;
  changeQuantity: (referenceId: string, quantity: number) => void;
  resetCart: () => void;
  resetProviderCart: (providerId: string) => void; // Reset specific provider cart
  toggleCart: (isOpen: boolean) => void;
  updateSummary: (summary: CartSummary) => void;
  updateItemPrices: ({
    referenceId, unitPrice, unitPriceAfterDiscount, subtotal, appliedDiscount,
  }:
  { referenceId: string,
    unitPrice: number,
    unitPriceAfterDiscount: number,
    subtotal: number,
    appliedDiscount: AppliedDiscount }) => void;
  toggleLoading: (isLoading:boolean) => void;
  setActiveProvider: (providerId: string) => void; // Set active provider
}

interface CartContext {
  cartState: CartState;
  cartFunctions: CartStateFunctions;
}

type CartActions =
    | { type: 'ADD_ITEM'; payload: CartItem }
    | { type: 'REMOVE_ITEM'; payload: { referenceId: string; providerId: string } }
    | { type: 'INCREMENT_QUANTITY'; payload: { referenceId: string; providerId: string } }
    | { type: 'DECREASE_QUANTITY'; payload: { referenceId: string; providerId: string } }
    | { type: 'CHANGE_QUANTITY'; payload: { referenceId: string; quantity: number; providerId: string } }
    | { type: 'TOGGLE_CART'; payload: boolean }
    | { type: 'UPDATE_ITEM_PRICES'; payload:
    { referenceId: string, unitPrice: number, unitPriceAfterDiscount: number, subtotal: number, appliedDiscount: AppliedDiscount, providerId: string } }
    | { type: 'UPDATE_SUMMARY'; payload: { summary: CartSummary; providerId: string } }
    | { type: 'RESET_CART' }
    | { type: 'RESET_PROVIDER_CART'; payload: string }
    | { type: 'TOGGLE_LOADING'; payload: { isLoading: boolean; providerId: string } }
    | { type: 'SET_ACTIVE_PROVIDER'; payload: string }
    ;

const getProviderCart = (state: CartState, providerId: string): ProviderCart => state.providerCarts[providerId] || { ...defaultProviderCart };

// eslint-disable-next-line max-len
const updateQuantityInProviderCart = (items: CartItem[], referenceId: string, modifier: number) => items.map((item) => (item.referenceId === referenceId ? { ...item, quantity: Math.max(1, item.quantity + modifier) } : item));

function cartReducer(state: CartState, action: CartActions): CartState {
  switch (action.type) {
    case 'ADD_ITEM': {
      const { providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);
      const existingItem = providerCart.items.find((item) => item.referenceId === action.payload.referenceId);

      if (existingItem) {
        return {
          ...state,
          providerCarts: {
            ...state.providerCarts,
            [providerId]: {
              ...providerCart,
              items: providerCart.items.map((item) => (item.referenceId === action.payload.referenceId
                ? { ...item, quantity: item.quantity + 1 }
                : item)),
            },
          },
          activeProviderId: providerId,
        };
      }

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: [...providerCart.items, { ...action.payload, quantity: 1 }],
          },
        },
        activeProviderId: providerId,
      };
    }

    case 'REMOVE_ITEM': {
      const { referenceId, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: providerCart.items.filter((item) => item.referenceId !== referenceId),
          },
        },
      };
    }

    case 'INCREMENT_QUANTITY': {
      const { referenceId, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: updateQuantityInProviderCart(providerCart.items, referenceId, 1),
          },
        },
      };
    }

    case 'DECREASE_QUANTITY': {
      const { referenceId, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: updateQuantityInProviderCart(providerCart.items, referenceId, -1),
          },
        },
      };
    }

    case 'CHANGE_QUANTITY': {
      const { referenceId, quantity, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: providerCart.items.map((item) => (item.referenceId === referenceId
              ? { ...item, quantity }
              : item)),
          },
        },
      };
    }

    case 'UPDATE_ITEM_PRICES': {
      const {
        referenceId, unitPrice, unitPriceAfterDiscount, subtotal, appliedDiscount, providerId,
      } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            items: providerCart.items.map((item) => (item.referenceId === referenceId
              ? {
                ...item,
                unitPrice,
                unitPriceAfterDiscount,
                subtotal,
                appliedDiscount,
              }
              : item)),
          },
        },
      };
    }

    case 'UPDATE_SUMMARY': {
      const { summary, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            summary,
          },
        },
      };
    }

    case 'TOGGLE_CART':
      return {
        ...state,
        isCartOpen: action.payload,
      };

    case 'TOGGLE_LOADING': {
      const { isLoading, providerId } = action.payload;
      const providerCart = getProviderCart(state, providerId);

      return {
        ...state,
        providerCarts: {
          ...state.providerCarts,
          [providerId]: {
            ...providerCart,
            isLoading,
          },
        },
      };
    }

    case 'SET_ACTIVE_PROVIDER':
      return {
        ...state,
        activeProviderId: action.payload,
      };

    case 'RESET_PROVIDER_CART': {
      const providerId = action.payload;
      const newProviderCarts = { ...state.providerCarts };
      delete newProviderCarts[providerId];

      return {
        ...state,
        providerCarts: newProviderCarts,
      };
    }

    case 'RESET_CART':
      return defaultCart;

    default:
      return state;
  }
}

export const getInitialCartState = (): CartState => {
  const storedCart = localStorage.getItem('cart');
  try {
    const storedCartInitial = JSON.parse(storedCart || '{}');
    // Ensure the stored cart has the new structure
    if (storedCartInitial.providerCarts && storedCartInitial.activeProviderId !== undefined) {
      return {
        ...defaultCart,
        ...storedCartInitial,
      };
    }
    // If old structure, migrate or return default
    return defaultCart;
  } catch (error) {
    return defaultCart;
  }
};

export const useLocalStorageCart = (state: CartState) => {
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(state));
  }, [state]);
};

export const CartFunctionsContext: CartStateFunctions = {
  addItem: () => null,
  removeItem: () => null,
  incrementQuantity: () => null,
  decreaseQuantity: () => null,
  changeQuantity: () => null,
  toggleCart: () => null,
  updateItemPrices: () => null,
  updateSummary: () => null,
  resetCart: () => null,
  resetProviderCart: () => null,
  toggleLoading: () => null,
  setActiveProvider: () => null,
};

export const CartContext = createContext<CartContext>({
  cartState: {
    providerCarts: {},
    activeProviderId: '',
    isCartOpen: false,
  },
  cartFunctions: CartFunctionsContext,
});

export const CartProvider: FC<CartProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, getInitialCartState());

  const functions = useMemo<CartStateFunctions>(() => ({
    addItem(item: CartItem) {
      dispatch({ type: 'ADD_ITEM', payload: item });
    },
    removeItem(referenceId: string) {
      dispatch({ type: 'REMOVE_ITEM', payload: { referenceId, providerId: state.activeProviderId } });
    },
    incrementQuantity(referenceId: string) {
      dispatch({ type: 'INCREMENT_QUANTITY', payload: { referenceId, providerId: state.activeProviderId } });
    },
    decreaseQuantity(referenceId: string) {
      dispatch({ type: 'DECREASE_QUANTITY', payload: { referenceId, providerId: state.activeProviderId } });
    },
    changeQuantity(referenceId: string, quantity: number) {
      dispatch({ type: 'CHANGE_QUANTITY', payload: { referenceId, quantity, providerId: state.activeProviderId } });
    },
    toggleCart(isOpen: boolean) {
      dispatch({ type: 'TOGGLE_CART', payload: isOpen });
    },
    toggleLoading(isLoading: boolean) {
      dispatch({ type: 'TOGGLE_LOADING', payload: { isLoading, providerId: state.activeProviderId } });
    },
    updateItemPrices({
      referenceId, unitPrice, subtotal, appliedDiscount, unitPriceAfterDiscount,
    }) {
      dispatch({
        type: 'UPDATE_ITEM_PRICES',
        payload: {
          referenceId,
          unitPrice,
          unitPriceAfterDiscount,
          subtotal,
          appliedDiscount,
          providerId: state.activeProviderId,
        },
      });
    },
    updateSummary(summary: CartSummary) {
      dispatch({ type: 'UPDATE_SUMMARY', payload: { summary, providerId: state.activeProviderId } });
    },
    resetCart() {
      dispatch({ type: 'RESET_CART' });
    },
    resetProviderCart(providerId: string) {
      dispatch({ type: 'RESET_PROVIDER_CART', payload: providerId });
    },
    setActiveProvider(providerId: string) {
      dispatch({ type: 'SET_ACTIVE_PROVIDER', payload: providerId });
    },
  }), [state.activeProviderId]);

  useLocalStorageCart(state);

  const memoizedState = useMemo(() => ({ ...state }), [JSON.stringify(state)]);

  return (
    <CartContext.Provider value={{ cartState: memoizedState, cartFunctions: functions }}>
      {children}
    </CartContext.Provider>
  );
};
