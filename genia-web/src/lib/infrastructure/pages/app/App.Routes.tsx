import {
  useContext,
} from 'react';
import {
  Navigate,
  Route, Routes,
} from 'react-router-dom';

import DashboardLayout from '#/lib/biizi/ui/layout/Dashboard.Layout';
import SettingsLayout from '#/lib/biizi/ui/layout/Settings.Layout';
import ClaudiaLayout from '#/lib/layout/claudia/Claudia.Layout';
import StoreLayout from '#/lib/layout/store/Store.Layout';
import ApplicationRegistry from '#composition/Application.Registry';
import { ReadModelProvider } from '#infrastructure/api/readModel/context/ReadModel.Context';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { AppPage } from '#infrastructure/pages/app/App.Page';
import { CartProvider } from '#infrastructure/pages/app/pageContexts/CartState.Context';
import { StoreProviderProvider } from '#infrastructure/pages/app/pageContexts/StoreProvider.Context';
import { CatalogRoutes } from '#infrastructure/pages/app/routes/Catalog.Routes';
import { CatalogDiscountRoutes } from '#infrastructure/pages/app/routes/CatalogDiscount.Routes';
import { ClientsRoutes } from '#infrastructure/pages/app/routes/Clients.Routes';
import { CompanyRoutes } from '#infrastructure/pages/app/routes/Company.Routes';
import { InventoryRoutes } from '#infrastructure/pages/app/routes/Inventory.Routes';
import { ProvidersRoutes } from '#infrastructure/pages/app/routes/Providers.Routes';
import { PurchaseOrdersRoutes } from '#infrastructure/pages/app/routes/PurchaseOrders.Routes';
import { SaleOrdersRoutes } from '#infrastructure/pages/app/routes/SaleOrders.Routes';
import { StoreDiscountRoutes } from '#infrastructure/pages/app/routes/StoreDiscount.Routes';
import { UsersRoutes } from '#infrastructure/pages/app/routes/Users.Routes';
import ReactProvidersUtil from '#infrastructure/utils/ReactProviders.Util';

const ContextProviders = ReactProvidersUtil.composeProviders(ReadModelProvider, StoreProviderProvider, CartProvider);

export function AppRoutes() {
  const { isAuthenticated, logOut } = useContext(AuthContext);
  if (!isAuthenticated) {
    logOut();
  }

  const {
    inventory,
    catalog,
    clients,
    company,
    catalogDiscounts,
    providers,
    saleOrders,
    storeDiscounts,
    store,
    purchaseOrders,
    claudia,
    users,
    dashboard,
    settings,
  } = ApplicationRegistry.PathService;

  return (
    <ContextProviders>
      <Routes>
        <Route path='/' element={<AppPage />}>
          <Route index element={<Navigate to={store.base()} />} />
          <Route path={dashboard.base(false)} element={<DashboardLayout />} />
          <Route path={store.base(false)} element={<StoreLayout/>} />
          <Route path={`${inventory.base(false)}/*`} element={<InventoryRoutes />} />
          <Route path={`${catalog.base(false)}/*`} element={<CatalogRoutes />} />
          <Route path={`${catalogDiscounts.base(false)}/*`} element={<CatalogDiscountRoutes />} />
          <Route path={`${storeDiscounts.base(false)}/*`} element={<StoreDiscountRoutes />} />
          <Route path={`${clients.base(false)}/*`} element={<ClientsRoutes />} />
          <Route path={`${providers.base(false)}/*`} element={<ProvidersRoutes />} />
          <Route path={`${purchaseOrders.base(false)}/*`} element={<PurchaseOrdersRoutes />} />
          <Route path={`${saleOrders.base(false)}/*`} element={<SaleOrdersRoutes />} />
          <Route path={`${users.base(false)}/*`} element={<UsersRoutes />} />
          <Route path={`${company.base(false)}/*`} element={<CompanyRoutes />} />
          <Route path ={`${settings.base(false)}/*`} element={<SettingsLayout />} />

          <Route path={claudia.base(false)} element={<ClaudiaLayout/>} />
          <Route path="*" element={<Navigate to={store.base()} />} />
        </Route>
      </Routes>
    </ContextProviders>
  );
}
