import {
  Card, CardContent,
  IconImporter,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import TextService from '#composition/textService/Text.Service';

export default function AuthCallbackPage() {
  const [searchParams] = useSearchParams();
  const textService = TextService.getText();
  const [authState, setAuthState] = useState<'success' | 'error' | 'loading'>('loading');

  useEffect(() => {
    // Get URL parameters using useSearchParams
    const code = searchParams.get('code');
    const intentId = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      setAuthState('error');
      // Send error message to parent window
      if (window.opener) {
        window.opener.postMessage(
          { type: 'oauth-error', error },
          window.location.origin,
        );
      }
    } else if (code && window.opener && intentId) {
      setAuthState('success');
      // Send success message to parent window
      window.opener.postMessage(
        { type: 'oauth-code', intentId, code },
        window.location.origin,
      );
      // If no valid parameters, show error
      setAuthState('success');
    }
  }, [searchParams]);

  const renderContent = () => {
    if (authState === 'loading') {
      return (
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{textService.common.loading}</p>
        </div>
      );
    }

    if (authState === 'success') {
      return (
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-green-100 p-3">
              <IconImporter
                name="checkCircle"
                size={48}
                className="text-green-600"
              />
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            {textService.common.connectionSuccessfulDescription}
          </h2>
          <p className="text-gray-600">
            {textService.common.connectionSuccessful}
          </p>
        </div>
      );
    }

    // Error state
    return (
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="rounded-full bg-red-100 p-3">
            <IconImporter
              name="xCircle"
              size={48}
              className="text-red-600"
            />
          </div>
        </div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          {textService.common.connectionErrorDescription}
        </h2>
        <p className="text-gray-600">
          {textService.common.connectionError}
        </p>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md pt-6">
        <CardContent className="p-8">
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
}
