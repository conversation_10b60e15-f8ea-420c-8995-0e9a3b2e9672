import {
  Button,
  InfoTips,
  Title,
} from '@pitsdepot/storybook';
import { useContext } from 'react';

import imagotype from '#/assets/imagotype.png';
import Typewriter from '#appComponent/common/Typewriter.Component';
import TextService from '#composition/textService/Text.Service';
import { AuthContext } from '#infrastructure/AuthState.Context';
import {
  INFOTIPS_CONTENT,
  INFOTIPS_TITLE,
  LOGIN_LABEL,
  WELCOME,
  WELCOME_SUBTITLE_FIXED,
  WELCOME_SUBTITLE_ROTATING_WORDS,
} from '#infrastructure/pages/login/SignIn.Constant';

const text = TextService.getText();

export function LoginPage() {
  const { triggerLogIn, isAuthenticating } = useContext(AuthContext);

  async function handleLogin() {
    await triggerLogIn();
  }
  return isAuthenticating ? (
    <div className="flex items-center justify-center min-h-screen">
      <Title as="h1" size="xlg">
        {text.common.loading}
      </Title>
    </div>
  ) : (
    <div className="flex flex-col md:flex-row min-h-screen justify-center align-middle">
      <div className="flex flex-1 justify-center flex-col items-center bg-gray-700 py-20 text-white">
        <div className='flex flex-col items-start gap-8 w-3/5'>
          <div className='flex flex-1 justify-center items-center'>
            <img src={imagotype} alt="" />
          </div>
          <InfoTips
            title={INFOTIPS_TITLE}
            items={INFOTIPS_CONTENT}
            className="!bg-transparent !p-0 text-white"
          />
        </div>
      </div>
      <div className='flex flex-1 flex-col justify-center items-center bg-gradient-to-br from-yellow-50 to-amber-100 relative py-20'>
        <div className="absolute top-10 right-10 w-32 h-32 bg-yellow-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 left-10 w-24 h-24 bg-amber-200 rounded-full opacity-30 animate-bounce"></div>
        <div className="absolute top-1/3 right-1/4 w-16 h-16 bg-yellow-300 rounded-full opacity-25"></div>

        <div className="flex flex-col gap-8 w-4/5 max-w-lg z-10 text-center">
          <div className="space-y-4">
            <Title
              as="h1"
              size="xlg"
              color='black'
              className="!text-4xl !font-extrabold !leading-tight !text-gray-700"
            >
              {WELCOME}
            </Title>

            <div className="!text-4xl !font-extrabold !leading-tight text-[#FFD614] min-h-[2rem]">
              <Typewriter
                fixedText={WELCOME_SUBTITLE_FIXED}
                rotatingWords={WELCOME_SUBTITLE_ROTATING_WORDS}
              />
            </div>
          </div>

          <div className="space-y-4">
            <Button onClick={handleLogin}>{LOGIN_LABEL}</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
