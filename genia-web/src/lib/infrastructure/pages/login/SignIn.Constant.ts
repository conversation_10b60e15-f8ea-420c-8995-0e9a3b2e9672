import { InfoTipsItems } from '@pitsdepot/storybook';

import { IHttpErrorMap } from '#application/types/HttpErrorMap.Type';

export const SIGN_IN_ERROR = 'Error en autenticación';
export const LOGIN_LABEL = 'Ingresar';
export const FORGOT_LABEL = '¿Olvidaste tu contraseña?';
export const AUTH_USER = 'authUser';
export const SIGN_IN_ERROR_DESCRIPTION = 'Usuario es requerido';
export const ERROR_500 = 'Ha ocurrido un error';
export const INTERNAL_ERROR_DESCRIPTION = 'Se ha presentado un error inesperado, por favor intente nuevamente.';
export const ERROR_400 = 'Credenciales incorrectas';
export const BAD_DATA = 'No hemos podido iniciar sesión, revisa la información de tu cuenta.';
export const ERROR_401 = 'Error de autenticación';
export const NOT_AUTHENTICATED = 'Ya que no te encuentras autenticado. ingresa nuevamente.';
export const ERROR_403 = 'Error de autorización';
export const NOT_AUTHORIZED = 'No ha sido posible realizar la accción solicitada, revisa que tengas permiso para realizar esta acción.';
export const ERROR_404 = 'Cuenta no encontrada';
export const NOT_FOUND = 'No hemos podido iniciar sesión, revisa la información de tu cuenta.';
export const UNEXPECTED_ERROR_MESSAGE = 'Error Inesperado';
export const UNEXPECTED_ERROR_DESCRIPTION = 'Ha ocurrido un error inesperado cambiando la información de tu cuenta, comunícate con soporte técnico para resolverlo.';
export const COMPANY_ERROR = 'Debes pertenecer a una compañia para poder acceder a la aplicación, solicita acceso a tu administrador';

export const WELCOME = 'Estas a punto de adquirir el control de tu negocio';
export const WELCOME_SUBTITLE_FIXED = 'estás a un click de mejorar tu proceso de';
export const WELCOME_SUBTITLE_ROTATING_WORDS = ['ventas', 'compras', 'adquisición del cliente', 'manejo de inventario'];
export const INFOTIPS_TITLE = 'Opera más simple. Decide mejor. Crece con inteligencia.';

export const AUTH_ERROR_PATH = '?show_error=true';

export const ERROR_LIST: IHttpErrorMap = {
  500: {
    message: ERROR_500,
    description: INTERNAL_ERROR_DESCRIPTION,
  },
  400: {
    message: ERROR_400,
    description: BAD_DATA,
  },
  401: {
    message: ERROR_401,
    description: NOT_AUTHENTICATED,
  },
  403: {
    message: ERROR_403,
    description: NOT_AUTHORIZED,
  },
  404: {
    message: ERROR_404,
    description: NOT_FOUND,
  },
  default: {
    message: UNEXPECTED_ERROR_MESSAGE,
    description: UNEXPECTED_ERROR_DESCRIPTION,
  },
};

export const INFOTIPS_CONTENT: InfoTipsItems[] = [
  {
    id: '1',
    subtitle: 'Centraliza tu operación',
    text: 'Todo lo que necesitas para gestionar tu negocio en un solo lugar.',
    icon: 'buildings',
  },
  {
    id: '2',
    subtitle: 'Compra y vende sin fricción',
    text: 'Conecta con proveedores y clientes B2B en segundos.',
    icon: 'shoppingCart',
  },
  {
    id: '3',
    subtitle: 'Toma decisiones con inteligencia',
    text: 'Recibe asistencia de IA en cada parte del proceso.',
    icon: 'brain',
  },
  {
    id: '4',
    subtitle: 'Ahorra tiempo y errores',
    text: 'Automatiza tareas clave y enfócate en lo que importa.',
    icon: 'clock',
  },
];
