import {
  ApolloClient,
  createHttpLink, from,
  InMemoryCache,
} from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

import { GetEnv } from '#infrastructure/config/Enviroment.Config';
import InMemCacheUtil from '#infrastructure/utils/InMemCache.Util';

const { REQUEST_DATA_API } = GetEnv();

const httpLink = createHttpLink({
  uri: REQUEST_DATA_API,
});

const authLink = setContext(async (_, { headers }) => {
  const token = InMemCacheUtil.getToken();
  return {
    headers: {
      ...headers,
      Authorization: token ? `Bearer ${token}` : '',
    },
  };
});

const ReadModelClient = new ApolloClient({
  link: from([authLink, httpLink]),
  cache: new InMemoryCache(),
});

export default ReadModelClient;
