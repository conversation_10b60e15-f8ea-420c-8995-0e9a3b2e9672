import {
  ApolloClient,
  createHttpLink, from,
  InMemoryCache,
  NormalizedCacheObject,
} from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

import { GetEnv } from '#infrastructure/config/Enviroment.Config';

const { REQUEST_DATA_API } = GetEnv();

export const createClient = (getToken: () => Promise<string | void>): ApolloClient<NormalizedCacheObject> => {
  const httpLink = createHttpLink({
    uri: REQUEST_DATA_API,
  });

  const authLink = setContext(async (_, { headers }) => {
    const token = await getToken();
    return {
      headers: {
        ...headers,
        Authorization: token ? `Bearer ${token}` : '',
      },
    };
  });

  return new ApolloClient({
    link: from([authLink, httpLink]),
    cache: new InMemoryCache(),
  });
};
