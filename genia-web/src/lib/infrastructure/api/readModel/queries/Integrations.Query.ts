import { gql } from '@apollo/client';

import ReadModelClient from '#infrastructure/api/readModel/ReadModel.Client';

interface IntegrationQuery {
  integration: [
    {
      id: string;
      type: string;
      createdAt: string;
      updatedAt: string;
      params: Record<string, string>;
    }
  ]
}

const getIntegrationsQuery = gql`
  query GetIntegrations {
    integration {
      id
      type
      createdAt
      updatedAt
      params
    }
  }
`;

async function getIntegrations() {
  const result = await ReadModelClient.query<IntegrationQuery>({
    query: getIntegrationsQuery,
    fetchPolicy: 'network-only',
  });

  return result.data.integration;
}

const IntegrationsQuery = {
  getIntegrations,
};

export default IntegrationsQuery;
