import { gql } from '@apollo/client';

export const GET_PROVIDERS_BY_INVENTORY_ITEM = gql`
  query GetProviderByInventoryItem($inventoryId: uuid!) {
    provider(
      where: {
        providerInventories: {
          inventoryId: {
            _eq: $inventoryId
          }
        }
      }
    ) {
    id
    name
    providerInventories(where: {inventoryId: {_eq:$inventoryId}}) {
      currentDiscount
      currentPurchasePrice
    }
  }
  }
`;
