import { Apollo<PERSON><PERSON>, ApolloProvider, NormalizedCacheObject } from '@apollo/client';
import React, { useContext, useMemo } from 'react';

import { createClient } from '#infrastructure/api/readModel/ReadModel.Api';
import { AuthContext } from '#infrastructure/AuthState.Context';

export const ReadModelProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { getToken } = useContext(AuthContext);

  const apolloClient: ApolloClient<NormalizedCacheObject> = useMemo(() => createClient(getToken), [getToken]);

  return (
    <ApolloProvider client={apolloClient}>
      {children}
    </ApolloProvider>
  );
};
