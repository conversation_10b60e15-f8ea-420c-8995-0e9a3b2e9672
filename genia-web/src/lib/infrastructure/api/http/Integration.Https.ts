import { GeniaClient } from './HttpClient.Http';

const client = GeniaClient();

export enum IntegrationType {
  GMAIL = 'gmail',
  GCALENDAR = 'gcalendar',
  WHATSAPP = 'whatsapp',
}

export interface PostIntegration {
  type: IntegrationType;
  token: string;
  params?: Record<string, string>;
}

 interface PostIntegrationResponse {
  id: string;
  type: IntegrationType;
  params: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

const postIntegrationPath = '/integrations';

const postIntegration = async (integration: PostIntegration) => {
  const response = await client.post<PostIntegrationResponse[]>(postIntegrationPath, [integration]);

  return response.data[0] || {};
};

const retrieveIntegrations = async () => {
  const response = await client.get<PostIntegrationResponse[]>(postIntegrationPath);
  return response.data;
};

const deleteIntegration = async (id: string) => {
  const response = await client.delete(`${postIntegrationPath}/${id}`);
  return response.data;
};

export const IntegrationHttps = {
  postIntegration,
  retrieveIntegrations,
  deleteIntegration,
};
