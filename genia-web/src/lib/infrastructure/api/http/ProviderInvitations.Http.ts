import { createNodeAxios } from './HttpClient.Http';

const INVITATIONS_PATH = '/invitations';

export interface UpdateInvitationRequest {
  state: 'accepted' | 'rejected' | 'pending';
}

const updateInvitationState = async (token: string | void, invitationId: string, state: 'accepted' | 'rejected' | 'pending'): Promise<void> => {
  const nodeAxios = createNodeAxios(token || '');
  const payload: UpdateInvitationRequest = { state };
  const response = await nodeAxios.patch(`${INVITATIONS_PATH}/${invitationId}`, payload);
  return response.data;
};

const acceptInvitation = async (token: string | void, invitationId: string): Promise<void> => updateInvitationState(token, invitationId, 'accepted');

const rejectInvitation = async (token: string | void, invitationId: string): Promise<void> => updateInvitationState(token, invitationId, 'rejected');

export const ProviderInvitationsHttp = {
  updateInvitationState,
  acceptInvitation,
  rejectInvitation,
};
