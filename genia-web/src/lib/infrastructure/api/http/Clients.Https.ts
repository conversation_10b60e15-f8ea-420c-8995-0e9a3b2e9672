import { createNodeAxios } from './HttpClient.Http';

const CLIENTS_PATH = '/clients';
export interface ClientsPayload {
  name: string;
  tributaryId: string | null | undefined;
  clientCompanyId: string | null;
  storeDiscounts: unknown[];
  contactInformation: {
    billingEmail: string;
    billingPhoneNumber: string;
    billingWhatsapp: string;
    purchasesEmail: string;
    purchasesPhoneNumber: string;
    purchasesWhatsapp: string;
    salesEmail: string;
    salesPhoneNumber: string;
    salesWhatsapp: string;
    shippingAddress: string;
    billingAddress: string;
  };
}

const postClients = (token: string | void, payload: ClientsPayload[]) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.post(CLIENTS_PATH, [...payload]);
};

const updateClient = (token: string | void, id: string, payload: ClientsPayload) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.patch(`${CLIENTS_PATH}/${id}`, payload);
};

export const ClientsHttps = {
  postClients,
  updateClient,
};
