import { CreateHistoryMovementProps } from '#application/inventory/Inventory.Type';

import { createNodeAxios } from './HttpClient.Http';

const INVENTORY_PATH = '/inventory';
const INVENTORY_HISTORY_PATH = '/history';

export interface Category {
  key: string;
  values?: string[];
  categories?: Category[];
}

interface InventoryItemPayload {
  description?: string | null;
  standardIdentifier?: string | null;
  attributes?: Category[] | null;
  id?: string;
  name?: string;
  hasStockValidation?: boolean;
  measurementUnit?: string | null;
  sku?: string | null;
  stock?: number;
  type?: string;
  providers?: {
    id?: string;
    name?: string;
    currentPurchasePrice?: number;
    currentDiscount?: number;
  }[]
}

const createInventory = async (token: string | void, payload: InventoryItemPayload) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(INVENTORY_PATH, [payload]);
};

const updateInventory = async (token: string | void, id?: string, payload?: InventoryItemPayload) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.patch(`${INVENTORY_PATH}/${id}`, payload);
};

const createHistoryMovement = async (props: CreateHistoryMovementProps) => {
  const { token, inventoryId, payload } = props;
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(`${INVENTORY_PATH}/${inventoryId}${INVENTORY_HISTORY_PATH}`, payload);
};

export const InventoryHttps = {
  createInventory,
  updateInventory,
  createHistoryMovement,
};
