import { createNodeAxios } from './HttpClient.Http';

const CATALOG_PATH = '/catalog';
const PROVIDERS_PATH = '/providers';

interface GetStoreItemsProps{
  token: string | void;
  pageSize?: number;
  page?: number;
  searchTerm?: string;
  orderBy?: string;
  order?: string;
  providers?: string;
}

const getStoreItems = ({
  token, pageSize, page, searchTerm, orderBy, order, providers,
}: GetStoreItemsProps) => {
  const nodeAxios = createNodeAxios(token || '');

  let url = `${PROVIDERS_PATH}/${CATALOG_PATH}`;

  const params = new URLSearchParams();

  if (searchTerm) {
    params.append('searchTerm', searchTerm);
  }
  if (pageSize) {
    params.append('pageSize', String(pageSize));
  }
  if (page) {
    params.append('page', String(page));
  }
  if (orderBy) {
    params.append('orderBy', orderBy);
  }
  if (order) {
    params.append('orderDirection', order);
  }
  if (providers) {
    params.append('providers', providers);
  }

  const paramString = params.toString();
  if (paramString) {
    url += `?${paramString}`;
  }

  return nodeAxios.get(url);
};

export const StoreHttps = {
  getStoreItems,
};
