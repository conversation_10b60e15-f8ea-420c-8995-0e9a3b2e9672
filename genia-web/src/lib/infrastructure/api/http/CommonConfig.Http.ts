import { InternalAxiosRequestConfig } from 'axios';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import InMemCacheUtil from '#infrastructure/utils/InMemCache.Util';

const NETWORK_MESSAGE = 'Verifica tu conexióna internet';
const REQUEST_ERROR = 'Error en la petición';

export const getResponse = (response: {data: Record<string, unknown>}) => {
  const { data: responseData } = response;

  return responseData;
};

export interface ResponseError {
  response: {
    data: {
      message: string;
      statusCode: number;
    };
  }
}

export interface AxiosResponseError {
  statusCode: number;
  message: string;
}

export class AxiosError extends Error {
  statusCode: number;

  constructor(message: string, code: number) {
    super(message);
    this.name = 'AxiosError';
    this.message = message;
    this.statusCode = code;
  }
}

export const rejectResponse = (error: ResponseError): AxiosResponseError => {
  const { response: { data } } = error;
  const { message, statusCode } = data;

  if (!statusCode && !message) { return { statusCode: 500, message: REQUEST_ERROR }; }

  if (message === 'Network Error') {
    Notification({
      message: NETWORK_MESSAGE,
      type: MSG_ERROR_TYPES.ERROR,
    });

    return { message, statusCode };
  }

  if (statusCode === 500) {
    throw new AxiosError(message, statusCode);
  }

  if (statusCode === 401) {
    window.location.reload();

    return { statusCode, message };
  }

  if (statusCode >= 400 && statusCode < 500) {
    throw new AxiosError(message, statusCode);
  }

  if (statusCode >= 400 && statusCode < 500) {
    throw new AxiosError(message, statusCode);
  }

  return { message, statusCode };
};

export const baseHeadersConfig = async (config: { headers: Record<string, unknown>}, token: string): Promise<InternalAxiosRequestConfig> => ({
  ...config,
  headers: {
    ...config.headers,
    Authorization: token ? `Bearer ${token}` : '',
  },
} as unknown as InternalAxiosRequestConfig);

export const AuthHeaderConfig = async (config: { headers: Record<string, unknown>}): Promise<InternalAxiosRequestConfig> => ({
  ...config,
  headers: {
    ...config.headers,
    Authorization: InMemCacheUtil.getToken() ? `Bearer ${InMemCacheUtil.getToken()}` : '',
  },
} as unknown as InternalAxiosRequestConfig);
