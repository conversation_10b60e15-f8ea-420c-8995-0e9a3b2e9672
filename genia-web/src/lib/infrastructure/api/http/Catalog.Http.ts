import { InventoryCatalogs } from '#application/catalog/Catalog.Type';

import { createNodeAxios } from './HttpClient.Http';

const CATALOG_PATH = '/catalog';

export type CatalogPayload = {
  name: string;
  description: string | null;
  requiresStock: boolean;
  disabledAt?: Date| string | null;
  standardIdentifier?: string | null;
  attributes?: unknown[] | null;
  price: number;
  taxIds: string[];
  inventoryRelations: Pick<InventoryCatalogs, 'inventoryId' | 'quantity'>[] | Pick<InventoryCatalogs, 'id' | 'quantity'>[] ;
  [key: string]: unknown;
};

const createCatalogItem = (
  token: string | void,
  payload: CatalogPayload,
) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(CATALOG_PATH, [payload]);
};

const updateCatalogItem = (
  token: string | void,
  id: string,
  payload: CatalogPayload,
) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.patch(`${CATALOG_PATH}/${id}`, payload);
};

export const CatalogHttps = {
  createCatalogItem,
  updateCatalogItem,
};
