import axios from 'axios';

import { AuthHeaderConfig, baseHeadersConfig, rejectResponse } from '#infrastructure/api/http/CommonConfig.Http';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';

const { GENIA_NODE_API, CLAUDIA_NODE_API } = GetEnv();

export const createNodeAxios = (token: string = '') => {
  const nodeAxios = axios.create({
    baseURL: GENIA_NODE_API,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    withCredentials: true,
  });

  nodeAxios.interceptors.request.use((config) => baseHeadersConfig(config, token));
  nodeAxios.interceptors.response.use((data) => data, rejectResponse);

  return nodeAxios;
};

export const createClaudiaAxios = (token: string = '') => {
  const claudiaAxios = axios.create({
    baseURL: CLAUDIA_NODE_API,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    withCredentials: true,
  });

  claudiaAxios.interceptors.request.use((config) => baseHeadersConfig(config, token));
  claudiaAxios.interceptors.response.use((data) => data, rejectResponse);

  return claudiaAxios;
};

export const GeniaClient = () => {
  const geniaAxios = axios.create({
    baseURL: GENIA_NODE_API,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    withCredentials: true,
  });

  geniaAxios.interceptors.request.use((config) => AuthHeaderConfig(config));
  geniaAxios.interceptors.response.use((data) => data, rejectResponse);

  return geniaAxios;
};
