import { UserRole } from '#domain/aggregates/user/User.Entity';

import { createNodeAxios } from './HttpClient.Http';

const USERS_PATH = '/users';

export interface CreateUserPayload {
  email: string;
  name: string;
  lastName: string;
  phoneNumber: string;
  role: UserRole;
}

export interface UpdateUserPayload {
  name?: string;
  lastName?: string;
  phoneNumber?: string;
  role?: UserRole;
  disabledAt?: Date | null;
}

export interface UserResponse {
  id: string;
  email: string;
  name: string;
  lastName: string;
  phoneNumber: string;
  companies: string[];
  role: UserRole;
  disabledAt?: Date | null;
  state?: 'ACTIVE' | 'DISABLED';
}

export interface ToggleUserStatusPayload {
  disable: boolean;
}

export interface UpdateUserStatePayload {
  state: 'ACTIVE' | 'DISABLED';
}

export interface UpdateUserRolePayload {
  role: UserRole;
}

const createUser = async (token: string | void, payload: CreateUserPayload[]): Promise<UserResponse[]> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.post<UserResponse[]>(USERS_PATH, payload);

  return response.data;
};

const getUser = async (token: string | void, userId: string): Promise<UserResponse> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.get<UserResponse>(`${USERS_PATH}/${userId}`);

  return response.data;
};

const updateUser = async (token: string | void, userId: string, payload: UpdateUserPayload): Promise<UserResponse> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.put<UserResponse>(`${USERS_PATH}/${userId}`, payload);

  return response.data;
};

const toggleUserStatus = async (token: string | void, userId: string, payload: ToggleUserStatusPayload): Promise<UserResponse> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.patch<UserResponse>(`${USERS_PATH}/${userId}/status`, payload);

  return response.data;
};

const updateUserState = async (token: string | void, userId: string, payload: UpdateUserStatePayload): Promise<UserResponse> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.patch<UserResponse>(`${USERS_PATH}/${userId}`, payload);

  return response.data;
};

const updateUserRole = async (token: string | void, userId: string, payload: UpdateUserRolePayload): Promise<UserResponse> => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.patch<UserResponse>(`${USERS_PATH}/${userId}`, payload);

  return response.data;
};

export const UsersHttp = {
  createUser,
  getUser,
  updateUser,
  toggleUserStatus,
  updateUserState,
  updateUserRole,
};
