import { StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';

import { createNodeAxios } from './HttpClient.Http';

const STORE_DISCOUNT_PATH = '/store-discount';
const CLIENTS_PATH = '/clients';

const createStoreDiscount = (token: string | void, payload: Partial<StoreDiscountItem>) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(STORE_DISCOUNT_PATH, [payload]);
};

const updateStoreDiscount = (token: string | void, id: string, payload: Partial<StoreDiscountItem>) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.patch(`${STORE_DISCOUNT_PATH}/${id}`, payload);
};

const assignStoreDiscountClients = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(`${STORE_DISCOUNT_PATH}/${id}${CLIENTS_PATH}`, { clientIds: payload });
};

const unAssignStoreDiscountClients = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.delete(`${STORE_DISCOUNT_PATH}/${id}${CLIENTS_PATH}`, { data: { clientIds: payload } });
};

export const StoreDiscountHttps = {
  createStoreDiscount,
  updateStoreDiscount,
  assignStoreDiscountClients,
  unAssignStoreDiscountClients,
};
