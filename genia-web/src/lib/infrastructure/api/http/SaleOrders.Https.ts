import { OrderConditionItem, OrderConditions } from '#application/common/orders/Order.Type';
import { SaleOrderServiceUpdateParams } from '#application/saleOrders/services/SaleOrder.Service';
import { Modify } from '#composition/Common.Type';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderItemSchema, OrderSchema, PostOrderSchema } from '#infrastructure/api/http/types/Order.Type';

import { createNodeAxios } from './HttpClient.Http';

const path = '/sale-orders';

interface SaleOrderItemSchema extends OrderItemSchema {
  catalogId: string;
}

export interface SaleOrderSchema extends OrderSchema {
  clientId: string;
  clientCompanyId: string | null;
  orderItems: SaleOrderItemSchema[];
  relatedPurchaseOrderId?: string | null;
}

export interface PostSaleOrderSchema extends PostOrderSchema {
  clientId: string;
  orderItems: {
    catalogId: string,
    quantity: number,
  }[];
}

export interface OrderConditionsPayload{
  shippingPrice?: number;
  orderItems:{
    catalogId: string;
    quantity: number;
    unitPrice?: number;
    taxIds?: string[];
    unitPriceAfterDiscount?: number;
  }[];
  hasTaxOnShipping?: boolean;
  clientId: string;
}

export type SaleOrderConditionsItem = Modify<Omit<OrderConditionItem, 'id'>, {
  catalogId: string;
}>;

export type SaleOrderConditions = Modify<OrderConditions, {
  orderItems: SaleOrderConditionsItem[];
}>;

const getSaleOrders = (token: string | void) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.get(path);
};

const getSaleOrderById = (token: string | void, id: string) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.get(`${path}/${id}`);
};

const createSaleOrder = async (token: string | void, payload: PostSaleOrderSchema) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.post<SaleOrderSchema[]>(path, [payload]);

  return response.data[0];
};

const updateSaleOrder = async (token: string | void, saleOrder: SaleOrderServiceUpdateParams) => {
  const nodeAxios = createNodeAxios(token || '');
  const { id, ...payload } = saleOrder;

  const response = await nodeAxios.patch<SaleOrderSchema>(`${path}/${id}`, payload);

  return response.data;
};

const postSaleOrderConditions = async (token: string | void, payload: OrderConditionsPayload) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.post<SaleOrderConditions>(`${path}/conditions`, payload);

  return response.data;
};

const getAvailableStatuses = async (token: string | void, id: string) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.get<OrderStatusIds[]>(`${path}/${id}/available-statuses`);

  return response.data;
};

export const SaleOrdersHttps = {
  getSaleOrders,
  getSaleOrderById,
  createSaleOrder,
  updateSaleOrder,
  postSaleOrderConditions,
  getAvailableStatuses,
};
