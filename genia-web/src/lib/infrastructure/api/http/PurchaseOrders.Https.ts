import { OrderConditionItem, OrderConditions } from '#application/common/orders/Order.Type';
import { PurchaseOrderServiceUpdateParams } from '#application/purchaseOrders/services/PurchaseOrder.Service';
import { Modify } from '#composition/Common.Type';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderItemSchema, OrderSchema, PostOrderSchema } from '#infrastructure/api/http/types/Order.Type';

import { createNodeAxios } from './HttpClient.Http';

const path = '/purchase-orders';

interface PurchaseOrderItemSchema extends OrderItemSchema {
  referenceId: string;
}

export interface PurchaseOrderSchema extends OrderSchema {
  providerId: string;
  providerCompanyId: string | null;
  orderItems: PurchaseOrderItemSchema[];
  relatedPurchaseOrderId?: string | null;
}

export interface PostPurchaseOrderSchema extends PostOrderSchema {
  providerId: string;
  orderItems: {
    referenceId: string,
    quantity: number,
  }[];
}

export interface PurchaseOrderOrderConditionsPayload{
  shippingPrice?: number;
  orderItems:{
    referenceId: string;
    quantity: number;
    unitPrice?: number;
    taxIds?: string[];
    unitPriceAfterDiscount?: number;
  }[];
  hasTaxOnShipping?: boolean;
  clientId: string;
}

export type PurchaseOrderConditionsItem = Modify<Omit<OrderConditionItem, 'id'>, {
  referenceId: string;
}>;

export type PurchaseOrderConditions = Modify<OrderConditions, {
  orderItems: PurchaseOrderConditionsItem[];
}>;

export interface InventorySuggestionSchema {
  referenceId: string;
  inventoryIds?: string[] | null;
}

const getPurchaseOrders = (token: string | void) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.get(path);
};

const getPurchaseOrderById = (token: string | void, id: string) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.get(`${path}/${id}`);
};

const createPurchaseOrder = async (token: string | void, purchaseOrder: PostPurchaseOrderSchema) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.post<PurchaseOrderSchema[]>(path, [purchaseOrder]);

  return response.data[0];
};

const updatePurchaseOrder = async (token: string | void, purchaseOrder: PurchaseOrderServiceUpdateParams) => {
  const nodeAxios = createNodeAxios(token || '');
  const { id, ...payload } = purchaseOrder;

  const response = await nodeAxios.patch<PurchaseOrderSchema>(`${path}/${id}`, payload);

  return response.data;
};

const postPurchaseOrderConditions = async (token: string | void, payload: PurchaseOrderOrderConditionsPayload) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.post<PurchaseOrderConditions>(`${path}/conditions`, payload);

  return response.data;
};

const getAvailableStatuses = async (token: string | void, id: string) => {
  const nodeAxios = createNodeAxios(token || '');

  const response = await nodeAxios.get<OrderStatusIds[]>(`${path}/${id}/available-statuses`);

  return response.data;
};

const getInventorySuggestions = async (token: string | void, providerId: string, referenceIds: string[]) => {
  const nodeAxios = createNodeAxios(token || '');
  const payload = {
    providerId,
    referenceIds,
  };
  const response = await nodeAxios.post<InventorySuggestionSchema[]>(`${path}/inventory-suggestions`, payload);
  return response.data;
};

export const PurchaseOrdersHttps = {
  getPurchaseOrders,
  getPurchaseOrderById,
  createPurchaseOrder,
  updatePurchaseOrder,
  postPurchaseOrderConditions,
  getAvailableStatuses,
  getInventorySuggestions,
};
