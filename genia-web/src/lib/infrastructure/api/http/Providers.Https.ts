import {
  CheckTributaryIdResponse,
  ConnectProviderResponse,
  CreateProviderResponse,
  InviteProviderResponse,
  UpdateProviderResponse,
} from '#application/provider/services/Provider.Service';

import { createNodeAxios } from './HttpClient.Http';

const PROVIDERS_PATH = '/providers';

export interface ProvidersPayload {
  name: string | undefined;
  tributaryId: string | null | undefined;
  phone?: string | null | undefined;
  address?: string | null | undefined;
  notificationEmail?: string | null | undefined;
  managerName?: string | null | undefined;
}

const postProviders = (token: string | void, payload: ProvidersPayload[]): Promise<CreateProviderResponse> => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.post(PROVIDERS_PATH, [...payload]).then((response) => response.data);
};

const updateProvider = (token: string | void, id: string, payload: ProvidersPayload): Promise<UpdateProviderResponse> => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.patch(`${PROVIDERS_PATH}/${id}`, payload).then((response) => response.data);
};

const getCatalogByReadId = (
  token: string | void,
  providerId: string,
  params: {
    limit?: number;
    offset?: number;
    searchTerm?: string;
  },
) => {
  const nodeAxios = createNodeAxios(token || '');

  const queryParams = new URLSearchParams({
    orderBy: 'readId',
    pageSize: (params.limit || 10).toString(),
    page: (params.offset || 1).toString(),
    providers: providerId,
  });

  if (params.searchTerm) {
    queryParams.append('searchTerm', params.searchTerm);
  }

  return nodeAxios.get(`${PROVIDERS_PATH}/catalog?${queryParams.toString()}`);
};

const checkTributaryId = (token: string | void, tributaryId: string): Promise<CheckTributaryIdResponse> => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.get(`${PROVIDERS_PATH}/check-tributary-id/${tributaryId}`).then((response) => response.data);
};

export interface ProviderInvitationPayload {
  email?: string;
  phoneNumber?: string;
  invitedCompanyId?: string;
  type?: string;
}

const connectProvider = (token: string | void, invitations: ProviderInvitationPayload[]): Promise<ConnectProviderResponse> => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.post('/invitations', invitations).then((response) => response.data);
};

const inviteProvider = (token: string | void, invitations: ProviderInvitationPayload[]): Promise<InviteProviderResponse> => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.post('/invitations', invitations).then((response) => response.data);
};

export const ProvidersHttps = {
  postProviders,
  updateProvider,
  getCatalogByReadId,
  checkTributaryId,
  connectProvider,
  inviteProvider,
};
