import { CatalogDiscountItem } from '#application/deprecated/DashboardPages.Type';

import { createNodeAxios } from './HttpClient.Http';

const CATALOG_DISCOUNT_PATH = '/catalog-discount';
const CLIENTS_PATH = '/clients';
const CATALOGS_PATH = '/catalogs';

const createCatalogDiscount = (token: string | void, payload: CatalogDiscountItem) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(CATALOG_DISCOUNT_PATH, [payload]);
};

const updateCatalogDiscount = (token: string | void, id: string, payload: CatalogDiscountItem) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.patch(`${CATALOG_DISCOUNT_PATH}/${id}`, payload);
};

const assignCatalogDiscountClients = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(`${CATALOG_DISCOUNT_PATH}/${id}${CLIENTS_PATH}`, { clientIds: payload });
};

const unAssignCatalogDiscountClients = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.delete(`${CATALOG_DISCOUNT_PATH}/${id}${CLIENTS_PATH}`, { data: { clientIds: payload } });
};

const assignCatalogDiscountCatalogs = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.post(`${CATALOG_DISCOUNT_PATH}/${id}${CATALOGS_PATH}`, { catalogIds: payload });
};

const unAssignCatalogDiscountCatalogs = (token: string | void, id: string, payload: string[]) => {
  const nodeAxios = createNodeAxios(token || '');

  return nodeAxios.delete(`${CATALOG_DISCOUNT_PATH}/${id}${CATALOGS_PATH}`, { data: { catalogIds: payload } });
};

export const CatalogDiscountHttps = {
  createCatalogDiscount,
  updateCatalogDiscount,
  assignCatalogDiscountClients,
  unAssignCatalogDiscountClients,
  assignCatalogDiscountCatalogs,
  unAssignCatalogDiscountCatalogs,
};
