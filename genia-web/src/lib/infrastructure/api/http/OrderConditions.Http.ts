import { createNodeAxios } from './HttpClient.Http';

const PURCHASE_ORDERS_CONDITIONS_PATH = '/purchase-orders/conditions';
export interface PurchaseOrderItem{
  referenceId: string;
  quantity: number;
}

export interface PostPurchaseOrderConditionsProps{
  token: string | void;
  providerId: string;
  orderItems: PurchaseOrderItem[];
  isUpdate?: boolean;
}

const postPurchaseOrderConditions = ({ token, providerId, orderItems }: PostPurchaseOrderConditionsProps) => {
  const nodeAxios = createNodeAxios(token || '');
  const url = PURCHASE_ORDERS_CONDITIONS_PATH;

  return nodeAxios.post(url, { providerId, orderItems });
};

export const OrderConditionsHttps = {
  postPurchaseOrderConditions,
};
