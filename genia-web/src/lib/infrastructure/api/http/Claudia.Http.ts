import { createClaudiaAxios } from './HttpClient.Http';

 interface ThreadEntity {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}
 enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

interface MessageEntity {
  id: string;
  threadId: string;
  content: string;
  role: MessageRole;
  createdAt: Date;
  updatedAt: Date;
}

export interface FindUserThreadResponse {
  thread: ThreadEntity | null;
  threadMessages: MessageEntity[];
}
const threadsPath = '/threads';

const getUserMessages = async (token: string | void) => {
  const nodeAxios = createClaudiaAxios(token || '');

  const response = await nodeAxios.get<FindUserThreadResponse>(threadsPath);

  return response.data.threadMessages;
};

export const ClaudiaHttp = {
  getUserMessages,
};
