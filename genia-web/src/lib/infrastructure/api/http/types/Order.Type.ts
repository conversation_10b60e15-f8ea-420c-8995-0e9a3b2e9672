import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderTax } from '#domain/aggregates/order/OrderTax.ValueObject';

export enum DiscountTypeSchema {
  AMOUNT = 'amount',
  PERCENTAGE = 'percentage',
}
export interface DiscountEntitySchema {
  id: string;
  name: string
  discountValue: number;
  discountType: DiscountTypeSchema;
  startDate: Date | null;
  endDate: Date | null;
  disabledAt: Date | null;
  companyId: string;
  createdAt: Date;
  updatedAt: Date;
  clientIds: string[];
}

export interface OrderItemSchema {
  name: string,
  productId: string,
  quantity: number,
  unitPrice: number,
  unitPriceAfterDiscount: number,
  unitPriceAfterDiscountAndTaxes: number,
  total: number,
  subtotal: number,
  taxes: OrderTax[],
  discount?: {
    value: number,
    type: DiscountTypeSchema,
  } | null,
  createdAt: string,
  updatedAt: string,
}

export interface OrderSchema {
  id: string;
  readId: string;
  assignedUserId: string | null;
  status: OrderStatusIds;
  subtotalBeforeDiscount: number;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  total: number;
  notes?: string | null;
  deliveryDate?: string | null;
  shippingAddress?: string | null;
  shippingPrice?: number | null;
  shippedAt?: string | null;
  taxes: OrderTax[];
  orderItems: OrderItemSchema[];
  createdAt: string;
  updatedAt: string
}

export interface PostOrderSchema {
  notes?: string;
  deliveryDate?: string;
  shippingAddress?: string | null;
  shippingPrice?: number;
  readId?: string;
  orderItems: {
    quantity: number,
  }[];
}
