import { createNodeAxios } from './HttpClient.Http';

const MEDIA_PATH = '/media';

const deleteMediaFromEntity = (
  token: string | void,
  entity: string,
  entityId: string,
  mediaIds: string[],
) => {
  const nodeAxios = createNodeAxios(token || '');
  return nodeAxios.delete(`${MEDIA_PATH}/${entity}/${entityId}`, {
    data: mediaIds,
  });
};

export const MediaHttps = {
  deleteMediaFromEntity,
};
