import { createNodeAxios } from './HttpClient.Http';

const NOTIFICATIONS_PATH = '/notifications';

export interface NotificationResponse {
  id: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

export interface MarkAsReadRequest {
  notificationIds: string[];
}

const markAsRead = async (token: string | void, notificationIds: string[]): Promise<void> => {
  const nodeAxios = createNodeAxios(token || '');
  const response = await nodeAxios.patch(`${NOTIFICATIONS_PATH}/mark-read`, {
    notificationIds,
  });
  return response.data;
};

const markAllAsRead = async (token: string | void): Promise<void> => {
  const nodeAxios = createNodeAxios(token || '');
  const response = await nodeAxios.patch(`${NOTIFICATIONS_PATH}/mark-all-read`);
  return response.data;
};

const deleteNotification = async (token: string | void, notificationId: string): Promise<void> => {
  const nodeAxios = createNodeAxios(token || '');
  const response = await nodeAxios.delete(`${NOTIFICATIONS_PATH}/${notificationId}`);
  return response.data;
};

export const NotificationsHttp = {
  markAsRead,
  markAllAsRead,
  deleteNotification,
};
