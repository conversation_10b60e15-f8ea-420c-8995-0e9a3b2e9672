import { gql, useQuery } from '@apollo/client';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { StoreDiscount } from '#application/discount/StoreDiscount.Type';

export const GET_STORE_DISCOUNTS = gql`
  query GetStoreDiscounts(
    $limit: Int,
    $offset: Int,
    $orderBy: [store_discount_order_by!],
    $whereFilter: store_discount_bool_exp,
    ) {
    store_discount(
      limit: $limit,
      offset: $offset,
      order_by: $orderBy,
      where: $whereFilter,
    ) {
      id
      name
      discountType
      discountValue
      requiredAmount
      startDate
      endDate
      disabledAt
      storeDiscountClients {
        client {
          id
          name
        }
      }
    }
    store_discount_aggregate(
      where: $whereFilter
    ) {
      aggregate {
        count
      }
    }
  }
`;

const useGetStoreDiscounts = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'desc', id,
}: GetTableDataProps): GetTableDataResponse<StoreDiscount> => {
  const whereFilter = {
    storeDiscountClients: { client: { id: { _eq: id } } },
  };

  const variables = {
    limit,
    offset,
    orderBy: [{ [orderBy]: order }],
  };

  const {
    data, loading, error, refetch,
  } = useQuery(GET_STORE_DISCOUNTS, {
    variables: id ? { ...variables, whereFilter } : variables,
    fetchPolicy: 'network-only',
  });

  return {
    items: data?.store_discount,
    totalNumberOfItems: data?.store_discount_aggregate?.aggregate?.count || 0,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelStoreDiscountService = {
  useGetStoreDiscounts,
};

export default ReadModelStoreDiscountService;
