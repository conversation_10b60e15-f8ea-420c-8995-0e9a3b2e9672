import { gql, useQuery } from '@apollo/client';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { CatalogDiscount } from '#application/discount/CatalogDiscount.Type';

const columns = `
  id
  name
  startDate
  requiredQuantity
  endDate
  discountType
  discountValue
  disabledAt
  catalog_discount_clients {
    client {
      id
      name
      client_company {
        name
      }
    }
  }
`;

const GET_CATALOG_DISCOUNT_QUERY = gql`
  query GetCatalogDiscount(
    $limit: Int!, 
    $offset: Int!,
    $whereFilter: catalog_discount_bool_exp,
    ) {
    catalog_discount(
      limit: $limit, 
      offset: $offset
      where: $whereFilter
      ) {
      ${columns}
    }
    catalog_discount_aggregate(
      where: $whereFilter
    ) {
      aggregate {
        count
      }
    }
  }
`;

const useGetCatalogDiscounts = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'asc', id,
}: GetTableDataProps): GetTableDataResponse<CatalogDiscount> => {
  const whereFilter = {
    catalogs: { catalogId: { _eq: id } },
  };

  const variables = {
    limit,
    offset,
    orderBy: [{ [orderBy]: order }],
  };

  const {
    data, loading, error, refetch,
  } = useQuery(GET_CATALOG_DISCOUNT_QUERY, {
    variables: id ? { ...variables, whereFilter } : variables,
    fetchPolicy: 'network-only',
  });

  return {
    items: data?.catalog_discount,
    totalNumberOfItems: data?.catalog_discount_aggregate.aggregate.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelCatalogDiscountService = {
  useGetCatalogDiscounts,
};

export default ReadModelCatalogDiscountService;
