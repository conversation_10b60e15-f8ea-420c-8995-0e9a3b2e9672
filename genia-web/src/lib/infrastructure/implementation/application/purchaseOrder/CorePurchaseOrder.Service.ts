import { gql, useLazyQuery, useQuery } from '@apollo/client';
import {
  useCallback,
  useContext, useEffect, useMemo, useState,
} from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { OrderConditions } from '#application/common/orders/Order.Type';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';
import {
  PurchaseOrderConditionsParams,
  PurchaseOrderService,
  PurchaseOrderServiceUpdateParams,
} from '#application/purchaseOrders/services/PurchaseOrder.Service';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { CatalogMediaItem, CatalogMediaProps, GetCatalogMediaResult } from '#domain/aggregates/catalog/CatalogInventory.ValueObject';
import { Order } from '#domain/aggregates/order/Order.Entity';
import { OrderItem, OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { AuthContext } from '#infrastructure/AuthState.Context';
import {
  InventorySuggestionSchema,
  PostPurchaseOrderSchema,
  PurchaseOrderOrderConditionsPayload,
  PurchaseOrderSchema,
  PurchaseOrdersHttps,
} from '#infrastructure/api/http/PurchaseOrders.Https';
import { useActiveProviderCart } from '#infrastructure/implementation/application/hooks/useActiveProviderCart.Hook';
import { PurchaseOrderResponse, PurchaseOrdersListResponse } from '#infrastructure/implementation/application/purchaseOrder/CorePurchaseOrder.Type';
import { StoreProviderContext } from '#infrastructure/pages/app/pageContexts/StoreProvider.Context';

interface GetInventoryByIdsResponse {
  inventory: {
    id: string;
    name: string;
    sku: string;
    inventoryMedia: {
      url: string;
    }[];
  }[];
}

interface CatalogItem {
  id: string;
  catalog_media: CatalogMediaItem[];
}

interface GetCatalogMediaByIdsResponse {
  catalog: CatalogItem[];
}

const purchaseOrderQuery = gql`
query GetPurchaseOrder($id: uuid!) {
  purchase_order(where: {id: {_eq: $id}}) {
    id
    notes
    readId
    total
    subtotal
    subtotalBeforeDiscount
    totalDiscount
    taxes
    readId
    deliveryDate
    shippingAddress
    shippingPrice
    status
    createdAt
    updatedAt
    assignedUser {
      id
      email
    }
    provider {
      id
      name
      providerCompanyId
      company {
        id
        name
      }
    }
    purchase_order_items {
      name
      unitPrice
      quantity
      subtotal
      total
      taxes
      unitPriceAfterDiscount
      unitPriceAfterDiscountAndTaxes
      referenceId
      discountType
      discountValue
      productId
      purchase_order_item_inventories {
          inventory {
          id
          name
          sku
          inventoryMedia {
            url
          }
        }
      }
    }
  }
}
`;

const getCatalogMediaByIdsQuery = gql`
  query GetCatalogMediaByIds($catalogIds: [uuid!]!) {
    catalog(where: {id: {_in: $catalogIds}}) {
      id
      catalog_media {
        id
        url
      }
    }
  }
`;

const PurchaseOrdersQuery = gql`
  query GetPurchaseOrders($limit: Int, $offset: Int, $orderBy: [purchase_order_order_by!], $condition: purchase_order_bool_exp ) {
    purchase_order(limit: $limit, order_by:  $orderBy, offset: $offset, where: $condition)  {
      id
      status
      total
      readId
      createdAt
      updatedAt
      provider {
        name
        id
      }
      assignedUser {
        email
        id
      }
    }
    purchase_order_aggregate (where: $condition) {
      aggregate {
        count
      }
    }
  }
`;

const getInventoryByIdsQuery = gql`
query GetInventoryByIds($ids: [uuid!]!) {
  inventory(where: {id: {_in: $ids}}) {
    id
    name
    sku
    inventoryMedia {
      url
    }
  }
}
`;

function mappedOrder(order: PurchaseOrderResponse): Order {
  return {
    id: order.id,
    receiver: {
      id: order?.provider?.id,
      name: order?.provider?.name,
      type: 'provider',
      companyId: order?.provider?.company?.id || null,
      providerCompanyId: order?.provider?.providerCompanyId,
    },
    orderInfo: {
      deliveryDate: order.deliveryDate || '',
      shippingAddress: order.shippingAddress || '',
      notes: order.notes || '',
      orderNumber: order.readId,
      assignedUser: order.assignedUser || null,
      shippingPrice: order.shippingPrice || 0,
      status: {
        id: order.status,
        name: order.status,
      },
      summary: {
        subtotalBeforeDiscount: order.subtotalBeforeDiscount || 0,
        shippingPrice: order.shippingPrice || 0,
        subtotal: order.subtotal || 0,
        total: order.total || 0,
        taxes: order.taxes || [],
        totalDiscount: order.totalDiscount || 0,
      },
    },
    orderItems: order.purchase_order_items?.map((item): OrderItem => ({
      id: item.referenceId || '',
      productNumber: item.productId || '',
      name: item.name,
      summary: {
        subtotalBeforeDiscount: 0,
        shippingPrice: 0,
        totalDiscount: 0,
        subtotal: item.subtotal || 0,
        total: item.total || 0,
        taxes: item.taxes || [],
      },
      quantity: item.quantity,
      unitPrice: item.unitPrice || 0,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount || 0,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes || 0,
      image: item.catalog_media?.url || '',
      inventoryRelations: item.inventory_purchase_order_items?.map((inventoryItem) => ({
        id: inventoryItem.inventory.id,
        name: inventoryItem.inventory.name,
        sku: inventoryItem.inventory.sku,
        image: inventoryItem.inventory.inventoryMedia?.length ? inventoryItem.inventory.inventoryMedia[0].url : '',
        quantity: item.quantity,
        total: item.quantity,
      })) || [],
      discounts: {
        applied: null,
        applicable: [],
      },
    })) || [],
    createdAt: order.createdAt,
    updatedAt: order.updatedAt,
  };
}

function mapPurchaseOrderSchemaToOrder(purchaseOrder: PurchaseOrderSchema): Order {
  return {
    id: purchaseOrder.id,
    receiver: {
      id: purchaseOrder.providerId,
      name: '',
      type: 'provider',
      companyId: purchaseOrder.providerCompanyId,
    },
    orderInfo: {
      deliveryDate: purchaseOrder.deliveryDate || '',
      shippingAddress: purchaseOrder.shippingAddress || '',
      notes: purchaseOrder.notes || '',
      orderNumber: purchaseOrder.readId,
      assignedUser: {
        id: purchaseOrder.assignedUserId || '',
        email: '',
      },
      shippingPrice: purchaseOrder.shippingPrice || 0,
      status: {
        id: purchaseOrder.status,
        name: purchaseOrder.status,
      },
      summary: {
        subtotalBeforeDiscount: purchaseOrder.subtotalBeforeDiscount || 0,
        shippingPrice: purchaseOrder.shippingPrice || 0,
        subtotal: purchaseOrder.subtotal || 0,
        total: purchaseOrder.total || 0,
        taxes: purchaseOrder.taxes || [],
        totalDiscount: purchaseOrder.totalDiscount || 0,
      },
    },
    orderItems: purchaseOrder.orderItems.map((item): OrderItem => ({
      id: item.referenceId || '',
      productNumber: item.productId || '',
      name: item.name,
      summary: {
        subtotalBeforeDiscount: 0,
        shippingPrice: 0,
        totalDiscount: 0,
        subtotal: item.subtotal || 0,
        total: item.total || 0,
        taxes: item.taxes || [],
      },
      quantity: item.quantity,
      unitPrice: item.unitPrice || 0,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount || 0,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes || 0,
      image: '',
      inventoryRelations: [],
      discounts: {
        applied: null,
        applicable: [],
      },
    })),
    createdAt: purchaseOrder.createdAt,
    updatedAt: purchaseOrder.updatedAt,
  };
}

const useGetPurchaseOrders = ({
  limit = 10, offset = 0, orderBy = 'createdAt', order = 'desc', searchTerm, filters,
}: GetTableDataProps): GetTableDataResponse<Order> => {
  const validOrderByFields = ['createdAt', 'updatedAt', 'readId'];
  if (!validOrderByFields.includes(orderBy)) {
    throw new Error(`Invalid orderBy field: ${orderBy}`);
  }
  const validOrderDirections = ['asc', 'desc'];
  if (!validOrderDirections.includes(order)) {
    throw new Error(`Invalid order direction: ${order}`);
  }

  const searchCondition = searchTerm ? { readId: { _ilike: `%${searchTerm}%` } } : {};
  const filterCondition = filters ? Object.entries(filters).reduce((acc, [key, value]) => {
    if (Array.isArray(value) && value.length) {
      acc[key] = { _in: value };
    }
    return acc;
  }, {} as Record<string, unknown>) : {};

  const {
    data, loading, error, refetch,
  } = useQuery<PurchaseOrdersListResponse>(PurchaseOrdersQuery, {
    fetchPolicy: 'cache-and-network',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      condition: { ...searchCondition, ...filterCondition },
    },
  });

  if (error) {
    Notification({
      type: MSG_ERROR_TYPES.ERROR,
      message: 'Error fetching purchase orders',
    });
  }

  return {
    items: data?.purchase_order?.map((item) => mappedOrder(item as PurchaseOrderResponse)) || [],
    totalNumberOfItems: data?.purchase_order_aggregate?.aggregate?.count || 0,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetPurchaseOrder = (
  id: string,
): {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  } => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const [getPurchaseOrder, {
    data: { purchase_order: readPurchaseOrder } = { purchase_order: [] },
    loading: queryLoading,
    error: queryError,
    refetch,
  },
  ] = useLazyQuery<{purchase_order: PurchaseOrderResponse[]}>(purchaseOrderQuery, {
    fetchPolicy: 'network-only',
    variables: {
      id,
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      if (id) {
        setLoading(true);
        setError(null);
        try {
          await getPurchaseOrder();
        } catch (fetchError) {
          setError(fetchError as Error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
        setError(null);
      }
    };

    fetchData();
  }, [id, getPurchaseOrder]);

  const transformedOrder = useMemo(() => {
    const fetchedPurchaseOrder = readPurchaseOrder[0];
    if (!fetchedPurchaseOrder) {
      return undefined;
    }

    return mappedOrder(fetchedPurchaseOrder);
  }, [readPurchaseOrder]);

  return {
    order: transformedOrder,
    loading: queryLoading || loading,
    error: queryError || error || null,
    refetch,
  };
};

const useGetAvailableStatuses = (purchaseOrder: Order): { availableStatuses:OrderStatusIds[], error: unknown} => {
  const { getToken } = useContext(AuthContext);
  const [availableStatuses, setAvailableStatuses] = useState<OrderStatusIds[]>([]);
  const [error, setError] = useState<unknown>(null);

  useEffect(
    () => {
      const fetchAvailableStatuses = async () => {
        if (purchaseOrder.orderInfo.status.id === OrderStatusIds.CANCELLED || purchaseOrder.orderInfo.status.id === OrderStatusIds.COMPLETED) {
          setAvailableStatuses([]);
          return;
        }

        try {
          const token = await getToken();
          const response = await PurchaseOrdersHttps.getAvailableStatuses(token, purchaseOrder.id);
          setAvailableStatuses(response);
        } catch (fetchError) {
          setError(fetchError);
        }
      };

      fetchAvailableStatuses();
    },
    [getToken, purchaseOrder],
  );

  return {
    availableStatuses,
    error,
  };
};

const useUpdatePurchaseOrder = (): {
  applyOrderUpdate: (order: PurchaseOrderServiceUpdateParams) => Promise<Order>;
} => {
  const { getToken } = useContext(AuthContext);

  const applyOrderUpdate = async (order: PurchaseOrderServiceUpdateParams): Promise<Order> => {
    try {
      const payload: PurchaseOrderServiceUpdateParams = {
        id: order.id,
        notes: order.notes,
        deliveryDate: order.deliveryDate,
        shippingAddress: order.shippingAddress,
        status: order.status,
        assignedUserId: order.assignedUserId,
      };

      const token = await getToken();
      const response = await PurchaseOrdersHttps.updatePurchaseOrder(token, payload);
      return mapPurchaseOrderSchemaToOrder(response);
    } catch (error) {
      throw new Error('Error updating purchase order');
    }
  };

  return {
    applyOrderUpdate,
  };
};

const useGetConditions = (params: PurchaseOrderConditionsParams): {
  conditions: OrderConditions | null;
  error: unknown;
  loading: boolean;
} => {
  const [conditions, setConditions] = useState<OrderConditions | null>(null);
  const [error, setError] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const { getToken } = useContext(AuthContext);

  useEffect(() => {
    const fetchConditions = async () => {
      setLoading(true);

      if (params.orderItems?.length === 0) {
        setLoading(false);
        setConditions(null);
        setError(null);
        return;
      }

      const allItemsHaveCatalogId = params.orderItems?.every((item) => item.referenceId);
      const allItemHaveQuantity = params.orderItems?.every((item) => item.quantity);
      const allItemsHaveValidUnitPrice = params.orderItems?.every((item) => item.unitPrice === undefined || item.unitPrice > 0);
      const allItemsHaveValidUnitPriceAfterDiscount = params.orderItems
        ?.every((item) => item.unitPriceAfterDiscount === undefined || item.unitPriceAfterDiscount > 0);

      if (!params.providerId
          || !params.orderItems
          || !allItemsHaveCatalogId
          || !allItemHaveQuantity
          || !allItemsHaveValidUnitPrice
          || !allItemsHaveValidUnitPriceAfterDiscount) {
        setLoading(false);
        setConditions(null);
        setError(null);

        return;
      }

      const token = await getToken();
      try {
        const response = await PurchaseOrdersHttps.postPurchaseOrderConditions(token, params as PurchaseOrderOrderConditionsPayload);
        setConditions({
          ...response,
          orderItems: response.orderItems.map((item) => ({
            ...item,
            id: item.referenceId || '',
          })),
        });
      } catch (fetchError) {
        setError(fetchError);
      }

      setLoading(false);
      setError(null);
    };

    fetchConditions();
  }, [JSON.stringify(params), getToken]);

  return { conditions, error, loading };
};

function useSavePurchaseOrder(): {
  applyOrderSave: (order: Order) => Promise<Order>;
  } {
  const { getToken } = useContext(AuthContext);

  const applyOrderSave = async (order: Order): Promise<Order> => {
    const companyId = order.receiver.type === 'provider' && order.receiver.companyId;

    try {
      const token = await getToken();
      const purchaseOrder: PostPurchaseOrderSchema = {
        providerId: order.receiver.id,
        orderItems: order.orderItems.map((item) => ({
          referenceId: companyId ? item.id : item.productNumber,
          quantity: item.quantity,
          name: item.name,
          unitPrice: item.unitPrice,
          unitPriceAfterDiscount: item.unitPriceAfterDiscount,
          inventoryIds: item.inventoryRelations.map((relation) => relation.id) || null,
          taxIds: item.summary.taxes?.map((tax) => tax?.id || '') || [],
        })),
        deliveryDate: order.orderInfo.deliveryDate || undefined,
        notes: order.orderInfo.notes || undefined,
        readId: order.orderInfo.orderNumber || undefined,
        shippingAddress: order.orderInfo.shippingAddress || undefined,
        shippingPrice: order.orderInfo.shippingPrice,
      };
      const response = await PurchaseOrdersHttps.createPurchaseOrder(token, purchaseOrder);
      return mapPurchaseOrderSchemaToOrder(response);
    } catch (error) {
      throw new Error('Error saving purchase order');
    }
  };

  return { applyOrderSave };
}

function useGetInventorySuggestions(providerId: string, currentItems: string[]): {
  items: Record<string, OrderItemInventoryRelation[]>;
  error: unknown;
  loading: boolean;
} {
  const { getToken } = useContext(AuthContext);
  const [items, setItems] = useState<Record<string, OrderItemInventoryRelation[]>>({} as Record<string, OrderItemInventoryRelation[]>);
  const [error, setError] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const [inventoryReference, setInventoryReference] = useState<InventorySuggestionSchema[]>([]);

  useEffect(() => {
    const fetchInventorySuggestions = async () => {
      if (!providerId || currentItems.length === 0) {
        setItems({});
        return;
      }

      setLoading(true);
      try {
        const token = await getToken();
        const response = await PurchaseOrdersHttps.getInventorySuggestions(token, providerId, currentItems);
        setInventoryReference(response);
      } catch (fetchError) {
        setError(fetchError);
      } finally {
        setLoading(false);
      }
    };

    fetchInventorySuggestions();
  }, [getToken, providerId, JSON.stringify(currentItems)]);

  const [
    getInventoryByIds, { data: { inventory } = { inventory: [] }, loading: loadingGetInventoryByIds, error: errorGetInventoryByIds },
  ] = useLazyQuery<GetInventoryByIdsResponse>(getInventoryByIdsQuery, {
    fetchPolicy: 'cache-and-network',
    variables: {
      ids: inventoryReference.flatMap((item) => item.inventoryIds).filter((id) => id !== null),
    },
  });

  useEffect(() => {
    if (inventoryReference.length > 0) {
      getInventoryByIds();
    }
  }, [getInventoryByIds, JSON.stringify(inventoryReference)]);

  useEffect(() => {
    const result = inventory || [];

    const newMap = result.reduce((acc, item) => {
      const referenceId = inventoryReference.find((ref) => ref.inventoryIds?.includes(item.id))?.referenceId || '';
      if (!acc[referenceId]) {
        acc[referenceId] = [];
      }
      acc[referenceId].push({
        ...item,
        id: item.id,
        name: item.name,
        image: item.inventoryMedia?.[0]?.url || '',
        quantity: 1,
        total: 1,
      });

      return acc;
    }, {} as Record<string, OrderItemInventoryRelation[]>);

    setItems(newMap);
  }, [JSON.stringify(inventory)]);

  return {
    items,
    error: errorGetInventoryByIds || error,
    loading: loadingGetInventoryByIds || loading,
  };
}

export const usePurchaseOrderFromExternalData = (): {
  order: Order;
  resetCart: () => void;
} => {
  const { storeProvider: providerFromCart } = useContext(StoreProviderContext);
  const { items, cartFunctions } = useActiveProviderCart();

  const order: Order = {
    id: '',
    receiver: {
      id: providerFromCart?.id || '',
      name: providerFromCart?.name || '',
      type: 'provider',
      companyId: providerFromCart?.providerCompanyId || '',
    },
    orderInfo: {
      notes: '',
      orderNumber: '',
      shippingPrice: 0,
      shippingAddress: '',
      deliveryDate: '',
      summary: {
        subtotalBeforeDiscount: 0,
        totalDiscount: 0,
        subtotal: 0,
        taxes: [],
        total: 0,
        shippingPrice: 0,
      },
      status: {
        id: OrderStatusIds.PENDING,
        name: 'pending',
      },
      assignedUser: null,
    },
    orderItems: items.map((item) => ({
      id: item.referenceId || '',
      name: item.name || '',
      productNumber: item.sku || '',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || 0,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount || item.unitPrice || 0,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscount || item.unitPrice || 0,
      image: item.imageUrl || '',
      summary: {
        subtotalBeforeDiscount: 0,
        totalDiscount: 0,
        subtotal: 0,
        taxes: [],
        total: 0,
        shippingPrice: 0,
      },
      inventoryRelations: [],
      discounts: { applied: null, applicable: [] },
    })) || [],
    createdAt: null,
    updatedAt: null,
  };

  return {
    order,
    resetCart: cartFunctions.resetCart,
  };
};

export const useGetCatalogMedia = ({ catalogIds }: CatalogMediaProps): GetCatalogMediaResult => {
  const [
    executeLoadCatalogMedia,
    {
      data, loading, error,
    },
  ] = useLazyQuery<GetCatalogMediaByIdsResponse>(
    getCatalogMediaByIdsQuery,
    {
      fetchPolicy: 'network-only',
    },
  );

  useEffect(() => {
    if (catalogIds && catalogIds.length > 0) {
      executeLoadCatalogMedia({ variables: { catalogIds } });
    }
  }, [catalogIds, executeLoadCatalogMedia]);

  const mediaByCatalogId = useMemo(() => {
    if (data && data.catalog) {
      return data.catalog.reduce((acc, catalogItem) => {
        acc[catalogItem.id] = catalogItem.catalog_media;
        return acc;
      }, {} as Record<string, CatalogMediaItem[]>);
    }
    return {};
  }, [data]);

  return {
    mediaByCatalogId,
    loading,
    error,
  };
};

const useGetPurchaseOrderWithCatalogMedia = (purchaseOrderId: string): {
  order: Order | undefined;
  loading: boolean;
  error: unknown;
  refetch: () => Promise<void>;
} => {
  const [finalOrder, setFinalOrder] = useState<Order | undefined>(undefined);
  const [currentLoading, setCurrentLoading] = useState<boolean>(true);
  const [currentError, setCurrentError] = useState<unknown>(null);

  const {
    order: purchaseOrderData,
    loading: orderLoading,
    error: orderError,
    refetch: refetchOrder,
  } = useGetPurchaseOrder(purchaseOrderId);

  const providerCompanyId = purchaseOrderData?.receiver?.providerCompanyId;

  const catalogItemIdsToFetch = useMemo(() => {
    if (providerCompanyId && purchaseOrderData && purchaseOrderData.orderItems) {
      return purchaseOrderData.orderItems
        .map((item) => item.id)
        .filter((id) => !!id && id !== '');
    }
    return [];
  }, [providerCompanyId, purchaseOrderData]);

  const {
    mediaByCatalogId,
    loading: mediaLoading,
    error: mediaError,
  } = useGetCatalogMedia({ catalogIds: catalogItemIdsToFetch });

  useEffect(() => {
    const needsMediaFetch = catalogItemIdsToFetch.length > 0;

    if (orderError || !purchaseOrderData || !needsMediaFetch || mediaError) {
      setCurrentLoading(false);
      return;
    }

    if (orderLoading || mediaLoading) {
      setCurrentLoading(true);
      return;
    }

    setCurrentError(orderError || null);
    setFinalOrder((mediaError || !needsMediaFetch) ? purchaseOrderData : undefined);

    const enrichedOrderItems = purchaseOrderData.orderItems.map((item: OrderItem) => {
      const itemCatalogMediaArray = mediaByCatalogId[item.id];
      if (itemCatalogMediaArray && itemCatalogMediaArray.length > 0) {
        return {
          ...item,
          image: itemCatalogMediaArray[0].url,
        };
      }
      return item;
    });
    setFinalOrder({ ...purchaseOrderData, orderItems: enrichedOrderItems });
    setCurrentLoading(false);
    setCurrentError(null);
  }, [
    orderLoading,
    orderError,
    purchaseOrderData,
    catalogItemIdsToFetch,
    mediaLoading,
    mediaError,
    mediaByCatalogId,
  ]);

  const combinedRefetch = useCallback(async () => {
    setCurrentLoading(true);
    setCurrentError(null);
    setFinalOrder(undefined);
    await refetchOrder();
  }, [refetchOrder]);

  return {
    order: providerCompanyId ? finalOrder : purchaseOrderData,
    loading: currentLoading,
    error: currentError,
    refetch: combinedRefetch,
  };
};

const CorePurchaseOrderService: PurchaseOrderService = {
  useGetPurchaseOrders,
  useGetPurchaseOrder,
  useGetAvailableStatuses,
  useUpdatePurchaseOrder,
  useGetConditions,
  useSavePurchaseOrder,
  useGetInventorySuggestions,
  usePurchaseOrderFromExternalData,
  useGetCatalogMedia,
  useGetPurchaseOrderWithCatalogMedia,
};

export default CorePurchaseOrderService;
