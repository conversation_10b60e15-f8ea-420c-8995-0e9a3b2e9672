import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderTax } from '#domain/aggregates/order/OrderTax.ValueObject';

interface CatalogMedia {
  id: string;
  url: string;
}

interface InventoryItem {
    id: string;
    name: string;
    sku: string;
    inventoryMedia: {
      url: string;
    }[];
}

interface InventoryPurchaseOrderItem {
  inventory: InventoryItem;
}

export interface PurchaseOrderItemResponse {
  name: string;
  quantity: number;
  referenceId: string;
  productId: string;
  taxes: OrderTax[];
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  discountType: string | null;
  discountValue: number;
  inventory_purchase_order_items: InventoryPurchaseOrderItem[] | null;
  catalog_media: CatalogMedia | null;
  subtotal: number;
  total: number;
  unitPrice: number;
}

export interface PurchaseOrderResponse {
  id: string;
  notes: string;
  total: number;
  subtotal: number;
  subtotalBeforeDiscount: number;
  totalDiscount: number;
  taxes: OrderTax[];
  readId: string;
  deliveryDate: string | null;
  shippingAddress: string | null;
  shippingPrice: number;
  status: OrderStatusIds;
  createdAt: string;
  updatedAt: string;
  assignedUser: {
    email: string;
    id: string;
  } | null;
  provider: {
    id: string;
    name: string;
    company?: {
      id: string;
      name: string;
    };
    providerCompanyId: string;
  };
  purchase_order_items: PurchaseOrderItemResponse[];
}

export interface PurchaseOrdersListResponse {
  purchase_order: Array<{
    id: string;
    status: OrderStatusIds;
    total: number;
    readId: string;
    createdAt: string;
    updatedAt: string;
    provider: {
      name: string;
      id: string;
    };
    assignedUser: {
      email: string;
      id: string;
    } | null;
  }>;
  purchase_order_aggregate: {
    aggregate: {
      count: number;
    };
  };
}
