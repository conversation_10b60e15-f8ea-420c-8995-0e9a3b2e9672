import {
  useContext, useEffect,
  useState,
} from 'react';

import { PurchaseOrderConditions } from '#application/store/Store.Type';
import { OrderConditionsHttps, PostPurchaseOrderConditionsProps } from '#infrastructure/api/http/OrderConditions.Http';
import { AuthContext } from '#infrastructure/AuthState.Context';

const useGetPurchaseOrderConditions = ({
  providerId,
  orderItems,
  isUpdate = false,
}:Omit<PostPurchaseOrderConditionsProps, 'token'>) => {
  const [orderDetails, setOrderDetails] = useState<PurchaseOrderConditions>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { getToken } = useContext(AuthContext);

  const fetchData = async () => {
    const token = await getToken();

    try {
      const result = await OrderConditionsHttps.postPurchaseOrderConditions({ token, providerId, orderItems });
      setOrderDetails(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (orderItems.length > 0) {
      setLoading(true);

      if (isUpdate) {
        timeoutId = setTimeout(fetchData, 1000);
      } else {
        fetchData();
      }
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [providerId, JSON.stringify(orderItems)]);

  return {
    orderDetails,
    loading,
    error: error as Error,
  };
};

export default useGetPurchaseOrderConditions;
