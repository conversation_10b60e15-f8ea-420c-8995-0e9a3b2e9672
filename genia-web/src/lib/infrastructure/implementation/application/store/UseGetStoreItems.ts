import { useContext, useEffect, useState } from 'react';

import { StoreHttps } from '#infrastructure/api/http/Store.Https';
import { AuthContext } from '#infrastructure/AuthState.Context';

import { GetStoreItemsProps, StoreItem } from '../../../../application/store/Store.Type';

const useGetStoreItems = ({
  pageSize = 10, page = 1, searchTerm, orderBy = 'readId', order = 'ASC', providers,
}: GetStoreItemsProps) => {
  const [items, setItems] = useState<StoreItem[] | null>(null);
  const [totalNumberOfItems, setTotalNumberOfItems] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { getToken } = useContext(AuthContext);

  useEffect(() => {
    if (!providers) {
      return;
    }

    const fetchData = async () => {
      const token = await getToken();
      try {
        const adjustedOrderBy = searchTerm ? undefined : orderBy;
        const adjustedOrder = searchTerm ? undefined : order;

        const result = await StoreHttps.getStoreItems({
          token, pageSize, page, searchTerm, orderBy: adjustedOrderBy, order: adjustedOrder, providers,
        });
        setItems(result.data.data);
        setTotalNumberOfItems(result.data.count);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    setLoading(true);
    fetchData();
  }, [pageSize, page, searchTerm, orderBy, order, providers]);

  return {
    items,
    totalNumberOfItems,
    loading,
    error: error as Error,
  };
};

export default useGetStoreItems;
