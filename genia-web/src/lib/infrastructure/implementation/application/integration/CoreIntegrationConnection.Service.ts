import { v4 } from 'uuid';

import IntegrationConnectionService from '#/lib/biizi/application/Integration/services/IntegrationConnection.Service';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';

const googleScopes = [
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.readonly',
  'profile',
  'email',
];

const createGoogleAuthUrl = (connectionIntentId: string): string => (
  'https://accounts.google.com/o/oauth2/v2/auth?'
  + `client_id=${GetEnv().GOOGLE_OAUTH_CLIENT_ID}`
  + `&redirect_uri=${window.location.origin}/integrations/auth/callback`
  + '&response_type=code'
  + `&scope=${encodeURIComponent(googleScopes.join(' '))}`
  + '&access_type=offline'
  + '&prompt=consent'
  + `&state=${encodeURIComponent(connectionIntentId)}`
);

const connectionMap: Record<string, (connectionIntentId: string) => string> = {
  gmail: createGoogleAuthUrl,
};

function openConnection(urlGenerator: (connectionIntentId: string) => string): Promise<{authCode: string}> {
  const width = 500;
  const height = 600;
  const left = window.screenX + (window.innerWidth - width) / 2;
  const top = window.screenY + (window.innerHeight - height) / 2;
  let popupTime: number = 0;

  const connectionIntentId = v4();
  const url = urlGenerator(connectionIntentId);

  const promise = new Promise<{authCode: string}>((resolve, reject) => {
    const popup = window.open(
      url,
      'Biizi Integration Connection',
      `width=${width},height=${height},left=${left},top=${top}`,
    );

    const handleMessage = (event: MessageEvent) => {
      const {
        origin,
        data: {
          type,
          code,
          intentId,
        } = {},
      } = event;

      if (origin !== window.location.origin) {
        return;
      }

      if (type === 'oauth-code' && code && intentId) {
        // Handle successful authentication

        if (intentId === connectionIntentId) {
          resolve({ authCode: code });
          window.removeEventListener('message', handleMessage);
        }
      }

      if (type === 'oauth-error' && intentId === connectionIntentId) {
        // Handle error
        reject(new Error('Authentication failed'));
        window.removeEventListener('message', handleMessage);
      }
    };

    window.addEventListener('message', handleMessage, false);

    // Handle popup closed without completion
    const popUpChecks = setInterval(() => {
      popupTime += 1000;
      if (popupTime > 5 * 60 * 1000) { // 5 minutes timeout
        clearInterval(popUpChecks);
        window.removeEventListener('message', handleMessage);
        reject(new Error('Authentication timed out'));
      }

      if (popup?.closed) {
        clearInterval(popUpChecks);
        window.removeEventListener('message', handleMessage);
        reject(new Error('Authentication cancelled'));
      }
    }, 1000);
  });

  return promise;
}

async function connect(params: { type: string }): Promise<{authCode: string}> {
  if (params.type === 'whatsapp') return { authCode: 'not-implemented' };

  if (!connectionMap[params.type]) {
    throw new Error(`Unsupported integration type: ${params.type}`);
  }

  const urlGenerator = connectionMap[params.type];
  const authCode = await openConnection(urlGenerator);

  return authCode;
}

const CoreIntegrationConnectionService: IntegrationConnectionService = {
  connect,
};

export default CoreIntegrationConnectionService;
