import IntegrationRepository, { IntegrationRepositoryCreateParams } from '#/lib/biizi/application/Integration/repositories/Integration.Repository';
import IntegrationEntity from '#/lib/biizi/domain/aggregates/integration/Integration.Entity';
import { IntegrationHttps } from '#infrastructure/api/http/Integration.Https';
import IntegrationsQuery from '#infrastructure/api/readModel/queries/Integrations.Query';

export enum IntegrationType {
  GMAIL = 'gmail',
  GCALENDAR = 'gcalendar',
  WHATSAPP = 'whatsapp',
}

async function createIntegration(integration: IntegrationRepositoryCreateParams & { token: string }): Promise<IntegrationEntity> {
  if (!Object.values(IntegrationType).includes(integration.type as IntegrationType)) {
    throw new Error(`Invalid integration type: ${integration.type}`);
  }

  let params = integration.params || {};
  let { token } = integration;

  if (integration.type === IntegrationType.WHATSAPP) {
    if (!integration.params?.whatsappToken || !integration.params?.whatsappPhoneId || !integration.params?.whatsappVerificationToken) {
      throw new Error('Missing required parameters for WhatsApp integration');
    }

    params = {
      whatsappPhoneId: integration.params.whatsappPhoneId,
      whatsappVerificationToken: integration.params.whatsappVerificationToken,
    };

    token = integration.params.whatsappToken;
  }

  const response = await IntegrationHttps.postIntegration({
    type: integration.type as IntegrationType,
    token,
    params,
  });

  return new IntegrationEntity(
    response.id,
    response.type,
    new Date(response.createdAt),
    new Date(response.updatedAt),
    response.params,
  );
}

async function deleteIntegration(id: string): Promise<void> {
  if (id === '') {
    throw new Error('Integration ID cannot be empty');
  }
  await IntegrationHttps.deleteIntegration(id);
}

async function findAllIntegrations(): Promise<IntegrationEntity[]> {
  const integrations = await IntegrationsQuery.getIntegrations();

  return integrations.map((integration) => new IntegrationEntity(
    integration.id,
    integration.type as IntegrationType,
    new Date(integration.createdAt),
    new Date(integration.updatedAt),
    integration.params || {},
  ));
}

const ServerIntegrationRepository: IntegrationRepository = {
  createIntegration,
  deleteIntegration,
  findAllIntegrations,
};

export default ServerIntegrationRepository;
