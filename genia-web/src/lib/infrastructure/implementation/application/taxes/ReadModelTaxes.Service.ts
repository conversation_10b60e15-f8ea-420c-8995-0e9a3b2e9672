import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { GetTableDataResponse } from '#appComponent/table/Table.Type';
import { Taxes } from '#application/taxes/Taxes.Type';

const GET_TAXES = gql`
  query GetTaxes {
    tax {
      id
      name
      value
      countryCode
      createdAt
      disabledAt
      type
      updatedAt
    }
    tax_aggregate {
      aggregate {
        count
      }
    }
  }
`;

const useGetTaxes = (isManualOrder?: boolean): GetTableDataResponse<Taxes> => {
  const [
    getTaxes, {
      data, loading, error, refetch,
    },
  ] = useLazyQuery(GET_TAXES, {
    fetchPolicy: 'network-only',
  });

  useEffect(() => {
    if (isManualOrder) {
      getTaxes();
    }
  }, [isManualOrder]);

  return {
    items: data?.tax || [],
    totalNumberOfItems: data?.tax_aggregate?.aggregate?.count || 0,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelTaxesService = {
  useGetTaxes,
};

export default ReadModelTaxesService;
