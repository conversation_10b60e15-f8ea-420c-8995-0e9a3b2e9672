import { gql, useApolloClient, useQuery } from '@apollo/client';
import { useContext } from 'react';

import { GetTableDataProps } from '#appComponent/table/Table.Type';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { CreateUserPayload, UpdateUserPayload, UsersHttp } from '#infrastructure/api/http/Users.Http';

const UsersQuery = gql`
  query GetUsers ($limit: Int, $offset: Int, $where: user_bool_exp) {
    user(
      limit: $limit,
      offset: $offset,
      where: $where
    ) {
      id
      email
      name
      lastName
      appPhoneNumber
      state
      userCompanies {
        company {
          id
          name
        }
        role
      }
    }
    user_aggregate {
      aggregate {
        count
      }
    }
  }
`;

const CurrentUserQuery = gql`
  query GetCurrentUser ($userId: uuid!) {
    user(where: { id: { _eq: $userId } }) {
      id
      email
      appPhoneNumber
      name
      lastName
      userCompanies {
        role
      }
      updatedAt
      createdAt
    }
  }
`;

const GetUserQuery = gql`
  query GetUser($id: uuid!) {
    user(where: {id: {_eq: $id}}) {
      id
      email
      name
      lastName
      appPhoneNumber
      userCompanies {
        company {
          id
          name
        }
        role
      }
    }
  }
`;

const useGetUsers = ({
  limit = 10, offset = 0, searchTerm,
}: GetTableDataProps) => {
  const searchCondition = searchTerm ? { email: { _ilike: `%${searchTerm}%` } } : {};
  const {
    data, loading, error, refetch,
  } = useQuery(UsersQuery, {
    variables: {
      limit,
      offset,
      where: searchCondition,
    },
    fetchPolicy: 'network-only',
  });

  return {
    items: data?.user,
    totalNumberOfItems: data?.user_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetCurrentUser = () => {
  const { systemUser } = useContext(AuthContext);

  const {
    data, loading, error, refetch,
  } = useQuery(CurrentUserQuery, {
    variables: {
      userId: systemUser.userId,
    },
    skip: !systemUser.userId,
  });

  const user = data?.user?.[0] ? {
    id: data.user[0].id,
    email: data.user[0].email,
    picture: undefined,
    name: data.user[0].name,
    role: data.user[0].userCompanies?.[0]?.role || UserRole.USER,
    companyId: data.user[0].userCompanies?.[0]?.company?.id,
    createdAt: data.user[0].createdAt,
    updatedAt: data.user[0].updatedAt,
  } : null;

  return {
    user,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetUser = (id: string) => {
  const {
    data, loading, error, refetch,
  } = useQuery(GetUserQuery, {
    variables: { id },
    skip: !id,
  });

  const user = data?.user?.[0];
  const transformedUser = user ? {
    id: user.id,
    email: user.email,
    name: user.name,
    lastName: user.lastName,
    phoneNumber: user.appPhoneNumber,
    disabledAt: null, // Todos los usuarios activos por ahora
    companies: user.userCompanies?.map((uc: { company: { id: string } }) => uc.company.id) || [],
    role: user.userCompanies?.[0]?.role || UserRole.USER,
  } : null;

  return {
    user: transformedUser,
    loading,
    error: error as Error,
    refetch,
  };
};

const useCreateUser = () => {
  const { getToken } = useContext(AuthContext);

  const createUser = async (users: CreateUserPayload[]) => {
    try {
      const token = await getToken();
      const response = await UsersHttp.createUser(token, users);
      return response;
    } catch (error) {
      throw new Error('Error creating user');
    }
  };

  return { createUser };
};

const useUpdateUser = () => {
  const { getToken } = useContext(AuthContext);

  const updateUser = async (id: string, user: UpdateUserPayload) => {
    try {
      const token = await getToken();
      const response = await UsersHttp.updateUser(token, id, user);
      return response;
    } catch (error) {
      throw new Error('Error updating user');
    }
  };

  return { updateUser };
};

const useUpdateUserState = () => {
  const { getToken } = useContext(AuthContext);

  const updateUserState = async (userId: string, state: 'ACTIVE' | 'DISABLED') => {
    try {
      const token = await getToken();
      await UsersHttp.updateUserState(token, userId, { state });
    } catch (error) {
      throw new Error('Error updating user state');
    }
  };

  return { updateUserState };
};

const useUpdateUserRole = () => {
  const { getToken } = useContext(AuthContext);

  const updateUserRole = async (userId: string, role: UserRole) => {
    try {
      const token = await getToken();
      await UsersHttp.updateUserRole(token, userId, { role });
    } catch (error) {
      throw new Error('Error updating user role');
    }
  };

  return { updateUserRole };
};

const useRefetchUsers = () => {
  const client = useApolloClient();

  const refetchUsers = async () => {
    await client.refetchQueries({
      include: [UsersQuery],
    });
  };

  return { refetchUsers };
};

const ReadModelUserOrderService = {
  useGetUsers,
  useGetCurrentUser,
  useGetUser,
  useCreateUser,
  useUpdateUser,
  useUpdateUserState,
  useUpdateUserRole,
  useRefetchUsers,
};

export default ReadModelUserOrderService;
