import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

const query = gql`
  query GetCompanyId {
    userCompany {
      companyId
    }
  }
`;

export const useGetCompanyId = () => {
  const [getCompanyId, { data, loading, error }] = useLazyQuery(query);
  useEffect(() => {
    getCompanyId();
  }, []);
  return {
    companyId: data?.userCompany[0]?.companyId,
    loading,
    error,
  };
};
