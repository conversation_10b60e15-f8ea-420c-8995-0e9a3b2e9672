import { AxiosResponse } from 'axios';
import {
  useCallback, useContext, useEffect, useState,
} from 'react';

import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';
import { createNodeAxios } from '#infrastructure/api/http/HttpClient.Http';

import { GetTableDataProps, GetTableDataResponse } from '../../../../application/deprecated/DashboardPages.Type';
import { AuthContext } from '../../../AuthState.Context';

import { useGetCompanyId } from './UseGetCompanyId.Hook';

interface CatalogResponse extends AxiosResponse {
  count: number;
}

export const useGetCatalogByReadId = ({
  limit = 10, offset = 1, filters, searchTerm = '', providerId, canTrigger,
}: Omit<GetTableDataProps, 'orderBy' | 'order'> & { searchTerm?: string, canTrigger?: boolean }): GetTableDataResponse<ProviderCatalogItem> => {
  const { getToken } = useContext(AuthContext);
  const { companyId } = useGetCompanyId();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [items, setItems] = useState<ProviderCatalogItem[]>([]);
  const [totalNumberOfItems, setTotalNumberOfItems] = useState(0);

  const filterString = JSON.stringify(filters);

  const fetchData = useCallback(async () => {
    if (!companyId || !providerId) return;

    setLoading(true);
    try {
      const token = await getToken();
      const nodeAxios = createNodeAxios(token || '');

      const filterParams: Record<string, string> = {};

      const queryParams = new URLSearchParams({
        orderBy: 'readId',
        pageSize: limit.toString(),
        page: offset.toString(),
        providers: providerId || '',
        ...filterParams,
      });

      if (searchTerm) {
        queryParams.append('searchTerm', searchTerm);
      }

      const response = await nodeAxios.get(`providers/catalog?${queryParams.toString()}`) as CatalogResponse;

      setItems(response.data.data || []);
      setTotalNumberOfItems(response.data.count || 0);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [companyId, getToken, filterString, limit, offset, searchTerm, providerId]);

  useEffect(() => {
    if (canTrigger) {
      fetchData();
    }
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    items,
    totalNumberOfItems,
    loading,
    error: error as Error,
    refetch,
  };
};
