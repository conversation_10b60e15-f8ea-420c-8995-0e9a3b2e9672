import { useContext } from 'react';
import { useParams } from 'react-router-dom';

import { StoreDiscountHttps } from '#infrastructure/api/http/StoreDiscount.Https';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';

import { StoreDiscountItem } from '../../../../application/deprecated/DashboardPages.Type';
import { AuthContext } from '../../../AuthState.Context';
import {
  handleDiscountError, handleDiscountResponse,
} from '../utils/discountService';

export const useStoreDiscount = () => {
  const { id } = useParams();
  const goToPath = CorePath.useGoToPath();
  const { getToken } = useContext(AuthContext);

  const saveDiscount = async (payload: Partial<StoreDiscountItem>) => {
    const token = await getToken();
    try {
      const response = id
        ? await StoreDiscountHttps.updateStoreDiscount(token, id, payload)
        : await StoreDiscountHttps.createStoreDiscount(token, payload);

      if (!handleDiscountResponse(response)) return;

      if (response?.data?.[0]?.id) {
        goToPath(CorePath.storeDiscounts.viewStoreDiscount(response?.data?.[0]?.id));
      }
    } catch (err) {
      handleDiscountError();
    }
  };

  return { saveDiscount };
};
