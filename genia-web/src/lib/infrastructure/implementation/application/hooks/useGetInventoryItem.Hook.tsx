import { useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { GET_INVENTORY_ITEM } from '#infrastructure/api/readModel/queries/GetInventoryItem.Query';

export const useGetInventoryItem = (id?: string) => {
  const [getInventoryItem, {
    data, loading, error,
  }] = useLazyQuery(GET_INVENTORY_ITEM, {
    fetchPolicy: 'network-only',
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id) {
      getInventoryItem();
    }
  }, [id]);

  return {
    inventoryItem: data?.inventory[0],
    loading,
    error,
  };
};
