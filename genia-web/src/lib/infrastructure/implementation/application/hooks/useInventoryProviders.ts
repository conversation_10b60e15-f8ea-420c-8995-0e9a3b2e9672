import { useContext } from 'react';

import { Provider } from '#application/provider/Provider.Type';
import { InventoryHttps } from '#infrastructure/api/http/Inventory.api';

import { AddProviderInventory, ProviderByInventoryId } from '../../../../application/deprecated/DashboardPages.Type';
import { AuthContext } from '../../../AuthState.Context';
import { useGetProviders } from '../provider/ReadModelProvider.Service';

import { useGetProvidersByInventoryItem } from './useGetProviderByInventoryItem';

const createPayload = (localProviders: ProviderByInventoryId[]) => ({
  providers: localProviders?.map((provInventory: ProviderByInventoryId) => ({
    id: provInventory?.id,
    name: provInventory?.name,
    currentPurchasePrice: provInventory?.providerInventories?.[0]?.currentPurchasePrice,
    currentDiscount: provInventory?.providerInventories?.[0].currentDiscount,
  })),
});

export const useInventoryProviders = (id: string) => {
  const { getToken } = useContext(AuthContext);

  const { providersByInventoryId, refetch } = useGetProvidersByInventoryItem(id || '');

  const { items } = useGetProviders({
    limit: 10, offset: 1, orderBy: 'name', order: 'asc',
  });

  const updateProviders = async (newProviders: ProviderByInventoryId[]) => {
    const token = await getToken();

    const payload = createPayload(newProviders);

    const response = await InventoryHttps.updateInventory(token, id, payload);

    if (response?.status === 200) {
      refetch(newProviders);
    }
  };

  const addProvider = async (provider?: AddProviderInventory) => {
    const newProviders = [...providersByInventoryId, provider];
    await updateProviders(newProviders);
  };

  const updateProvider = async (oldProvider?: AddProviderInventory | null, newProvider?: AddProviderInventory) => {
    if (!oldProvider || !newProvider) return;

    const updatedProviders = providersByInventoryId.map((provider: Provider) => (provider.id === oldProvider?.id ? newProvider : provider));
    await updateProviders(updatedProviders);
  };

  const removeProvider = async (providerId: string) => {
    const newProviders = providersByInventoryId?.filter((prov: ProviderByInventoryId) => prov.id !== providerId);
    await updateProviders(newProviders);
  };

  return {
    providersByInventoryId,
    items,
    removeProvider,
    updateProvider,
    addProvider,
  };
};
