import { useContext } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { NotificationsHttp } from '#infrastructure/api/http/Notifications.Http';

export const useNotificationActions = () => {
  const { getToken } = useContext(AuthContext);

  const markAsRead = async (notificationIds: string[]) => {
    const token = await getToken();

    try {
      await NotificationsHttp.markAsRead(token, notificationIds);
    } catch (error) {
      Notification({ message: 'Error al marcar como leída', type: MSG_ERROR_TYPES.ERROR });
      throw error;
    }
  };

  const markAllAsRead = async () => {
    const token = await getToken();

    try {
      await NotificationsHttp.markAllAsRead(token);
    } catch (error) {
      Notification({ message: 'Error al marcar como leída', type: MSG_ERROR_TYPES.ERROR });
      throw error;
    }
  };

  const deleteNotification = async (notificationId: string) => {
    const token = await getToken();

    try {
      await NotificationsHttp.deleteNotification(token, notificationId);
    } catch (error) {
      Notification({ message: 'Error al eliminar notificación', type: MSG_ERROR_TYPES.ERROR });
      throw error;
    }
  };

  return {
    markAsRead,
    markAllAsRead,
    deleteNotification,
  };
};
