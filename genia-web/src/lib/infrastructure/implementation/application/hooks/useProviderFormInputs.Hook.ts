import {
  FormInput, InputComponent, isRequired, singleWord,
} from '@pitsdepot/storybook';

import CustomImageInputWithOutFunctionality from '#appComponent/form/CustomImageInputWithOutFunctionality.Component';
import {
  INPUT_NAME, LABELS, PLACEHOLDERS, TOOLTIPS,
} from '#application/provider/modules/constants/ProviderForm.Constants';
import { Provider } from '#application/provider/Provider.Type';

import { useForm } from './useForm.Hook';

export const useProviderFormInputs = (initialState: Partial<Provider>, edition: boolean) => {
  const {
    name, tributaryId, phone, address, notificationEmail, managerName, formState, onInputChange, resetForm,
  } = useForm<Partial<Provider>>(initialState);

  const inputs: FormInput[] = [
    {
      label: LABELS.NAME,
      component: InputComponent,
      validate: isRequired,
      props: {
        isRequired: true,
        name: INPUT_NAME.NAME,
        value: name,
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.NAME,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.NAME,
          },
        },
      },
    },
    {
      label: LABELS.TRIBUTARY_ID,
      component: InputComponent,
      validate: singleWord,
      props: {
        name: INPUT_NAME.TRIBUTARY_ID,
        value: tributaryId || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.TRIBUTARY_ID,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.TRIBUTARY_ID,
          },
        },
      },
    },
    {
      label: LABELS.PHONE,
      component: InputComponent,
      props: {
        name: INPUT_NAME.PHONE,
        value: phone || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.PHONE,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.PHONE,
          },
        },
      },
    },
    {
      label: LABELS.ADDRESS,
      component: InputComponent,
      props: {
        name: INPUT_NAME.ADDRESS,
        value: address || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.ADDRESS,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.ADDRESS,
          },
        },
      },
    },
    {
      label: LABELS.NOTIFICATION_EMAIL,
      component: InputComponent,
      props: {
        name: INPUT_NAME.NOTIFICATION_EMAIL,
        value: notificationEmail || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.NOTIFICATION_EMAIL,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.NOTIFICATION_EMAIL,
          },
        },
      },
    },
    {
      label: LABELS.MANAGER_NAME,
      component: InputComponent,
      props: {
        name: INPUT_NAME.MANAGER_NAME,
        value: managerName || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: PLACEHOLDERS.MANAGER_NAME,
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.MANAGER_NAME,
          },
        },
      },
    },
    {
      label: LABELS.MEDIA,
      component: CustomImageInputWithOutFunctionality,
      props: {
        name: INPUT_NAME.MEDIA,
        value: '',
        onChange: () => { },
        className: 'mt-2',
        disabled: !edition,
        info: {
          tooltip: {
            content: TOOLTIPS.MEDIA,
          },
        },
      },
    },
  ];

  return {
    inputs,
    formState,
    resetForm,
  };
};
