import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { Provider } from '#application/provider/Provider.Type';

export const getProviderQuery = gql`
    query GetProvider($id: uuid!) {
        provider(where: {id: {_eq: $id}}) {
            id
            name
            tributaryId
            providerCompanyId
        }
    }
`;

const getProviderByIdQuery = gql`
    query GetProviderByCompanyId($companyId: uuid!, $providerCompanyId: uuid!) {
      provider(where: {company_id: {_eq: $companyId}, providerCompanyId: {_eq: $providerCompanyId}}) {
        id
        name
        tributaryId
        providerCompanyId
      }
    }
`;

export const useGetProvider = (id?: string | number, autoFetch: boolean = true) => {
  const [getProvider, {
    loading, data, error, refetch,
  }] = useLazyQuery(getProviderQuery, {
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id && autoFetch) {
      getProvider();
    }
  }, [id, autoFetch]);

  return {
    provider: data?.provider[0] as Provider,
    loading,
    error,
    refetch,
  };
};

interface GetProviderByCompanyIdProps {
  companyId?: string;
  providerCompanyId?: string;
  autoFetch?: boolean;
}

export const useGetProviderByCompanyId = ({
  companyId,
  providerCompanyId,
  autoFetch = true,
}: GetProviderByCompanyIdProps) => {
  const [getProvider, {
    loading, data, error, refetch,
  }] = useLazyQuery(getProviderByIdQuery, {
    variables: {
      companyId,
      providerCompanyId,
    },
  });

  useEffect(() => {
    if (companyId && providerCompanyId && autoFetch && !loading) {
      getProvider();
    }
  }, [companyId, autoFetch, providerCompanyId, loading]);

  return {
    provider: data?.provider[0] as Provider,
    loading,
    error,
    refetch,
  };
};
