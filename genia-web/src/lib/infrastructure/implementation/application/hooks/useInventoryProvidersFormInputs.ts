import {
  combineValidators,
  FormInput, FormInputType, InputComponent,
  InputPrice,
  isPositiveNumer,
  isRequired,
} from '@pitsdepot/storybook';

import { discountTooltip, priceTooltip, providersFormConstants } from '#appComponent/inventoryProviders/ProvidersForm.constant';

import { useForm } from './useForm.Hook';

export const useInventoryProvidersFormInputs = (initialState: { price: number, discount: number}, editMode?: boolean) => {
  const {
    price, discount, onInputChange, formState,
  } = useForm(initialState);

  const handleBlur = (value?: string) => (Number(value) > 100 ? 'Debe ser menor a 100%' : undefined);

  const inputs: FormInput[] = [
    {
      label: providersFormConstants.PRICE,
      component: InputPrice,
      props: {
        isRequired: true,
        name: 'price',
        value: price,
        onChange: onInputChange,
        inputType: FormInputType.Number,
        min: 0,
        disabled: !editMode,
        placeholder: 'Normal',
        info: {
          tooltip: {
            content: priceTooltip.content,
          },
        },
      },
      validate: combineValidators(isRequired, isPositiveNumer),
    },
    {
      label: providersFormConstants.DICOUNT_LABEL,
      component: InputComponent,
      props: {
        name: 'discount',
        value: discount,
        onChange: onInputChange,
        inputType: FormInputType.Number,
        onBlur: handleBlur,
        className: 'mt-[1px]',
        min: 0,
        max: 100,
        disabled: !editMode || !price,
        placeholder: 'Normal',
        info: {
          tooltip: {
            content: discountTooltip.content,
          },
        },
      },
      validate: combineValidators(handleBlur, isPositiveNumer),
    },

  ];

  return {
    inputs,
    formState,
  };
};
