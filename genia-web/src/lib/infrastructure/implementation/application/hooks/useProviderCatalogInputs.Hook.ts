/* eslint-disable max-len */
import {
  FormInput,
  InputComponent,
  TextAreaInput,
} from '@pitsdepot/storybook';

import { CatalogItem } from '#application/catalog/Catalog.Type';
import { useForm } from '#infrastructure/implementation/application/hooks/useForm.Hook';

import {
  INPUT_NAME, LABELS,
} from '../../../../application/catalog/modules/Catalog.Constants';
import { TOOLTIPS } from '../../../../application/constants/texts/ProvidersCatalogDetails.Constants';

export const useProviderCatalogInputs = (initialState: Partial<CatalogItem>) => {
  const {
    name = '',
    description = '',
    readId = '',
    price = '',
    type = '',
  } = useForm<Partial<CatalogItem>>(initialState);

  const inputs: FormInput[] = [
    {
      label: LABELS.PRODUCT_NAME,
      component: InputComponent,
      props: {
        name: INPUT_NAME.NAME,
        value: name || '',
        info: {
          tooltip: {
            content: TOOLTIPS.PRODUCT_NAME,
          },
        },
      },
    },
    {
      label: LABELS.ID,
      component: InputComponent,
      props: {
        name: INPUT_NAME.READ_ID,
        value: readId || '',
        info: {
          tooltip: {
            content: TOOLTIPS.ID,
          },
        },
      },
    },
    {
      label: LABELS.TYPE,
      component: InputComponent,
      props: {
        name: INPUT_NAME.TYPE,
        value: type || '',
        info: {
          tooltip: {
            content: TOOLTIPS.TYPE,
          },
        },
      },
    },
    {
      label: LABELS.DESCRIPTION,
      component: TextAreaInput,
      formInputClassName: 'row-start-2 row-end-5 col-start-1 col-end-2',
      props: {
        name: INPUT_NAME.DESCRIPTION,
        value: description || '',
        className: 'mt-2 !h-[270px]',
        info: {
          tooltip: {
            content: TOOLTIPS.DESCRIPTION,
          },
        },
      },
    },
    {
      label: LABELS.PRICE,
      component: InputComponent,
      props: {
        name: INPUT_NAME.PRICE,
        value: `$${price}` || '',
        className: '!text-end !text-xl font-semibold',
        info: {
          tooltip: {
            content: TOOLTIPS.PRICE,
          },
        },
      },
    },
  ];

  const inputsMapped = inputs?.map((input) => ({
    ...input,
    props: {
      ...input.props,
      className: `mt-2 !cursor-text ${input.props.className || ''}`,
      disabled: true,
    },
  }));

  return {
    inputs: inputsMapped,
  };
};
