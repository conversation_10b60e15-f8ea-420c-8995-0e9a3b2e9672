import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

const GET_CATALOG_ITEMS = gql`
  query GetCatalogItems($ids: [uuid!]!) {
    catalog(where: { id: { _in: $ids } }) {
      id
      name
      readId
    }
  }
`;

export interface CatalogItem {
  id: string;
  name: string;
  readId: string;
}

export interface UseGetCatalogItemsResponse {
  catalogItems: CatalogItem[];
  loading: boolean;
  error: Error | undefined;
}

export const useGetCatalogItems = (ids: string[]): UseGetCatalogItemsResponse => {
  const [getCatalogItems, { data, loading, error }] = useLazyQuery(GET_CATALOG_ITEMS);

  useEffect(() => {
    if (ids.length > 0) {
      getCatalogItems({ variables: { ids } });
    }
  }, [ids, getCatalogItems]);

  return {
    catalogItems: data?.catalog || [],
    loading,
    error,
  };
};
