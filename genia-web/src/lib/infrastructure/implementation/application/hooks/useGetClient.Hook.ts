import { useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { Client } from '#application/client/Client.Type';
import { GET_CLIENT } from '#infrastructure/api/readModel/queries/GetClient.Query';

export const useGetClient = (
  id?: string | number,
  autoFetch: boolean = true,
) => {
  const [getClient, {
    data, loading, error, refetch,
  },
  ] = useLazyQuery(GET_CLIENT, {
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id && autoFetch) {
      getClient();
    }
  }, [id, autoFetch]);

  return {
    client: data?.client[0] as Client,
    loading,
    error,
    refetch,
  };
};
