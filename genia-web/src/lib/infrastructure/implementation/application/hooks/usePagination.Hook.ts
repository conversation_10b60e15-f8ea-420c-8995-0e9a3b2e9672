import { useCallback, useMemo, useState } from 'react';

export function usePagination(itemsPerPage: number) {
  const [currentPage, setCurrentPage] = useState(1);

  const offset = useMemo(() => (currentPage - 1) * itemsPerPage, [currentPage, itemsPerPage]);

  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage((prevPage) => (newPage !== prevPage ? newPage : prevPage));
  }, []);

  return useMemo(() => ({
    currentPage,
    offset,
    setCurrentPage,
    handlePageChange,
  }), [currentPage, offset, handlePageChange]);
}
