import { gql, useQuery } from '@apollo/client';
import { useMemo } from 'react';

import { transformNotification } from '#application/notifications/utils/NotificationTransformer';
import { Notification } from '#domain/types/Notification.Type';

interface UseGetNotificationsOptions {
  limit?: number;
  offset?: number;
  pollInterval?: number;
}

interface NotificationQueryResult {
  id: string;
  ownerUserId: string;
  company: {
    name: string;
  };
  payload: Record<string, unknown>;
  requiredRoles: string[];
  type: string;
  createdAt: string;
}

const GET_NOTIFICATIONS = gql`
  query GetNotifications($limit: Int, $offset: Int) {
    notification(
      order_by: { createdAt: desc }
      limit: $limit
      offset: $offset
    ) {
      id
      ownerUserId
      company {
        name
      }
      payload
      requiredRoles
      type
      createdAt
    }
  }
`;

export const useGetNotifications = (options: UseGetNotificationsOptions = {}) => {
  const {
    limit = 25,
    offset = 0,
    pollInterval = 30000, // Poll every 30 seconds for new notifications
  } = options;

  const {
    data, loading, error, refetch,
  } = useQuery<{
    notification: NotificationQueryResult[];
  }>(GET_NOTIFICATIONS, {
    variables: { limit, offset },
    pollInterval,
    errorPolicy: 'all',
    fetchPolicy: 'cache-and-network',
  });

  const notifications = useMemo(() => {
    if (!data?.notification) return [];

    return data.notification.map((notificationData): Notification => transformNotification({
      id: notificationData.id,
      ownerUserId: notificationData.ownerUserId,
      company: notificationData.company,
      company_id: notificationData.company?.name || '',
      payload: notificationData.payload,
      requiredRoles: notificationData.requiredRoles,
      type: notificationData.type,
      createdAt: new Date(notificationData.createdAt),
    }));
  }, [data]);

  return {
    notifications,
    loading,
    error,
    refetch,
  };
};

// Hook for unread notifications only
export const useGetUnreadNotifications = () => useGetNotifications({
  limit: 10,
  pollInterval: 15000, // More frequent polling for unread notifications
});
