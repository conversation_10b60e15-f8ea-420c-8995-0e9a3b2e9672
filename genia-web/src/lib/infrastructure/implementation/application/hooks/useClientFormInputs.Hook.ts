import {
  FormInput, InputComponent, isRequired, singleWord,
} from '@pitsdepot/storybook';

import CustomImageInputWithOutFunctionality from '#appComponent/form/CustomImageInputWithOutFunctionality.Component';
import { Client } from '#application/client/Client.Type';
import TextService from '#composition/textService/Text.Service';

import { useForm } from './useForm.Hook';

export const useClientFormInputs = (initialState: Partial<Client>, edition: boolean) => {
  const {
    name, tributaryId, formState, onInputChange, resetForm,
  } = useForm<Partial<Client>>(initialState);

  const text = TextService.getText();

  const INPUT_NAME = {
    NAME: 'name',
    TRIBUTARY_ID: 'tributaryId',
    MEDIA: 'image',
  };

  const inputs: FormInput[] = [
    {
      label: text.clients.clientCompanyName,
      component: InputComponent,
      validate: isRequired,
      props: {
        isRequired: true,
        name: INPUT_NAME.NAME,
        value: name,
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: text.clients.clientCompanyName,
        disabled: !edition,
        info: {
          tooltip: {
            content: 'Nombre de la empresa cliente',
          },
        },
      },
    },
    {
      label: text.clients.tributaryId,
      component: InputComponent,
      validate: singleWord,
      props: {
        name: INPUT_NAME.TRIBUTARY_ID,
        value: tributaryId || '',
        onChange: onInputChange,
        className: 'mt-2',
        placeholder: text.clients.tributaryId,
        disabled: !edition,
        info: {
          tooltip: {
            content: 'Identificador tributario del cliente',
          },
        },
      },
    },
    {
      label: 'Agregar Imagen',
      component: CustomImageInputWithOutFunctionality,
      props: {
        name: INPUT_NAME.MEDIA,
        value: '',
        onChange: () => { },
        className: 'mt-2',
        disabled: !edition,
        info: {
          tooltip: {
            content: 'Imagen del cliente',
          },
        },
      },
    },
  ];

  return {
    inputs,
    formState,
    resetForm,
  };
};
