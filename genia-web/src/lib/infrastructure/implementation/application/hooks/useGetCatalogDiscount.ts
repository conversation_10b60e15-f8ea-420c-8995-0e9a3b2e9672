import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { CatalogDiscountItem } from '../../../../application/deprecated/DashboardPages.Type';

const GET_CATALOG_DISCOUNT_BY_ID = gql`
  query GetCatalogDiscountById($id: uuid!) {
    catalog_discount_by_pk(id: $id) {
      name
      discountType
      discountValue
      requiredQuantity
      startDate
      endDate
      disabledAt
      companyId
      id
      catalog_discount_clients {
      clientId
        client {
          name
        }
      }
      catalogs {
        catalog {
          name
          readId
          id
          catalog_media {
            url
            mediaType
            id
          }
        }
      }
    }
  }
`;

export const useGetCatalogDiscount = (
  id?: string | number,
) => {
  const [catalogDiscount, {
    data, loading, error, refetch,
  },
  ] = useLazyQuery(GET_CATALOG_DISCOUNT_BY_ID, {
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id) {
      catalogDiscount();
    }
  }, [id]);

  return {
    catalogDiscount: data?.catalog_discount_by_pk as CatalogDiscountItem,
    loading,
    error,
    refetch,
  };
};
