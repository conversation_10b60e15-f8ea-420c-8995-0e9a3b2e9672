import {
  combineValidators,
  FormInput,
  InputComponent,
  InputPrice,
  isNumeric,
  isPositiveNumer,
  isRequired,
  maxLength,
  SelectInput,
  TextAreaInput,
} from '@pitsdepot/storybook';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import CustomImageViewer from '#appComponent/common/CustomImageViewer.AppComponent';
import CustomBottonInputChildren from '#appComponent/form/CustomBottomInputChildren.Component';
import CustomCheckBox from '#appComponent/form/CustomCheckbox.Component';
import CustomEnableDisableInput from '#appComponent/form/CustomEnableDisableInput.Component';
import CustomSkuInput from '#appComponent/form/CustomSkuInput.Component';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import {
  INPUT_NAME, LA<PERSON>LS, PLACEHOLDERS, TOOLTIPS,
} from '#application/catalog/modules/Catalog.Constants';
import TextService from '#composition/textService/Text.Service';
import { useForm } from '#infrastructure/implementation/application/hooks/useForm.Hook';

const ivaTax = 1.16;
const textService = TextService.getText();

export const useCatalogInputs = (initialState: Partial<CatalogItem> & {active?: boolean}, editMode?: boolean) => {
  const { id } = useParams();
  const {
    name,
    readId,
    type,
    description,
    price,
    catalog_media: catalogMedia,
    formState,
    onInputChange,
    resetForm,
    active,
    disabledAt,
    catalogTax,
  } = useForm<Partial<CatalogItem> & {active?: boolean}>(initialState);
  const [isIdAutoGenerated, setIsIdAutoGenerated] = useState<boolean>(false);
  const [isEnable, setIsEnable] = useState<boolean>(active || false);
  const [isTaxEnable, setIsTaxEnable] = useState<boolean>(true);

  const [images, setImages] = useState<(File | { id: string; url: string; name: string })[]>([]);

  const onImageChange = useCallback((files: (File | { id: string; url: string; name: string })[]) => {
    setImages(files);
  }, []);

  useEffect(() => {
    if (catalogTax?.length && catalogTax.length > 0) {
      setIsTaxEnable(true);
    }
  }, [JSON.stringify(catalogTax)]);

  const inputs: FormInput[] = [
    {
      label: LABELS.PRODUCT_NAME,
      component: InputComponent,
      props: {
        isRequired: true,
        name: INPUT_NAME.NAME,
        value: name,
        onChange: onInputChange,
        className: 'mt-2',
        disabled: !editMode,
        placeholder: PLACEHOLDERS.PRODUCT_NAME,
        info: {
          tooltip: {
            content: TOOLTIPS.PRODUCT_NAME,
          },
        },
      },
      validate: combineValidators(maxLength(80), isRequired),
    },
    {
      label: LABELS.ID,
      component: CustomSkuInput,
      props: {
        isRequired: true,
        name: INPUT_NAME.READ_ID,
        value: readId || '',
        onChange: onInputChange,
        checkBoxLabel: LABELS.ID_AUTOGENERATE,
        disabled: !editMode,
        placeholder: isIdAutoGenerated ? PLACEHOLDERS.ID_AUTOGENERATE : PLACEHOLDERS.ID,
        info: {
          tooltip: {
            content: TOOLTIPS.ID,
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsIdAutoGenerated(isChecked);
        },
      },
      validate: isIdAutoGenerated ? undefined : isRequired,
    },
    {
      label: LABELS.TYPE,
      component: SelectInput,
      props: {
        isRequired: true,
        name: INPUT_NAME.TYPE,
        value: type || '',
        onChange: onInputChange,
        disabled: !editMode,
        className: 'mt-2',
        options: [
          { value: 'product', placeholder: 'Product' },
          { value: 'service', placeholder: 'Service' },
        ],
        info: {
          tooltip: {
            content: TOOLTIPS.TYPE,
          },
        },
      },
      validate: isRequired,
    },
    {
      label: LABELS.DESCRIPTION,
      component: TextAreaInput,
      formInputClassName: 'row-start-2 row-end-4 col-start-2 h-content',
      validate: combineValidators(maxLength(500), isRequired),
      props: {
        isRequired: true,
        name: INPUT_NAME.DESCRIPTION,
        value: description,
        onChange: onInputChange,
        className: 'mt-2 !h-[270px]',
        disabled: !editMode,
        placeholder: PLACEHOLDERS.DESCRIPTION,
        info: {
          tooltip: {
            content: TOOLTIPS.DESCRIPTION,
          },
        },
      },
    },
    {
      label: LABELS.ADD_IMAGE,
      component: () => CustomImageViewer({
        catalogMedia,
        readId,
        onChange: onImageChange,
        editMode,
        entity: 'catalog',
        entityId: id,
      }),
      formInputClassName: 'min-w-0',
      props: {
        name: 'INPUT_NAME.IMAGE',
        value: '',
        onChange: onImageChange,
        className: 'mt-2',
        disabled: !editMode,
        info: {
          tooltip: {
            content: TOOLTIPS.ADD_IMAGE,
          },
        },
      },
    },
    {
      label: 'Precio antes de impuestos',
      component: InputPrice,
      props: {
        isRequired: true,
        name: INPUT_NAME.PRICE,
        value: price,
        onChange: onInputChange,
        disabled: !editMode,
        placeholder: PLACEHOLDERS.PRICE,
        className: 'mt-2',
        customLabelSibling: <CustomCheckBox
          onCheckBoxChange={(isChecked) => setIsTaxEnable(isChecked)}
          isChecked={isTaxEnable}
          disabled={!editMode}
          label='Incluir IVA' />,
        bottomChild: isTaxEnable && price !== undefined ? <CustomBottonInputChildren label='Precio con impuestos incluídos' value={price * ivaTax } /> : undefined,
        info: {
          tooltip: {
            content: textService.catalog.priceWithoutIva,
          },
        },
      },
      validate: combineValidators(isRequired, isNumeric, isPositiveNumer),
    },
    {
      label: LABELS.ENABLE_DISABLE,
      component: CustomEnableDisableInput,
      props: {
        name: INPUT_NAME.ENABLE_DISABLE,
        value: disabledAt !== undefined && (!disabledAt || isEnable) ? 'active' : 'inactive',
        onChange: onInputChange,
        switchLabel: (!disabledAt || isEnable) ? 'Activo' : 'Inactivo',
        disabled: !editMode,
        classNameContainer: 'mt-2',
        className: 'flex justify-start items-center',
        options: [
          { value: 'active', placeholder: 'Activo' },
          { value: 'inactive', placeholder: 'Inactivo' },
        ],
        initialCheck: disabledAt !== undefined ? Boolean((!disabledAt || isEnable)) : false,
        info: {
          tooltip: {
            content: TOOLTIPS.ACTIVE,
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsEnable(isChecked);
        },
      },
    },
  ];

  return {
    inputs,
    images,
    formState,
    resetForm,
    isIdAutoGenerated,
    isTaxEnable,
  };
};
