import { ChangeEvent, useEffect, useState } from 'react';

export const useForm = <T extends object> (initialForm: T) => {
  const [formState, setFormState] = useState(initialForm);
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setFormState(initialForm);
    setTouchedFields({});
  }, [initialForm]);

  const onInputChange = ({ target }: ChangeEvent<HTMLInputElement>) => {
    const {
      name, value, type, files,
    } = target;
    switch (type) {
      case 'file':
        setFormState({
          ...formState,
          [name]: files ? files[0].name : null,
        });
        break;
      case 'number':
        setFormState({
          ...formState,
          [name]: Number(value),
        });
        break;
      default:
        setFormState({
          ...formState,
          [name]: value,
        });
        break;
    }
    if (!touchedFields[name]) {
      setTouchedFields((prevTouchedFields) => ({
        ...prevTouchedFields,
        [name]: true,
      }));
    }
  };

  const resetForm = () => {
    setFormState(initialForm);
    setTouchedFields({});
  };

  return {
    ...formState,
    formState,
    onInputChange,
    touchedFields,
    resetForm,
  };
};
