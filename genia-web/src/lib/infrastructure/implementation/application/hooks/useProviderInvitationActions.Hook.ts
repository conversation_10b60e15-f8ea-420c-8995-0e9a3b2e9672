import { useContext } from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { ProviderInvitationPayload } from '#domain/types/ProviderInvitation.Type';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { ProviderInvitationsHttp } from '#infrastructure/api/http/ProviderInvitations.Http';

export const useProviderInvitationActions = () => {
  const { getToken } = useContext(AuthContext);

  const acceptInvitation = async (notificationId: string, payload: ProviderInvitationPayload) => {
    const token = await getToken();

    const invitationId = payload.invitationId || notificationId;

    try {
      await ProviderInvitationsHttp.acceptInvitation(token, invitationId);
      Notification({ message: 'Invitación aceptada exitosamente', type: MSG_ERROR_TYPES.SUCCESS });
    } catch (error) {
      Notification({ message: 'Error al aceptar invitación', type: MSG_ERROR_TYPES.ERROR });
      throw error;
    }
  };

  const rejectInvitation = async (notificationId: string, payload: ProviderInvitationPayload) => {
    const token = await getToken();

    const invitationId = payload.invitationId || notificationId;

    try {
      await ProviderInvitationsHttp.rejectInvitation(token, invitationId);

      Notification({ message: 'Invitación rechazada exitosamente', type: MSG_ERROR_TYPES.SUCCESS });
    } catch (error) {
      Notification({ message: 'Error al rechazar invitación', type: MSG_ERROR_TYPES.ERROR });
      throw error;
    }
  };

  return {
    acceptInvitation,
    rejectInvitation,
  };
};
