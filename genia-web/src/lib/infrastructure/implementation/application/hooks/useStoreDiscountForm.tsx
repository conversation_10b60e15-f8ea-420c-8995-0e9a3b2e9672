import { useContext } from 'react';
import { useParams } from 'react-router-dom';

import { StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';
import { StoreDiscountHttps } from '#infrastructure/api/http/StoreDiscount.Https';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { handleDiscountResponse } from '#infrastructure/implementation/application/utils/discountService';

export const useStoreDiscountForm = () => {
  const { id } = useParams();
  const { getToken } = useContext(AuthContext);

  const saveDiscount = async (payload: Partial<StoreDiscountItem>) => {
    const token = await getToken();
    const response = id
      ? await StoreDiscountHttps.updateStoreDiscount(token, id, payload).catch((error) => error)
      : await StoreDiscountHttps.createStoreDiscount(token, payload).catch((error) => error);

    return handleDiscountResponse(response);
  };

  const assignDiscountClients = async (payload: string[]) => {
    const token = await getToken();

    const response = await StoreDiscountHttps.assignStoreDiscountClients(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  const unAssignDiscountClients = async (payload: string[]) => {
    const token = await getToken();

    const response = await StoreDiscountHttps.unAssignStoreDiscountClients(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  return {
    saveDiscount,
    assignDiscountClients,
    unAssignDiscountClients,
  };
};
