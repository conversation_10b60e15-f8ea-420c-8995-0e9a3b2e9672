import {
  combineValidators,
  DateInputComponent,
  FormInputType,
  InputComponent,
  isRequired, maxLength,
  SelectInput,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import CustomEnableDisableInput from '#appComponent/form/CustomEnableDisableInput.Component';
import { LABELS } from '#application/catalog/modules/Catalog.Constants';
import { INPUT_NAME, PLACEHOLDERS, TOOLTIPS } from '#application/constants/texts/StoreDiscount.Constants';
import { StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';

import { useForm } from './useForm.Hook';

export interface StoreDiscountInputsProps {
  initialState: Partial<StoreDiscountItem>
  editMode?: boolean,
  id?: string;
}

export const useStoreDiscountInputs = ({ initialState, editMode, id }: StoreDiscountInputsProps) => {
  const {
    name,
    discountType,
    discountValue,
    startDate,
    requiredAmount,
    endDate,
    onInputChange,
    formState,
    disabledAt,
    active,
  } = useForm<Partial<StoreDiscountItem>>(initialState);

  const [isEnable, setIsEnable] = useState<boolean>(disabledAt === null);

  useEffect(() => {
    setIsEnable(disabledAt === null);
  }, [disabledAt]);

  const inputs = [
    {
      label: LABELS.DISCOUNT_NAME,
      component: InputComponent,
      validate: combineValidators(isRequired, maxLength(80)),
      props: {
        isRequired: true,
        name: INPUT_NAME.NAME,
        value: name,
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        min: 0,
        placeholder: PLACEHOLDERS.NAME,
        info: {
          tooltip: {
            content: TOOLTIPS.NAME,
          },
        },
      },
    },
    {
      label: LABELS.TYPE,
      component: SelectInput,
      validate: isRequired,
      props: {
        isRequired: true,
        name: INPUT_NAME.DISCOUNT_TYPE,
        value: discountType || '',
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        options: [
          { value: 'amount', placeholder: 'Amount' },
          { value: 'percentage', placeholder: 'Percentage' },
        ],
        info: {
          tooltip: {
            content: TOOLTIPS.DISCOUNT_TYPE,
          },
        },
      },
    },
    {
      label: LABELS.VALUE,
      component: InputComponent,
      props: {
        isRequired: true,
        name: INPUT_NAME.DISCOUNT_VALUE,
        value: discountValue || '',
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        inputType: FormInputType.Number,
        min: 0,
        max: discountType === 'percentage' ? 100 : undefined,
        placeholder: PLACEHOLDERS.DISCOUNT_VALUE,
        info: {
          tooltip: {
            content: TOOLTIPS.DISCOUNT_VALUE,
          },
        },
      },
    },
    {
      label: LABELS.REQUIRED_AMOUNT,
      component: InputComponent,
      props: {
        name: INPUT_NAME.REQUIRED_AMOUNT,
        value: requiredAmount || '',
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        inputType: FormInputType.Number,
        min: 0,
        placeholder: PLACEHOLDERS.REQUIRED_AMOUNT,
        info: {
          tooltip: {
            content: TOOLTIPS.REQUIRED_AMOUNT,
          },
        },
      },
    },
    {
      label: LABELS.START_DATE,
      component: DateInputComponent,
      props: {
        name: INPUT_NAME.START_DATE,
        isRequired: true,
        value: startDate,
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        info: {
          tooltip: {
            content: TOOLTIPS.START_DATE,
          },
        },
      },
      validate: isRequired,
    },
    {
      label: LABELS.END_DATE,
      component: DateInputComponent,
      props: {
        name: INPUT_NAME.END_DATE,
        value: endDate,
        className: 'mt-2',
        onChange: onInputChange,
        disabled: !editMode,
        info: {
          tooltip: {
            content: TOOLTIPS.END_DATE,
          },
        },
      },
    },
    {
      label: LABELS.ENABLE_DISABLE,
      component: CustomEnableDisableInput,
      props: {
        name: INPUT_NAME.ENABLE_DISABLE,
        value: active,
        onChange: onInputChange,
        switchLabel: isEnable ? 'Activo' : 'Inactivo',
        disabled: !editMode || !id,
        classNameContainer: 'mt-2',
        className: 'flex justify-start items-center',
        options: [
          { value: 'active', placeholder: 'Activo' },
          { value: 'inactive', placeholder: 'Inactivo' },
        ],
        initialCheck: isEnable || false,
        info: {
          tooltip: {
            content: 'Activa o desactiva el descuento.',
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsEnable(isChecked);
        },
      },
    },
  ];

  return {
    inputs,
    formState,
  };
};
