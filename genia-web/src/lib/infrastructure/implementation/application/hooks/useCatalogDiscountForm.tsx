import { useContext } from 'react';
import { useParams } from 'react-router-dom';

import { CatalogDiscountItem } from '#application/deprecated/DashboardPages.Type';
import { CatalogDiscountHttps } from '#infrastructure/api/http/CatalogDiscount.Http';
import { AuthContext } from '#infrastructure/AuthState.Context';
import {
  handleDiscountResponse,
} from '#infrastructure/implementation/application/utils/discountService';

export const useCatalogDiscountForm = () => {
  const { id } = useParams();
  const { getToken } = useContext(AuthContext);

  const saveDiscount = async (payload: CatalogDiscountItem) => {
    const token = await getToken();
    const response = id
      ? await CatalogDiscountHttps.updateCatalogDiscount(token, id, payload).catch((error) => error)
      : await CatalogDiscountHttps.createCatalogDiscount(token, payload).catch((error) => error);

    return handleDiscountResponse(response);
  };

  const assignDiscountClients = async (payload: string[]) => {
    const token = await getToken();

    const response = await CatalogDiscountHttps.assignCatalogDiscountClients(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  const unAssignDiscountClients = async (payload: string[]) => {
    const token = await getToken();

    const response = await CatalogDiscountHttps.unAssignCatalogDiscountClients(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  const assignDiscountCatalogs = async (payload: string[]) => {
    const token = await getToken();

    const response = await CatalogDiscountHttps.assignCatalogDiscountCatalogs(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  const unAssignDiscountCatalogs = async (payload: string[]) => {
    const token = await getToken();

    const response = await CatalogDiscountHttps.unAssignCatalogDiscountCatalogs(token, id || '', payload).catch((error) => error);
    return handleDiscountResponse(response);
  };

  return {
    saveDiscount,
    assignDiscountClients,
    unAssignDiscountClients,
    assignDiscountCatalogs,
    unAssignDiscountCatalogs,
  };
};
