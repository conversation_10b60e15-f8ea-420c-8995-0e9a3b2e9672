import { useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { GET_PROVIDERS_BY_INVENTORY_ITEM } from '#infrastructure/api/readModel/queries/GetProviderByInventoryItem.Query';

interface ProviderInvenriesProp {
  currentDiscount: number;
  currentPurchasePrice: number;
}

export interface Provider {
  id: string;
  name: string;
  providerInventories: ProviderInvenriesProp[],
}

export const useGetProvidersByInventoryItem = (id: string | number) => {
  const [getProviderByInventoryItem, {
    data, loading, error, refetch,
  }] = useLazyQuery(GET_PROVIDERS_BY_INVENTORY_ITEM, {
    variables: {
      inventoryId: id,
    },
  });

  useEffect(() => {
    if (id) {
      getProviderByInventoryItem();
    }
  }, [id]);

  return {
    providersByInventoryId: data?.provider,
    loading,
    error,
    refetch,
  };
};
