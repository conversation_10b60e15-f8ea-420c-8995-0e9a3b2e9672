import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { StoreDiscountItem } from '../../../../application/deprecated/DashboardPages.Type';

const GET_STORE_DISCOUNT_BY_ID = gql`
  query GetStoreDiscountById($id: uuid!) {
    store_discount(where: {id: {_eq: $id}}) {
      disabledAt
      discountType
      discountValue
      endDate
      id
      name
      requiredAmount
      startDate
      companyId
      storeDiscountClients {
        clientId
        client {
          name
        }
      }
    }
  }
`;

export const useGetStoreDiscount = (
  id?: string | number,
) => {
  const [getStoreDiscount, {
    data, loading, error, refetch,
  },
  ] = useLazyQuery(GET_STORE_DISCOUNT_BY_ID, {
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id) {
      getStoreDiscount();
    }
  }, [id]);

  return {
    storeDiscount: data?.store_discount[0] as StoreDiscountItem,
    loading,
    error,
    refetch,
  };
};
