import { useContext } from 'react';

import { CartContext } from '#infrastructure/pages/app/pageContexts/CartState.Context';

// import { CartContext } from '../../../pages/app/pageContexts/CartState.Context';

const defaultProviderCart = {
  items: [],
  summary: {
    subtotal: 0,
    total: 0,
  },
  isLoading: false,
};

/**
 * Custom hook to get current provider cart data
 * Returns only the data for the currently active provider
 */
export const useActiveProviderCart = () => {
  const { cartState, cartFunctions } = useContext(CartContext);
  const { activeProviderId, providerCarts } = cartState;

  const activeCart = providerCarts[activeProviderId] || defaultProviderCart;

  return {
    items: activeCart.items,
    summary: activeCart.summary,
    isLoading: activeCart.isLoading || false,
    activeProviderId,
    cartFunctions,
    isCartOpen: cartState.isCartOpen,
  };
};
