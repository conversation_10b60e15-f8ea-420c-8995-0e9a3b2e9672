import {
  combineValidators,
  DateInputComponent,
  FormInput, FormInputType,
  InputComponent, isPositiveNumer, isRequired, maxLength,
  SelectInput,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import CustomEnableDisableInput from '#appComponent/form/CustomEnableDisableInput.Component';
import { INPUT_NAME, LABELS } from '#application/catalog/modules/Catalog.Constants';
import { CatalogDiscountItem } from '#application/deprecated/DashboardPages.Type';

import { useForm } from './useForm.Hook';

export interface CatalogDiscountInputs {
  initialState: Partial<CatalogDiscountItem>
  editMode?: boolean,
  id?: string;
}

export const useCatalogDiscountInputs = ({ initialState, editMode, id }: CatalogDiscountInputs) => {
  const {
    name,
    discountType,
    discountValue,
    startDate,
    requiredQuantity,
    endDate,
    onInputChange,
    formState,
    disabledAt,
    active,
  } = useForm<Partial<CatalogDiscountItem>>(initialState);
  const [isEnable, setIsEnable] = useState<boolean>(disabledAt === null);

  useEffect(() => {
    setIsEnable(disabledAt === null);
  }, [disabledAt]);

  const inputs: FormInput[] = [
    {
      label: LABELS.DISCOUNT_NAME,
      component: InputComponent,
      validate: combineValidators(isRequired, maxLength(80)),
      props: {
        isRequired: true,
        name: 'name',
        value: name,
        onChange: onInputChange,
        className: 'mt-2',
        min: 0,
        disabled: !editMode,
        placeholder: 'Ej: 20% de descuento en productos de ...',
        info: {
          tooltip: {
            content: 'Agrega un nombre descriptivo para el descuento',
          },
        },
      },
    },
    {
      label: LABELS.TYPE,
      component: SelectInput,
      validate: isRequired,
      props: {
        isRequired: true,
        name: INPUT_NAME.DISCOUNT_TYPE,
        value: discountType || '',
        onChange: onInputChange,
        disabled: !editMode,
        className: 'mt-2',
        options: [
          { value: 'amount', placeholder: 'Amount' },
          { value: 'percentage', placeholder: 'Percentage' },
        ],
        info: {
          tooltip: {
            content: 'Selecciona si quieres descontar porcentajes o montos fijos',
          },
        },
      },
    },
    {
      label: LABELS.VALUE,
      component: InputComponent,
      validate: combineValidators(isPositiveNumer, isRequired),
      props: {
        isRequired: true,
        name: 'discountValue',
        value: discountValue || '',
        onChange: onInputChange,
        disabled: !editMode,
        inputType: FormInputType.Number,
        className: 'mt-2',
        min: 0,
        max: discountType === 'percentage' ? 100 : undefined,
        placeholder: 'Ej: $20 o 20%',
        info: {
          tooltip: {
            content: 'Ingresa el monto o porcentaje que quieres descontar',
          },
        },
      },
    },
    {
      label: LABELS.REQUIRED_QUANTITY,
      component: InputComponent,
      props: {
        name: 'requiredQuantity',
        value: requiredQuantity || '',
        onChange: onInputChange,
        inputType: FormInputType.Number,
        className: 'mt-2',
        min: 0,
        disabled: !editMode,
        placeholder: 'Escribe la cantidad de productos para activar el descuento',
        info: {
          tooltip: {
            content: 'Ej: 3 productos',
          },
        },
      },
    },
    {
      label: LABELS.START_DATE,
      component: DateInputComponent,
      props: {
        name: 'startDate',
        isRequired: true,
        value: startDate,
        onChange: onInputChange,
        className: 'mt-2',
        disabled: !editMode,
        info: {
          tooltip: {
            content: 'Ingresa la fecha cuando quieras iniciar el descuento',
          },
        },
      },
      validate: isRequired,
    },
    {
      label: LABELS.END_DATE,
      component: DateInputComponent,
      props: {
        name: 'endDate',
        value: endDate,
        onChange: onInputChange,
        className: 'mt-2',
        disabled: !editMode,
        info: {
          tooltip: {
            content: 'Ingresa la fecha cuando quieras finalizar el descuento',
          },
        },
      },
    },
    {
      label: LABELS.ENABLE_DISABLE,
      component: CustomEnableDisableInput,
      props: {
        name: INPUT_NAME.ENABLE_DISABLE,
        value: active,
        onChange: onInputChange,
        switchLabel: isEnable ? 'Activo' : 'Inactivo',
        disabled: !editMode || !id,
        classNameContainer: 'mt-2',
        className: 'flex justify-start items-center',
        options: [
          { value: 'active', placeholder: 'Activo' },
          { value: 'inactive', placeholder: 'Inactivo' },
        ],
        initialCheck: isEnable || false,
        info: {
          tooltip: {
            content: 'Activa o desactiva el descuento.',
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsEnable(isChecked);
        },
      },
    },

  ];

  return {
    inputs,
    formState,
  };
};
