import {
  combineValidators, FormInput,
  InputComponent, isNumeric, isPositiveNumer, isRequired, maxLength, SelectInput, TextAreaInput,
} from '@pitsdepot/storybook';
import { useCallback, useContext, useState } from 'react';
import { useParams } from 'react-router-dom';

import CustomImageViewer from '#appComponent/common/CustomImageViewer.AppComponent';
import CustomSkuInput from '#appComponent/form/CustomSkuInput.Component';
import { CustomStockInput } from '#appComponent/form/CustomStockInput.Component';
import {
  INPUT_NAME, LABELS, PLACEHOLDERS, TOOLTIPS,
} from '#application/constants/texts/InventoryForm.Constants';
import { InventoryItem } from '#application/deprecated/DashboardPages.Type';
import ApplicationRegistry from '#composition/Application.Registry';

import { useForm } from './useForm.Hook';

export const typeInputOptions = [
  { value: 'commodity', placeholder: 'Mercancía' },
  { value: 'product_input', placeholder: 'Insumo' },
];

export const useInventoryFormInputs = (initialState: Partial<InventoryItem>, editMode?: boolean) => {
  const { id } = useParams();
  const {
    name,
    sku,
    type,
    stock,
    description,
    formState,
    measurementUnit,
    onInputChange,
    resetForm,
    hasStockValidation,
    inventoryMedia,
  } = useForm<Partial<InventoryItem>>(initialState);

  const { inventoryId: updateInventory } = useContext(ApplicationRegistry.InventoryContext);

  const [isSkuAutoGenerated, setIsSkuAutoGenerated] = useState<boolean>(false);
  const [isStockDisabled, setIsStockDisabled] = useState<boolean>(hasStockValidation || true);

  const [images, setImages] = useState<(File | { id: string; url: string; name: string })[]>([]);

  const onImageChange = useCallback((files: (File | { id: string; url: string; name: string })[]) => { // Add this callback
    setImages(files);
  }, []);

  const inputs: FormInput[] = [
    {
      label: LABELS.PRODUCT_NAME,
      component: InputComponent,
      props: {
        isRequired: true,
        name: INPUT_NAME.NAME,
        value: name,
        onChange: onInputChange,
        className: 'mt-2',
        disabled: !editMode,
        placeholder: PLACEHOLDERS.PRODUCT_NAME,
        info: {
          tooltip: {
            content: TOOLTIPS.PRODUCT_NAME,
          },
        },
      },
      validate: combineValidators(maxLength(80), isRequired),
    },
    {
      label: LABELS.SKU,
      component: CustomSkuInput,
      validate: isSkuAutoGenerated ? undefined : isRequired,
      props: {
        isRequired: true,
        name: INPUT_NAME.SKU,
        value: sku,
        onChange: onInputChange,
        disabled: !editMode,
        checkBoxLabel: LABELS.SKU_AUTOGENERATE,
        placeholder: isSkuAutoGenerated ? PLACEHOLDERS.SKU_AUTOGENERATED : PLACEHOLDERS.SKU,
        info: {
          tooltip: {
            content: TOOLTIPS.SKU,
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsSkuAutoGenerated(isChecked);
        },
      },
    },
    {
      label: LABELS.TYPE,
      component: SelectInput,
      props: {
        name: INPUT_NAME.TYPE,
        value: type || '',
        onChange: onInputChange,
        disabled: !editMode,
        className: 'mt-2',
        options: typeInputOptions,
        info: {
          tooltip: {
            content: TOOLTIPS.TYPE,
          },
        },
      },
      validate: isRequired,
    },
    {
      label: LABELS.DESCRIPTION,
      component: TextAreaInput,
      formInputClassName: 'row-start-2 row-end-4 col-start-2 h-content',
      validate: maxLength(500),
      props: {
        name: INPUT_NAME.DESCRIPTION,
        value: description,
        onChange: onInputChange,
        disabled: !editMode,
        className: 'mt-2 !h-[270px]',
        placeholder: PLACEHOLDERS.DESCRIPTION,
        info: {
          tooltip: {
            content: TOOLTIPS.DESCRIPTION,
          },
        },
      },
    },
    {
      label: LABELS.ADD_IMAGE,
      component: () => CustomImageViewer({ // Change component here
        catalogMedia: inventoryMedia, // Pass existing inventory media
        readId: sku || '', // Use sku or id for display mode if images are not present
        onChange: onImageChange, // Pass the image change handler
        editMode,
        entity: 'inventory', // Specify the entity type
        entityId: id, // Pass the inventory item ID
      }),
      formInputClassName: 'min-w-0',
      props: {
        name: INPUT_NAME.IMAGE,
        disabled: !editMode,
        value: '',
        onChange: () => { },
        className: 'mt-2',
        info: {
          tooltip: {
            content: TOOLTIPS.ADD_IMAGE,
          },
        },
      },
    },
    {
      label: LABELS.MEASUREMENT_UNIT,
      component: InputComponent,
      props: {
        isRequired: true,
        name: INPUT_NAME.MEASUREMENT_UNIT,
        disabled: !editMode,
        value: measurementUnit,
        onChange: onInputChange,
        className: 'mt-2',
        info: {
          tooltip: {
            content: TOOLTIPS.MEASUREMENT_UNIT,
          },
        },
      },
      validate: isRequired,
    },
    {
      label: LABELS.STOCK,
      component: CustomStockInput,
      props: {
        name: INPUT_NAME.STOCK,
        value: stock,
        disabled: !editMode,
        onChange: onInputChange,
        checkBoxLabel: LABELS.STOCK_DISABLED,
        initialCheck: hasStockValidation,
        nonStock: Boolean(updateInventory),
        info: {
          tooltip: {
            content: TOOLTIPS.STOCK,
          },
        },
        onCheckboxChange: (isChecked: boolean) => {
          setIsStockDisabled(isChecked);
        },
      },
      validate: isStockDisabled ? undefined : combineValidators(isRequired, isNumeric, isPositiveNumer),
    },
  ];

  return {
    inputs,
    images,
    formState,
    resetForm,
    isSkuAutoGenerated,
  };
};
