import { gql, useLazyQuery, useQuery } from '@apollo/client';
import {
  useContext, useEffect, useMemo, useState,
} from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { OrderConditions } from '#application/common/orders/Order.Type';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';
import {
  SaleOrderConditionsParams, SaleOrderService, SaleOrderServiceUpdateParams,
} from '#application/saleOrders/services/SaleOrder.Service';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { Order } from '#domain/aggregates/order/Order.Entity';
import { OrderItem } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { AuthContext } from '#infrastructure/AuthState.Context';
import {
  OrderConditionsPayload, PostSaleOrderSchema,
  SaleOrderSchema,
  SaleOrdersHttps,
} from '#infrastructure/api/http/SaleOrders.Https';
import { SaleOrderResponse, SaleOrdersListResponse } from '#infrastructure/implementation/application/saleOrder/CoreSaleOrder.Type';

const saleOrderQuery = gql`
query GetSaleOrder($id: uuid!) {
  sale_order(where: {id: {_eq: $id}}) {
    id
    notes
    readId
    total
    subtotal
    subtotalBeforeDiscount
    totalDiscount
    taxes
    readId
    deliveryDate
    shippingAddress
    shippingPrice
    status
    createdAt
    updatedAt
    assignedUser {
      id
      email
    }
    client {
      id
      name
      company {
        name
      }
    }
    sale_order_items {
      name
      unitPrice
      quantity
      subtotal
      total
      taxes
      unitPriceAfterDiscount
      unitPriceAfterDiscountAndTaxes
      catalogId
      discountType
      discountValue
      productId
      inventoryCatalogs {
        quantity
        inventory {
          id
          name
          sku
          inventoryMedia {
            url
          }
        }
      }
      catalog_media {
        id
        url
      }
    }
  }
}
`;

const SaleOrdersQuery = gql`
  query GetSaleOrders($limit: Int, $offset: Int, $orderBy: [sale_order_order_by!], $condition: sale_order_bool_exp ) {
    sale_order(limit: $limit, order_by:  $orderBy, offset: $offset, where: $condition)  {
      id
      status
      total
      readId
      createdAt
      updatedAt
      client {
        name
        id
      }
      assignedUser {
        email
        id
      }
    }
    sale_order_aggregate (where: $condition) {
      aggregate {
        count
      }
    }
  }
`;

function mappedOrder(order: SaleOrderResponse): Order {
  return {
    id: order.id,
    receiver: {
      id: order.client.id,
      name: order.client.name,
      type: 'client',
      companyId: order.client.company?.id || '',
    },
    orderInfo: {
      deliveryDate: order.deliveryDate || '',
      shippingAddress: order.shippingAddress || '',
      notes: order.notes || '',
      orderNumber: order.readId,
      assignedUser: order.assignedUser || null,
      shippingPrice: order.shippingPrice || 0,
      status: {
        id: order.status,
        name: order.status,
      },
      summary: {
        subtotalBeforeDiscount: order.subtotalBeforeDiscount || 0,
        shippingPrice: order.shippingPrice || 0,
        subtotal: order.subtotal || 0,
        total: order.total || 0,
        taxes: order.taxes || [],
        totalDiscount: order.totalDiscount || 0,
      },
    },
    orderItems: order.sale_order_items?.map((item): OrderItem => ({
      id: item.catalogId || '',
      productNumber: item.productId || '',
      name: item.name,
      summary: {
        subtotalBeforeDiscount: 0,
        shippingPrice: 0,
        totalDiscount: 0,
        subtotal: item.subtotal || 0,
        total: item.total || 0,
        taxes: item.taxes || [],
      },
      quantity: item.quantity,
      unitPrice: item.unitPrice || 0,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount || 0,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes || 0,
      image: item.catalog_media?.url || '',
      inventoryRelations: item.inventoryCatalogs?.map((inventoryCatalog) => ({
        id: inventoryCatalog.inventory.id,
        name: inventoryCatalog.inventory.name,
        sku: inventoryCatalog.inventory.sku,
        image: inventoryCatalog.inventory.inventoryMedia[0]?.url,
        quantity: inventoryCatalog.quantity * item.quantity,
        total: item.quantity * inventoryCatalog.quantity,
      })) || [],
      discounts: {
        applied: null,
        applicable: [],
      },
    })) || [],
    createdAt: order.createdAt,
    updatedAt: order.updatedAt,
  };
}

function mapSaleOrderSchemaToOrder(saleOrder: SaleOrderSchema): Order {
  return {
    id: saleOrder.id,
    receiver: {
      id: saleOrder.clientId,
      name: '',
      type: 'client',
      companyId: saleOrder.clientCompanyId || null,
    },
    orderInfo: {
      deliveryDate: saleOrder.deliveryDate || '',
      shippingAddress: saleOrder.shippingAddress || '',
      notes: saleOrder.notes || '',
      orderNumber: saleOrder.readId,
      assignedUser: {
        id: saleOrder.assignedUserId || '',
        email: '',
      },
      shippingPrice: saleOrder.shippingPrice || 0,
      status: {
        id: saleOrder.status,
        name: saleOrder.status,
      },
      summary: {
        subtotalBeforeDiscount: saleOrder.subtotalBeforeDiscount || 0,
        shippingPrice: saleOrder.shippingPrice || 0,
        subtotal: saleOrder.subtotal || 0,
        total: saleOrder.total || 0,
        taxes: saleOrder.taxes || [],
        totalDiscount: saleOrder.totalDiscount || 0,
      },
    },
    orderItems: saleOrder.orderItems.map((item): OrderItem => ({
      id: item.catalogId || '',
      productNumber: item.productId || '',
      name: item.name,
      summary: {
        subtotalBeforeDiscount: 0,
        shippingPrice: 0,
        totalDiscount: 0,
        subtotal: item.subtotal || 0,
        total: item.total || 0,
        taxes: item.taxes || [],
      },
      quantity: item.quantity,
      unitPrice: item.unitPrice || 0,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount || 0,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes || 0,
      image: '',
      inventoryRelations: [],
      discounts: {
        applied: null,
        applicable: [],
      },
    })),
    createdAt: saleOrder.createdAt,
    updatedAt: saleOrder.updatedAt,
  };
}

const useGetSaleOrders = ({
  limit = 10, offset = 0, orderBy = 'createdAt', order = 'desc', searchTerm, filters,
}: GetTableDataProps): GetTableDataResponse<Order> => {
  const validOrderByFields = ['createdAt', 'updatedAt', 'saleOrderReadId'];
  if (!validOrderByFields.includes(orderBy)) {
    throw new Error(`Invalid orderBy field: ${orderBy}`);
  }
  const validOrderDirections = ['asc', 'desc'];
  if (!validOrderDirections.includes(order)) {
    throw new Error(`Invalid order direction: ${order}`);
  }

  const searchCondition = searchTerm ? { readId: { _ilike: `%${searchTerm}%` } } : {};
  const filterCondition = filters ? Object.entries(filters).reduce((acc, [key, value]) => {
    if (Array.isArray(value) && value.length) {
      acc[key] = { _in: value };
    }
    return acc;
  }, {} as Record<string, unknown>) : {};

  const {
    data, loading, error, refetch,
  } = useQuery<SaleOrdersListResponse>(SaleOrdersQuery, {
    fetchPolicy: 'cache-and-network',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      condition: { ...searchCondition, ...filterCondition },
    },
  });

  if (error) {
    Notification({
      type: MSG_ERROR_TYPES.ERROR,
      message: 'Error fetching sale orders',
    });
  }

  return {
    items: data?.sale_order?.map((item) => mappedOrder(item as SaleOrderResponse)) || [],
    totalNumberOfItems: data?.sale_order_aggregate?.aggregate?.count || 0,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetSaleOrder = (
  id: string,
): {
    order: Order | undefined;
    loading: boolean;
    error: unknown;
    refetch: () => void;
  } => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const [getSaleOrder, {
    data: { sale_order: readSaleOrder } = { sale_order: [] },
    loading: queryLoading,
    error: queryError,
    refetch,
  },
  ] = useLazyQuery<{sale_order: SaleOrderResponse[]}>(saleOrderQuery, {
    fetchPolicy: 'network-only',
    variables: {
      id,
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      if (id) {
        setLoading(true);
        setError(null);
        try {
          await getSaleOrder();
        } catch (fetchError) {
          setError(fetchError as Error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
        setError(null);
      }
    };

    fetchData();
  }, [id, getSaleOrder]);

  const transformedOrder = useMemo(() => {
    const fetchedSaleOrder = readSaleOrder[0];
    if (!fetchedSaleOrder) {
      return undefined;
    }

    return mappedOrder(fetchedSaleOrder);
  }, [readSaleOrder]);

  return {
    order: transformedOrder,
    loading: queryLoading || loading,
    error: queryError || error || null,
    refetch,
  };
};

const useGetAvailableStatuses = (saleOrder: Order): { availableStatuses:OrderStatusIds[], error: unknown} => {
  const { getToken } = useContext(AuthContext);
  const [availableStatuses, setAvailableStatuses] = useState<OrderStatusIds[]>([]);
  const [error, setError] = useState<unknown>(null);

  useEffect(
    () => {
      const fetchAvailableStatuses = async () => {
        if (saleOrder.orderInfo.status.id === OrderStatusIds.CANCELLED || saleOrder.orderInfo.status.id === OrderStatusIds.COMPLETED) {
          setAvailableStatuses([]);
          return;
        }

        try {
          const token = await getToken();
          const response = await SaleOrdersHttps.getAvailableStatuses(token, saleOrder.id);
          setAvailableStatuses(response);
        } catch (fetchError) {
          setError(fetchError);
        }
      };

      fetchAvailableStatuses();
    },
    [getToken, saleOrder],
  );

  return {
    availableStatuses,
    error,
  };
};

// TODO improve type of axios response
const useUpdateSaleOrder = (): {
  applyOrderUpdate: (order: SaleOrderServiceUpdateParams) => Promise<Order>;
} => {
  const { getToken } = useContext(AuthContext);

  const applyOrderUpdate = async (order: SaleOrderServiceUpdateParams): Promise<Order> => {
    try {
      const payload: SaleOrderServiceUpdateParams = {
        id: order.id,
        notes: order.notes,
        deliveryDate: order.deliveryDate,
        shippingAddress: order.shippingAddress,
        status: order.status,
        assignedUserId: order.assignedUserId,
      };

      const token = await getToken();
      const response = await SaleOrdersHttps.updateSaleOrder(token, payload);
      return mapSaleOrderSchemaToOrder(response);
    } catch (error) {
      throw new Error('Error updating sale order');
    }
  };

  return {
    applyOrderUpdate,
  };
};

const useGetConditions = (params: SaleOrderConditionsParams): {
  conditions: OrderConditions | null;
  error: unknown;
  loading: boolean;
} => {
  const [conditions, setConditions] = useState<OrderConditions | null>(null);
  const [error, setError] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const { getToken } = useContext(AuthContext);

  useEffect(() => {
    const fetchConditions = async () => {
      setLoading(true);

      if (params.orderItems?.length === 0) {
        setLoading(false);
        setConditions(null);
        setError(null);
        return;
      }

      const allItemsHaveCatalogId = params.orderItems?.every((item) => item.catalogId);
      const allItemHaveQuantity = params.orderItems?.every((item) => item.quantity);

      if (!params.clientId || !params.orderItems || !allItemsHaveCatalogId || !allItemHaveQuantity) {
        setLoading(false);
        setConditions(null);
        setError(null);

        return;
      }

      const token = await getToken();
      try {
        const response = await SaleOrdersHttps.postSaleOrderConditions(token, params as OrderConditionsPayload);
        setConditions({
          ...response,
          orderItems: response.orderItems.map((item) => ({
            ...item,
            id: item.catalogId || '',
          })),
        });
      } catch (fetchError) {
        setError(fetchError);
      }

      setLoading(false);
      setError(null);
    };

    fetchConditions();
  }, [JSON.stringify(params), getToken]);

  return { conditions, error, loading };
};

function useSaveSaleOrder(): {
  applyOrderSave: (order: Order) => Promise<Order>;
  } {
  const { getToken } = useContext(AuthContext);

  const applyOrderSave = async (order: Order): Promise<Order> => {
    const token = await getToken();
    const saleOrder: PostSaleOrderSchema = {
      clientId: order.receiver.id,
      orderItems: order.orderItems.map((item) => ({
        catalogId: item.id,
        quantity: item.quantity,
      })),
      deliveryDate: order.orderInfo.deliveryDate || undefined,
      notes: order.orderInfo.notes || undefined,
      readId: order.orderInfo.orderNumber || undefined,
      shippingAddress: order.orderInfo.shippingAddress || undefined,
      shippingPrice: order.orderInfo.shippingPrice,
    };
    const response = await SaleOrdersHttps.createSaleOrder(token, saleOrder);
    return mapSaleOrderSchemaToOrder(response);
  };

  return { applyOrderSave };
}

const ReadModelSaleOrderService: SaleOrderService = {
  useGetSaleOrders,
  useGetSaleOrder,
  useGetAvailableStatuses,
  useUpdateSaleOrder,
  useGetConditions,
  useSaveSaleOrder,
};

export default ReadModelSaleOrderService;
