import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderTax } from '#domain/aggregates/order/OrderTax.ValueObject';

interface CatalogMedia {
  id: string;
  url: string;
}

interface InventoryCatalogItem {
  quantity: number;
  inventory: {
    id: string;
    name: string;
    sku: string;
    inventoryMedia: {
      url: string;
    }[];
  };
}

export interface SaleOrderItemResponse {
  name: string;
  quantity: number;
  catalogId: string;
  productId: string;
  taxes: OrderTax[];
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  discountType: string | null;
  discountValue: number;
  inventoryCatalogs: InventoryCatalogItem[];
  catalog_media: CatalogMedia | null;
  subtotal: number;
  total: number;
  unitPrice: number;
}

export interface SaleOrderResponse {
  id: string;
  notes: string;
  total: number;
  subtotal: number;
  subtotalBeforeDiscount: number;
  totalDiscount: number;
  taxes: OrderTax[];
  readId: string;
  deliveryDate: string | null;
  shippingAddress: string | null;
  shippingPrice: number;
  status: OrderStatusIds;
  createdAt: string;
  updatedAt: string;
  assignedUser: {
    email: string;
    id: string;
  } | null;
  client: {
    id: string;
    name: string;
    company?: {
      name: string;
      id: string;
    };
  };
  sale_order_items: SaleOrderItemResponse[];
}

export interface SaleOrdersListResponse {
  sale_order: Array<{
    id: string;
    status: OrderStatusIds;
    total: number;
    readId: string;
    createdAt: string;
    updatedAt: string;
    client: {
      name: string;
      id: string;
    };
    assignedUser: {
      email: string;
      id: string;
    } | null;
  }>;
  sale_order_aggregate: {
    aggregate: {
      count: number;
    };
  };
}
