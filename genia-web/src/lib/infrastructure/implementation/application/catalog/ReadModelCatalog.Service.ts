import { gql, useLazyQuery } from '@apollo/client';
import {
  useCallback, useContext, useEffect, useState,
} from 'react';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { CatalogItem } from '#application/catalog/Catalog.Type';
import { CatalogService } from '#application/catalog/services/Catalog.Service';
import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';
import { CatalogInventory } from '#domain/aggregates/catalog/CatalogInventory.ValueObject';
import { ProvidersHttps } from '#infrastructure/api/http/Providers.Https';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { useGetCompanyId } from '#infrastructure/implementation/application/hooks/UseGetCompanyId.Hook';

export interface CatalogInventoryResponse {
  inventory_catalog: {
    catalogId: string;
    inventory: {
      id: string;
      sku: string;
      name: string;
      inventoryMedia: {
        url: string;
        id: string;
      }[];
    };
    quantity: number;
  }[];
}

const GET_CATALOG = gql`
  query GetCatalog($companyId: uuid!, $limit: Int!, $offset: Int!, $orderBy: [catalog_order_by!]) {
    catalog(where: {companyId: {_eq: $companyId}}, limit: $limit, offset: $offset, order_by: $orderBy) {
      description
      disabledAt
      id
      name
      type
      companyId
      createdAt
      attributes
      price
      readId
      requiresStock
      catalog_media {
        mediaType
        url
        id
      }
      inventoryCatalogs {
        inventoryId
        inventory {
          id
          name
          sku
          inventoryMedia {
            url
          }
        }
      }
    }
    catalog_aggregate(where: {companyId: {_eq: $companyId}}) {
      aggregate {
        count
      }
    }
  }
`;

const GET_CATALOG_BY_SEARCH = gql`
  query GetCatalogBySearch($term: String!) {
    catalog_search(args: {search_term: $term}) {
      description
      disabledAt
      id
      name
      type
      companyId
      createdAt
      attributes
      price
      readId
      catalog_media {
        url
      }
      requiresStock
    }
    catalog_search_aggregate(args: {search_term: $term}) {
      aggregate {
        count
      }
    }
  }
`;

const GET_CATALOG_ITEM = gql`
    query GetCatalogItem($id: uuid) {
      catalog(where: {id: {_eq: $id}}) {
        id
        name
        price
        readId
        type
        description
        attributes
        disabledAt
         catalog_media {
          url
          id
          processing
        }
        requiresStock
        catalogTax {
          taxId
        }
        inventoryCatalogs {
          quantity
          inventoryId
          catalogId
          id
        }
      }
    }
  `;

const getCatalogInventoryQueryByIds = gql`
    query GetCatalogItem($_in: [uuid!]) {
      inventory_catalog(where: {catalogId: {_in: $_in}}) {
        catalogId
        inventory {
          id
          sku
          name
          inventoryMedia {
            url
            id
          }
        }
        quantity
      }
    }
  `;

const useGetCatalogs = ({
  limit = 10, offset = 0, orderBy = 'updatedAt', order = 'desc', searchTerm,
}: GetTableDataProps): GetTableDataResponse<CatalogItem> => {
  const { systemUser: { companyId } } = useContext(AuthContext);
  const [getCatalogs, {
    data, loading, error, refetch,
  }] = useLazyQuery(GET_CATALOG, {
    fetchPolicy: 'network-only',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      companyId,
    },
  });

  const [getCatalogBySearch, { data: dataGetCatalogBySearch, loading: loadingGetCatalogBySearch }] = useLazyQuery(GET_CATALOG_BY_SEARCH, {
    variables: {
      term: searchTerm,
    },
  });

  useEffect(() => {
    if (companyId && !searchTerm) {
      getCatalogs();
    }
  }, [companyId, searchTerm]);

  useEffect(() => {
    if (searchTerm) {
      getCatalogBySearch();
    }
  }, [searchTerm]);

  return {
    items: searchTerm ? dataGetCatalogBySearch?.catalog_search : data?.catalog,
    totalNumberOfItems: searchTerm ? dataGetCatalogBySearch?.catalog_search_aggregate?.aggregate?.count : data?.catalog_aggregate?.aggregate?.count,
    loading: loadingGetCatalogBySearch || loading,
    error: error as Error,
    refetch,
  };
};

const useGetCatalogItem = (id?: string): GetTableDataResponse<CatalogItem> => {
  const [
    getCataloItems, {
      data, loading, error, refetch,
    },
  ] = useLazyQuery(GET_CATALOG_ITEM, {
    fetchPolicy: 'network-only',
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id) {
      getCataloItems();
    }
  }, [id]);

  return {
    items: data?.catalog,
    totalNumberOfItems: 0,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetCatalogByReadId = ({
  limit = 10, offset = 1, searchTerm = '', providerId, enabled = true,
}: Omit<GetTableDataProps, 'orderBy' | 'order'> & { searchTerm?: string, enabled?: boolean }): GetTableDataResponse<ProviderCatalogItem> => {
  const { getToken } = useContext(AuthContext);
  const { companyId } = useGetCompanyId();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [items, setItems] = useState<ProviderCatalogItem[]>([]);
  const [totalNumberOfItems, setTotalNumberOfItems] = useState(0);

  const fetchData = useCallback(async () => {
    if (!companyId || !providerId || !enabled) return;

    setLoading(true);
    try {
      const token = await getToken();

      const response = await ProvidersHttps.getCatalogByReadId(
        token,
        providerId,
        {
          limit,
          offset,
          searchTerm,
        },
      );

      setItems(response.data.data || []);
      setTotalNumberOfItems(response.data.count || 0);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [companyId, getToken, limit, offset, searchTerm, providerId, enabled]);

  useEffect(() => {
    if (providerId && enabled) {
      fetchData();
    }
  }, [providerId, fetchData, enabled]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    items,
    totalNumberOfItems,
    loading,
    error: error as Error,
    refetch,
  };
};

function useGetCatalogInventoryByIds(ids: string[]): {items: CatalogInventory[], loading: boolean, error: Error | null, refetch: () => void} {
  const [getCatalogs, {
    data, loading, error, refetch,
  }] = useLazyQuery<CatalogInventoryResponse>(getCatalogInventoryQueryByIds, {
    fetchPolicy: 'network-only',
    variables: {
      _in: ids,
    },
  });

  useEffect(() => {
    if (ids) {
      getCatalogs();
    }
  }, [ids.join(',')]);

  return {
    items: data?.inventory_catalog.map((item) => ({
      catalogId: item.catalogId,
      inventoryId: item.inventory.id,
      quantity: item.quantity,
      sku: item.inventory.sku,
      name: item.inventory.name,
      image: item.inventory.inventoryMedia[0]?.url || '',
    })) || [],
    loading,
    error: error as Error,
    refetch,
  };
}

const ReadModelCatalogService: CatalogService = {
  useGetCatalogs,
  useGetCatalogItem,
  useGetCatalogByReadId,
  useGetCatalogInventoryByIds,
};

export default ReadModelCatalogService;
