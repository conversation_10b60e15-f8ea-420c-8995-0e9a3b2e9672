import { gql, useLazyQuery } from '@apollo/client';
import { useEffect } from 'react';

import { CatalogItem } from '#application/catalog/Catalog.Type';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';
import { useGetCompanyId } from '#infrastructure/implementation/application/hooks/UseGetCompanyId.Hook';

const GET_CATALOG = gql`
  query GetCatalog($companyId: uuid!, $limit: Int!, $offset: Int!, $orderBy: [catalog_order_by!]) {
    catalog(where: {companyId: {_eq: $companyId}}, limit: $limit, offset: $offset, order_by: $orderBy) {
      description
      disabledAt
      id
      name
      type
      companyId
      createdAt
      attributes
      price
      readId
      requiresStock
      catalog_media {
        mediaType
        url
        id
      }
    }
    catalog_aggregate(where: {companyId: {_eq: $companyId}}) {
      aggregate {
        count
      }
    }
  }
`;

const GET_CATALOG_BY_SEARCH = gql`
  query GetCatalogBySearch($term: String!) {
    catalog_search(args: {search_term: $term}) {
      description
      disabledAt
      id
      name
      type
      companyId
      createdAt
      attributes
      price
      readId
      requiresStock
    }
    catalog_search_aggregate(args: {search_term: $term}) {
      aggregate {
        count
      }
    }
  }
`;

export const useGetCatalog = ({
  limit = 10, offset = 0, orderBy = 'updatedAt', order = 'desc', searchTerm,
}: GetTableDataProps): GetTableDataResponse<CatalogItem> => {
  const { companyId } = useGetCompanyId();

  const [getCatalogs, {
    data, loading, error, refetch,
  }] = useLazyQuery(GET_CATALOG, {
    fetchPolicy: 'cache-and-network',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      companyId,
    },
  });

  const [getCatalogBySearch, { data: dataGetCatalogBySearch, loading: loadingGetCatalogBySearch }] = useLazyQuery(GET_CATALOG_BY_SEARCH, {
    variables: {
      term: searchTerm,
    },
  });

  useEffect(() => {
    if (companyId && !searchTerm) {
      getCatalogs();
    }
  }, [companyId, searchTerm]);

  useEffect(() => {
    if (searchTerm) {
      getCatalogBySearch();
    }
  }, [searchTerm]);

  return {
    items: searchTerm ? dataGetCatalogBySearch?.catalog_search : data?.catalog,
    totalNumberOfItems: searchTerm ? dataGetCatalogBySearch?.catalog_search_aggregate?.aggregate?.count : data?.catalog_aggregate?.aggregate?.count,
    loading: loadingGetCatalogBySearch || loading,
    error: error as Error,
    refetch,
  };
};
