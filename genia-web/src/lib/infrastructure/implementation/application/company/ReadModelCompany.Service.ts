import { gql, useQuery } from '@apollo/client';

import { GetTableDataResponse } from '#appComponent/table/Table.Type';
import { Company } from '#application/company/Company.Type';
import { CompanyService } from '#application/company/services/Company.Service';

import { useGetCompanyId } from '../hooks/UseGetCompanyId.Hook';

const GetCompanyQuery = gql`
  query GetCompany($id: uuid!) {
    company(where: {id: {_eq: $id}}) {
      id
      name
      description
      country
      tributaryId
    }
  }
`;

const useGetCompanies = (): Omit<GetTableDataResponse<Company>, 'totalNumberOfItems'> => ({
  items: [],
  loading: false,
  error: null as unknown as Error,
  refetch: () => {},
});

const useGetCurrentCompany = () => {
  const { companyId, loading: companyIdLoading } = useGetCompanyId();

  const {
    data, loading: queryLoading, error, refetch,
  } = useQuery(GetCompanyQuery, {
    variables: { id: companyId },
    skip: !companyId,
  });

  const company = data?.company?.[0] || null;
  const loading = companyIdLoading || queryLoading;

  return {
    company,
    loading,
    error: error as Error | null,
    refetch,
  };
};

const ReadModelCompanyService: CompanyService = {
  useGetCompanies,
  useGetCurrentCompany,
};

export default ReadModelCompanyService;
