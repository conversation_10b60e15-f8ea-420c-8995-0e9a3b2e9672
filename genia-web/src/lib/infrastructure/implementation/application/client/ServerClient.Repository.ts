import { Client } from '#application/client/Client.Type';
import { ClientRepositoryCreateParams } from '#/lib/biizi/application/Client/repositories/Client.Repository';
import { ClientsHttps } from '#infrastructure/api/http/Clients.Https';

async function createClient(params: ClientRepositoryCreateParams): Promise<Client> {
  const { token, ...clientData } = params;
  const response = await ClientsHttps.postClients(token, [clientData]);

  return response;
}

const ServerClientRepository = {
  createClient,
};

export default ServerClientRepository;
