import { ClientRepositoryCreateParams } from '#/lib/biizi/application/Client/repositories/Client.Repository';
import ClientEntity from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { ClientsHttps, ClientsPayload } from '#infrastructure/api/http/Clients.Https';

// Response interface for the HTTP API
interface ClientResponse {
  id: string;
  name: string;
  tributaryId: string;
  clientCompanyId: string | null;
  storeDiscounts: string[];
  contactInformation: {
    billingEmail: string;
    billingPhoneNumber: string;
    billingWhatsapp: string;
    purchasesEmail: string;
    purchasesPhoneNumber: string;
    purchasesWhatsapp: string;
    salesEmail: string;
    salesPhoneNumber: string;
    salesWhatsapp: string;
    shippingAddress: string;
    billingAddress: string;
  };
  createdAt: string;
  updatedAt: string;
}

async function createClient(params: ClientRepositoryCreateParams): Promise<ClientEntity> {
  const { token, ...clientData } = params;

  // Transform Client data to ClientsPayload format
  // Only send required fields and fields with valid values
  const payload: Partial<ClientsPayload> & { name: string } = {
    name: clientData.name, // Required field
  };

  // Only include tributaryId if client type is COMPANY and has a value
  if (clientData.clientType === 'company' && clientData.tributaryId) {
    payload.tributaryId = clientData.tributaryId;
  }

  // Add other optional fields only if they have values
  if (clientData.clientType === 'company') {
    payload.clientCompanyId = null; // For companies, explicitly set to null
  }

  // Only add storeDiscounts if needed (empty array as default)
  payload.storeDiscounts = [];

  // Only add contactInformation if we have contact data
  payload.contactInformation = {
    billingEmail: '',
    billingPhoneNumber: '',
    billingWhatsapp: '',
    purchasesEmail: '',
    purchasesPhoneNumber: '',
    purchasesWhatsapp: '',
    salesEmail: '',
    salesPhoneNumber: '',
    salesWhatsapp: '',
    shippingAddress: '',
    billingAddress: '',
  };

  const response = await ClientsHttps.postClients(token, [payload]);

  // The API returns an array, so we take the first element
  const clientResponse: ClientResponse = response.data[0];

  // Transform the HTTP response into a ClientEntity
  return new ClientEntity(
    clientResponse.id,
    clientResponse.name,
    clientResponse.tributaryId,
    clientResponse.clientCompanyId,
    clientResponse.storeDiscounts,
    clientResponse.contactInformation,
    new Date(clientResponse.createdAt),
    new Date(clientResponse.updatedAt),
  );
}

const ServerClientRepository = {
  createClient,
};

export default ServerClientRepository;
