import { ClientRepositoryCreateParams } from '#/lib/biizi/application/Client/repositories/Client.Repository';
import ClientEntity from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { ClientsHttps } from '#infrastructure/api/http/Clients.Https';

// Response interface for the HTTP API
interface ClientResponse {
  id: string;
  name: string;
  tributaryId: string;
  clientCompanyId: string | null;
  storeDiscounts: string[];
  contactInformation: {
    billingEmail: string;
    billingPhoneNumber: string;
    billingWhatsapp: string;
    purchasesEmail: string;
    purchasesPhoneNumber: string;
    purchasesWhatsapp: string;
    salesEmail: string;
    salesPhoneNumber: string;
    salesWhatsapp: string;
    shippingAddress: string;
    billingAddress: string;
  };
  createdAt: string;
  updatedAt: string;
}

async function createClient(params: ClientRepositoryCreateParams): Promise<ClientEntity> {
  const { token, ...clientData } = params;
  const response = await ClientsHttps.postClients(token, [clientData]);

  // The API returns an array, so we take the first element
  const clientResponse: ClientResponse = response.data[0];

  // Transform the HTTP response into a ClientEntity
  return new ClientEntity(
    clientResponse.id,
    clientResponse.name,
    clientResponse.tributaryId,
    clientResponse.clientCompanyId,
    clientResponse.storeDiscounts,
    clientResponse.contactInformation,
    new Date(clientResponse.createdAt),
    new Date(clientResponse.updatedAt),
  );
}

const ServerClientRepository = {
  createClient,
};

export default ServerClientRepository;
