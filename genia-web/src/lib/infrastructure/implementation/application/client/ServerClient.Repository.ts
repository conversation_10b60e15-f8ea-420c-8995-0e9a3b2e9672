import { ClientRepositoryCreateParams } from '#/lib/biizi/application/Client/repositories/Client.Repository';
import ClientEntity from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { ClientsHttps, ClientsPayload } from '#infrastructure/api/http/Clients.Https';
import { ClientType } from '#application/client/Client.Type';

// Response interface for the HTTP API
interface ClientResponse {
  id: string;
  name: string;
  tributaryId: string;
  clientCompanyId: string | null;
  storeDiscounts: string[];
  contactInformation: {
    billingEmail: string;
    billingPhoneNumber: string;
    billingWhatsapp: string;
    purchasesEmail: string;
    purchasesPhoneNumber: string;
    purchasesWhatsapp: string;
    salesEmail: string;
    salesPhoneNumber: string;
    salesWhatsapp: string;
    shippingAddress: string;
    billingAddress: string;
  };
  createdAt: string;
  updatedAt: string;
}

async function createClient(params: ClientRepositoryCreateParams): Promise<ClientEntity> {
  const { token, ...clientData } = params;

  // Transform Client data to ClientsPayload format
  const payload: ClientsPayload = {
    name: clientData.name,
    // Only include tributaryId if client type is COMPANY
    tributaryId: clientData.clientType === 'company' ? clientData.tributaryId : null,
    clientCompanyId: null, // Default value as seen in other implementations
    storeDiscounts: [], // Default empty array
    contactInformation: {
      billingEmail: '',
      billingPhoneNumber: '',
      billingWhatsapp: '',
      purchasesEmail: '',
      purchasesPhoneNumber: '',
      purchasesWhatsapp: '',
      salesEmail: '',
      salesPhoneNumber: '',
      salesWhatsapp: '',
      shippingAddress: '',
      billingAddress: '',
    },
  };

  const response = await ClientsHttps.postClients(token, [payload]);

  // The API returns an array, so we take the first element
  const clientResponse: ClientResponse = response.data[0];

  // Transform the HTTP response into a ClientEntity
  return new ClientEntity(
    clientResponse.id,
    clientResponse.name,
    clientResponse.tributaryId,
    clientResponse.clientCompanyId,
    clientResponse.storeDiscounts,
    clientResponse.contactInformation,
    new Date(clientResponse.createdAt),
    new Date(clientResponse.updatedAt),
  );
}

const ServerClientRepository = {
  createClient,
};

export default ServerClientRepository;
