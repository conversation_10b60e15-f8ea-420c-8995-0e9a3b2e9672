import { gql, useQuery } from '@apollo/client';

import { Client } from '#application/client/Client.Type';
import { ClientService } from '#application/client/services/Client.Service';
import { GetTableDataProps, GetTableDataResponse } from '#application/deprecated/DashboardPages.Type';

const query = gql`
  query GetClients($limit: Int!, $offset: Int!, $orderBy: [client_order_by!], $where: client_bool_exp) {
      client(limit: $limit, offset: $offset, order_by: $orderBy, where: $where) {
          id
          name
          tributaryId
          client_company {
            name
          }
      }
      client_aggregate (where: $where) {
        aggregate {
          count
        }
      }
    }
`;

const useGetClients = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'asc', searchTerm,
}: GetTableDataProps): GetTableDataResponse<Client> => {
  const searchCondition = searchTerm ? { name: { _ilike: `%${searchTerm}%` } } : {};
  const {
    data, loading, error, refetch,
  } = useQuery(query, {
    fetchPolicy: 'network-only',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      where: searchCondition,
    },
  });

  return {
    items: data?.client,
    totalNumberOfItems: data?.client_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelClientService: ClientService = {
  useGetClients,
};

export default ReadModelClientService;
