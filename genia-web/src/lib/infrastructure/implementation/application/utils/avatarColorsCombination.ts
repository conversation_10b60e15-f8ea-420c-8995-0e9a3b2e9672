const colorCategories: { [key: string]: { backgroundColor: string; textColor: string } } = {
  'A-F': { backgroundColor: 'red', textColor: 'text-white' },
  'G-L': { backgroundColor: 'green', textColor: 'text-white' },
  'M-R': { backgroundColor: 'blue', textColor: 'text-white' },
  'S-X': { backgroundColor: 'red', textColor: 'text-white' },
  'Y-Z': { backgroundColor: 'green', textColor: 'text-white' },
  default: { backgroundColor: 'white', textColor: 'text-black' },
};

export function getCategory(firstLetter: string): string {
  const charCode = firstLetter.charCodeAt(0);
  if (charCode >= 65 && charCode <= 70) return 'A-F';
  if (charCode >= 71 && charCode <= 76) return 'G-L';
  if (charCode >= 77 && charCode <= 82) return 'M-R';
  if (charCode >= 83 && charCode <= 88) return 'S-X';
  if (charCode >= 89 && charCode <= 90) return 'Y-Z';
  return 'default';
}

export function getAvatarStyles(name: string): { backgroundColor: string; textColor: string } {
  const firstLetter = name.charAt(0).toUpperCase();
  const category = getCategory(firstLetter);
  return colorCategories[category];
}
