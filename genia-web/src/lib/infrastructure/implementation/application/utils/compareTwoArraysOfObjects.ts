type Actions = 'length' | 'quantity' | 'unitPrice' | 'subtotal';
interface Test {
  referenceId: string;
  quantity: number;
  unitPrice?: number | null;
  subtotal?: number;
}

export const compareTwoArraysOfObjects = (element1: Test[], element2: Test[], action: Actions) => {
  const internal1 = element1?.map((el) => ({
    referenceId: el.referenceId, quantity: el.quantity, unitPrice: el.unitPrice, subtotal: el.subtotal,
  })) || [];
  const internal2 = element2?.map((el) => ({
    referenceId: el.referenceId, quantity: el.quantity, unitPrice: el.unitPrice, subtotal: el.subtotal,
  })) || [];

  if (action === 'length') {
    return internal1.length !== internal2.length;
  }

  return internal1.some((item1) => {
    const item2 = internal2.find((it) => it.referenceId === item1.referenceId);

    if (action === 'unitPrice' && item1.unitPrice !== item2?.unitPrice) {
      return true;
    }

    if (action === 'quantity' && item1.quantity !== item2?.quantity) {
      return true;
    }

    if (action === 'subtotal' && item1.subtotal !== item2?.subtotal) {
      return true;
    }

    return false;
  });
};
