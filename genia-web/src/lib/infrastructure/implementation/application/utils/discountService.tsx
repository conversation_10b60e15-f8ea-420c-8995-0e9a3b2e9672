import { AvatarGroup, AvatarProps } from '@pitsdepot/storybook';
import { AxiosResponse } from 'axios';

import TextService from '#composition/textService/Text.Service';

import { Notification } from '../../../../appComponent/common/Notification.Component';
import { STORE_DISCOUNT_ADD_FAILED, STORE_DISCOUNT_UPDATE_FAILED } from '../../../../application/constants/texts/StoreDiscount.Constants';
import { MSG_ERROR_TYPES } from '../../../../application/types/Notification.Type';

import { NotificableError } from './Error.Util';
import { transformDateToFormat } from './TransformDateToFormat';

interface AvatarClient {
  client: AvatarProps;
}

interface CatalogDiscountProps {
  id: string;
  name: string;
  description?: string;
  discountType?: string;
  discountValue?: string;
  requiredQuantity?: number;
  catalog_discount_clients?: AvatarClient[];
  startDate: string;
  endDate: string;
}

const text = TextService.getText();

interface CustomAxiosResponse extends AxiosResponse {
  statusCode?: number;
}

export const createDiscountRows = (catalogDiscount:CatalogDiscountProps[]) => {
  if (!catalogDiscount) return [];

  return catalogDiscount.map((item: CatalogDiscountProps) => {
    const size = 35;

    return {
      id: item.id || '',
      product: { name: item.name || '', description: '' },
      type: item.discountType || '',
      value: item.discountValue || '',
      requireAmount: item.requiredQuantity || 0,
      clients: (
        <div className='min-w-[35px]'>
          <AvatarGroup
            itemsSelected={item.catalog_discount_clients?.map(
              (catalogDiscountClient) => ({
                ...catalogDiscountClient.client,
                id: catalogDiscountClient.client.id || '',
              }),
            ) || []}
            readOnly
            avatarProps={{ size }}
          />
        </div>
      ),
      startDate: transformDateToFormat(item.startDate) || '',
      endDate: transformDateToFormat(item.endDate) || '',
    };
  });
};

export const handleDiscountResponse = (response: CustomAxiosResponse): CustomAxiosResponse => {
  if (response.statusCode === 409) {
    throw new NotificableError(text.discounts.existsError, MSG_ERROR_TYPES.ERROR);
  }

  if (response.statusCode === 400) {
    throw new NotificableError(text.discounts.relationError, MSG_ERROR_TYPES.ERROR);
  }

  if (!response?.status || response?.status === 500) {
    throw new NotificableError(text.discounts.addFailed, MSG_ERROR_TYPES.ERROR);
  }

  return response;
};

export const handleDiscountError = (id?: string) => {
  Notification({ message: !id ? STORE_DISCOUNT_ADD_FAILED : STORE_DISCOUNT_UPDATE_FAILED, type: MSG_ERROR_TYPES.ERROR });
};
