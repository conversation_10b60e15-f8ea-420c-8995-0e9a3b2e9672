import {
  INVALID_DATES_ERROR,
  NO_GREATER_THAN_100,
  NON_NEGATIVE_VALUES_ERROR,
  NUMERIC_VALUES_ERROR,
  REQUIRED_FIELDS_ERROR,
  START_DATE_AFTER_END_DATE_ERROR,
  START_DATE_BEFORE_TODAY_ERROR,
} from '#application/constants/texts/CatalogDiscount.Constants';
import { CatalogDiscountItem, StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';

export const checkValidationsDate = (start: Date, end: Date | null) => {
  const today = new Date();

  today.setHours(0, 0, 0, 0);

  if (Number.isNaN(start.getTime())) {
    return INVALID_DATES_ERROR;
  }

  if (end && Number.isNaN(end.getTime())) {
    return INVALID_DATES_ERROR;
  }

  const startMidnight = new Date(start);
  startMidnight.setHours(0, 0, 0, 0);

  let endMidnight: Date | null = null;
  if (end) {
    endMidnight = new Date(end);
    endMidnight.setHours(0, 0, 0, 0);
  }

  if (endMidnight && endMidnight.getTime() < today.getTime() && startMidnight.getTime() < today.getTime()) {
    return START_DATE_BEFORE_TODAY_ERROR;
  }

  if (endMidnight && startMidnight.getTime() > endMidnight.getTime()) {
    return START_DATE_AFTER_END_DATE_ERROR;
  }

  return null;
};

export const validateDiscountForm = (discount: Partial<CatalogDiscountItem | StoreDiscountItem>): string | null => {
  const {
    name, discountValue, startDate, endDate, discountType,
  } = discount;

  const isCatalogDiscount = 'requiredQuantity' in discount;
  const isStoreDiscount = 'requiredAmount' in discount;

  if (!name || !discountType || !startDate) {
    return REQUIRED_FIELDS_ERROR;
  }

  if (
    Number.isNaN(Number(discountValue))
    || (isCatalogDiscount && Number.isNaN(Number(discount.requiredQuantity)))
    || (isStoreDiscount && Number.isNaN(Number(discount.requiredAmount)))
  ) {
    return NUMERIC_VALUES_ERROR;
  }

  if (
    (discountValue && Number(discountValue) < 0)
    || (isCatalogDiscount && discount.requiredQuantity && Number(discount.requiredQuantity) < 0)
    || (isStoreDiscount && discount.requiredAmount && Number(discount.requiredAmount) < 0)
  ) {
    return NON_NEGATIVE_VALUES_ERROR;
  }

  if (discountType === 'percentage' && (discountValue || 0) > 100) {
    return NO_GREATER_THAN_100;
  }

  const start = startDate ? new Date(startDate) : new Date();
  const end = endDate ? new Date(endDate) : null;

  return checkValidationsDate(start, end);
};
