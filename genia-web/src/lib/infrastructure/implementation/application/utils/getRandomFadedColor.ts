import Theme from '@pitsdepot/storybook/theme';

export const getRandomFadedColor = (id: string): string => {
  const fadedColors = Object.entries(Theme.colors)
    .filter(([key, value]) => key.startsWith('faded') && typeof value === 'string')
    .map(([, value]) => value as string);

  const index = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % fadedColors.length;

  return fadedColors[index];
};
