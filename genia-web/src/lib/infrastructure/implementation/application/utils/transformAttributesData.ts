import { EditableHierarchy } from '@pitsdepot/storybook';

import {
  CATEGORIES_LABEL, HIERARCHY_TYPE_CATEGORY, HIERARCHY_TYPE_VALUE, VALUES_LABEL,
} from '#application/constants/texts/Attributes.Constants';
import { CategoryAttributes } from '#application/deprecated/DashboardPages.Type';

import { generateId } from './GenerateID.Util';

export const transformData = (data: CategoryAttributes[]): EditableHierarchy[] => data?.map((item: CategoryAttributes) => {
  const newItem: EditableHierarchy = {
    id: generateId(),
    name: item.key,
    properties: {
      type: (CATEGORIES_LABEL in item || VALUES_LABEL in item) ? HIERARCHY_TYPE_CATEGORY : HIERARCHY_TYPE_VALUE,
    },
  };

  newItem.hierarchies = item.categories
    ? transformData(item.categories)
    : (item.values
          && item.values.map((value) => ({
            id: generateId(),
            name: value,
            properties: {
              type: HIERARCHY_TYPE_VALUE,
            },
          })));

  return newItem;
});

export const reverseTransformData = (
  data: EditableHierarchy[],
): CategoryAttributes[] => data.map((item: EditableHierarchy) => {
  const newItem: CategoryAttributes = {
    key: item.name,
  };

  if (item.hierarchies && item.hierarchies.length > 0 && item.hierarchies[0]?.properties?.type === HIERARCHY_TYPE_CATEGORY) {
    newItem.categories = reverseTransformData(item.hierarchies);
  } else {
    newItem.values = item?.hierarchies?.map((h) => h.name);
  }

  return newItem;
});

export const findHierarchy = (hierarchiesToSearch: EditableHierarchy[], hierarchyToFind: EditableHierarchy): EditableHierarchy | undefined => {
  let foundHierarchy: EditableHierarchy | undefined;

  for (let i = 0; i < hierarchiesToSearch.length; i += 1) {
    const hierarchy = hierarchiesToSearch[i];

    if (hierarchy.id === hierarchyToFind.id) {
      return hierarchy;
    }

    if (hierarchy.hierarchies) {
      foundHierarchy = findHierarchy(hierarchy.hierarchies, hierarchyToFind);

      if (foundHierarchy) {
        return foundHierarchy;
      }
    }
  }

  return foundHierarchy;
};

export const hasNotCategoriesOrValues = (obj: CategoryAttributes | undefined, setIsloading: (isLoading: boolean) => void) => {
  const hascategories = obj?.categories && obj?.categories.length > 0;
  const hasValues = obj?.values && obj?.values.length > 0;

  if (!hascategories && !hasValues) {
    return true;
  }

  if (hascategories) {
    // eslint-disable-next-line no-use-before-define
    return validateArrayAttributes(obj?.categories as CategoryAttributes[], setIsloading);
  }

  return false;
};

export const validateArrayAttributes = (newAttributesForm: CategoryAttributes[], setIsloading: (isLoading: boolean) => void) => {
  const hasInvalidObject = newAttributesForm.some((obj) => hasNotCategoriesOrValues(obj, setIsloading));

  if (hasInvalidObject) {
    return true;
  }

  return false;
};
