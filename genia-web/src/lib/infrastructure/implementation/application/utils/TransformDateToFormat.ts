type DateFormat = 'dd-mm-yyyy' | 'mm-dd-yyyy' | 'yyyy-mm-dd';

export function transformDateToFormat(date: string, format: DateFormat = 'dd-mm-yyyy'): string {
  const dateObj = new Date(date);

  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const year = dateObj.getFullYear();

  if (format === 'mm-dd-yyyy') return `${month}-${day}-${year}`;
  if (format === 'yyyy-mm-dd') return `${year}-${month}-${day}`;

  return `${day}-${month}-${year}`;
}
