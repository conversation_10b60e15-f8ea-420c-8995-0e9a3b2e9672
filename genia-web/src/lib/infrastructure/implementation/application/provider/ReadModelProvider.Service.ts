import { gql, useLazyQuery, useQuery } from '@apollo/client';
import { useContext, useEffect, useMemo } from 'react';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { Provider } from '#application/provider/Provider.Type';
import { ProviderCatalogItem } from '#application/provider/ProviderCatalogItem.Type';
import { ProviderService } from '#application/provider/services/Provider.Service';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { ProvidersHttps } from '#infrastructure/api/http/Providers.Https';

const query = gql`
    query GetProviders($limit: Int!, $offset: Int!, $orderBy: [provider_order_by!], $where: provider_bool_exp) {
      provider(
        limit: $limit,
        offset: $offset,
        order_by: $orderBy,
        where: $where
      ) {
        id
        name
        tributaryId
        providerCompanyId
        providerInventories_aggregate {
          aggregate {
            count
          }
        }
      }
      provider_aggregate(where: $where) {
        aggregate {
          count
        }
      }
    }
  `;

export const useGetProviders = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'asc', searchTerm,
}: GetTableDataProps): GetTableDataResponse<Provider> => {
  const searchCondition = searchTerm ? { name: { _ilike: `%${searchTerm}%` } } : {};
  const {
    data, loading, error, refetch,
  } = useQuery(query, {
    fetchPolicy: 'network-only',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      where: searchCondition,
    },
  });

  return {
    items: data?.provider,
    totalNumberOfItems: data?.provider_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const GET_PROVIDERS_CATALOG_SEARCH = gql`
  query GetProvidersCatalogSearch(
    $limit: Int!
    $offset: Int!
    $searchTerm: String!
    $orderBy: [catalog_search_results_order_by!]
    $whereFilter: catalog_search_results_bool_exp
  ) {
    providers_catalog_search(
      args: {
        search_term: $searchTerm
        }, 
      limit: $limit, 
      offset: $offset,
      where: $whereFilter
      order_by: $orderBy
      ) {
      attributes
      description
      disabledAt
      id
      name
      price
      requiresStock
      readId
      type
      catalog_media {
        url
        id
      }
      company {
        name
        id
      }
    }
      providers_catalog_search_aggregate(
        args: {
          search_term: $searchTerm
        }
        where: $whereFilter
      ){
        aggregate {
          count
        }
      }
  }
`;

const GET_PROVIDERS_CATALOG = gql`
  query GetProvidersCatalog(
    $limit: Int!,
    $offset: Int!,
    $orderBy: [catalog_order_by!],
    $whereFilter: catalog_bool_exp
  ) {
    catalog(
      where: $whereFilter
      limit: $limit
      offset: $offset
      order_by: $orderBy
    ) {
    attributes
    description
    disabledAt
    id
    name
    price
    requiresStock
    readId
    type
    catalog_media {
      url
      id
    }
    company {
      name
      id
    }
    }
    catalog_aggregate(where: $whereFilter) {
      aggregate {
        count
      }
    }
  }
`;

const useGetProvidersCatalog = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'asc', searchTerm, filters,
}: GetTableDataProps): GetTableDataResponse<ProviderCatalogItem> => {
  const { systemUser: { companyId } } = useContext(AuthContext);

  const filterCondition = useMemo(() => (filters ? Object.entries(filters).reduce((acc, [key, value]) => {
    if (value.length) {
      acc[key] = key === 'company' ? { id: { _in: value } } : { _in: value };
    }
    return acc;
  }, {} as Record<string, unknown>) : {}), [filters]);

  const whereFilter = useMemo(() => ({
    disabledAt: { _is_null: true },
    _not: { companyId: { _eq: companyId } },
    ...filterCondition,
  }), [companyId, filterCondition]);

  const isSearching = Boolean(searchTerm);
  const orderBySearch = 'rank';
  const orderSearch = 'desc';

  const [getProvidersCatalogSearch, {
    data: searchData, loading: searchLoading, error: searchError, refetch: searchRefetch,
  }] = useLazyQuery(GET_PROVIDERS_CATALOG_SEARCH);

  const [getProvidersCatalog, {
    data: catalogData, loading: catalogLoading, error: catalogError, refetch: catalogRefetch,
  }] = useLazyQuery(GET_PROVIDERS_CATALOG);

  useEffect(() => {
    if (companyId) {
      if (isSearching) {
        getProvidersCatalogSearch({
          variables: {
            limit,
            offset,
            searchTerm,
            whereFilter,
            orderBy: [{ [orderBySearch]: orderSearch }],
          },
        });
      } else {
        getProvidersCatalog({
          variables: {
            limit,
            offset,
            orderBy: [{ [orderBy]: order }],
            companyId,
            whereFilter,
          },
        });
      }
    }
  // eslint-disable-next-line max-len
  }, [companyId, isSearching, limit, offset, searchTerm, JSON.stringify(whereFilter), orderBySearch, orderSearch, getProvidersCatalogSearch, getProvidersCatalog, orderBy, order]);

  const items = isSearching ? searchData?.providers_catalog_search : catalogData?.catalog;
  const totalNumberOfItems = isSearching
    ? searchData?.providers_catalog_search_aggregate?.aggregate?.count
    : catalogData?.catalog_aggregate?.aggregate?.count;
  const loading = isSearching ? searchLoading : catalogLoading;
  const error = isSearching ? searchError : catalogError;
  const refetch = isSearching ? searchRefetch : catalogRefetch;

  return {
    items,
    totalNumberOfItems,
    loading,
    error: error as Error,
    refetch,
  };
};

const queryProvidersWithCompanyId = gql`
  query GetProvidersWithCompanyId($limit: Int!, $offset: Int!, $orderBy: [provider_order_by!], $where: provider_bool_exp) {
    provider(
      limit: $limit,
      offset: $offset,
      order_by: $orderBy,
      where: $where
    ) {
      id
      name
      tributaryId
      providerCompanyId
      providerInventories_aggregate {
        aggregate {
          count
        }
      }
    }
    provider_aggregate(where: $where) {
      aggregate {
        count
      }
    }
  }
`;

export const useGetProvidersWithCompanyId = ({
  limit = 10, offset = 0, orderBy = 'name', order = 'asc', searchTerm,
}: GetTableDataProps): GetTableDataResponse<Provider> => {
  const baseCondition = {
    providerCompanyId: { _is_null: false },
  };

  const searchCondition = searchTerm
    ? { ...baseCondition, name: { _ilike: `%${searchTerm}%` } }
    : baseCondition;

  const {
    data, loading, error, refetch,
  } = useQuery(queryProvidersWithCompanyId, {
    fetchPolicy: 'network-only',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      where: searchCondition,
    },
  });

  return {
    items: data?.provider,
    totalNumberOfItems: data?.provider_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelProviderService : ProviderService = {
  useGetProvidersCatalog,
  useGetProviders,
  useGetProvidersWithCompanyId,
  createProviders: ProvidersHttps.postProviders,
  updateProvider: ProvidersHttps.updateProvider,
  connectProvider: ProvidersHttps.connectProvider,
  inviteProvider: ProvidersHttps.inviteProvider,
  checkTributaryId: ProvidersHttps.checkTributaryId,
};

export default ReadModelProviderService;
