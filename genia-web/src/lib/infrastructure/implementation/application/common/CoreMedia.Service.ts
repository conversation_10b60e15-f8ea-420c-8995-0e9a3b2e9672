import {
  MediaImage, MediaService, SignedUrlResult,
} from '#application/common/services/Media.Service';
import { MediaHttps } from '#infrastructure/api/http/Media.Http';

const getSignedUrlsInParallel = async (props: {
  filesToUpload: MediaImage[];
  kind: string;
  entity: string;
  entityId: string;
  getSignedUrlEndpoint: string;
  token: string | null | undefined;
}): Promise<SignedUrlResult[]> => {
  const {
    filesToUpload, kind, entity, entityId, getSignedUrlEndpoint, token,
  } = props;

  const onlyFiles = filesToUpload.filter((file): file is File => file instanceof File);

  const promises = onlyFiles.map(async (file) => {
    const signedUrlPayload = {
      kind,
      entity,
      entityId,
      contentType: file.type || 'image/jpeg',
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const signedUrlResponse = await fetch(getSignedUrlEndpoint, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify(signedUrlPayload),
      });

      if (!signedUrlResponse.ok) {
        const errorData = await signedUrlResponse.json().catch(() => ({ message: 'Error al obtener URL firmada o la respuesta no fue JSON.' }));
        throw new Error(`Error al obtener URL firmada: ${signedUrlResponse.status} ${signedUrlResponse.statusText}. ${errorData.message || ''}`);
      }

      if (signedUrlResponse.ok) {
        const signedUrlData = await signedUrlResponse.json();

        if (!signedUrlData) {
          throw new Error('No se recibió la URL firmada de API.');
        }

        return {
          success: true, fileName: file.name, fileObject: file, data: signedUrlData,
        };
      }

      const errorData = await signedUrlResponse.text();
      return {
        success: false, fileName: file.name, fileObject: file, status: signedUrlResponse.status, error: errorData,
      };
    } catch (error) {
      return {
        success: false,
        fileName: file.name,
        fileObject: file,
        message: 'Error al obtener URL firmada o la respuesta no fue JSON.',
      };
    }
  });

  try {
    const results = await Promise.all(promises);
    return results;
  } catch (error) {
    throw new Error('Error inesperado por favor intenta nuevamente');
  }
};

const assignMediaToEntity = async (
  entity: string,
  entityId: string,
  urlsArray: { url: string | undefined }[],
  token: string | null | undefined,
  postMediaEndpoint: string,
): Promise<Response> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const assignToEntity = await fetch(`${postMediaEndpoint}/${entity}/${entityId}`, {
    method: 'POST',
    headers,
    credentials: 'include',
    body: JSON.stringify(urlsArray),
  });

  if (!assignToEntity.ok) {
    const errorData = await assignToEntity.json().catch(() => ({ message: 'Error al asignar media a la entidad.' }));
    throw new Error(`Error al asignar media: ${assignToEntity.status} ${assignToEntity.statusText}. ${errorData.message || ''}`);
  }

  return assignToEntity;
};

const deleteMedia = async (
  mediaIds: string[],
  entity: string,
  entityId: string,
  token: string | null | undefined,
): Promise<Response> => {
  try {
    const response = await MediaHttps.deleteMediaFromEntity(token || '', entity, entityId, mediaIds);
    return response.data;
  } catch (error) {
    throw new Error(`Error al eliminar media de la entidad: ${(error as Error).message}`);
  }
};

const CoreMediaService: MediaService = {
  getSignedUrlsInParallel,
  assignMediaToEntity,
  deleteMedia,
};

export default CoreMediaService;
