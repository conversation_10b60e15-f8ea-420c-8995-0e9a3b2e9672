import { useNavigate } from 'react-router-dom';

import PathService from '#application/common/services/Path.Service';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';

const { GENIA_NODE_API } = GetEnv();

const CLIENTS_PATH = '/clients';
const PROVIDERS_PATH = '/providers';
const INVENTORY_PATH = '/inventory';
const SALE_ORDERS_PATH = '/sale-orders';
const CATALOG_PATH = '/catalog';
const CATALOG_DISCOUNT_PATH = '/catalog-discount';
const STORE_DISCOUNT_PATH = '/store-discount';
const STORE_PATH = '/store';
const CLAUDIA_PATH = '/claud-ia';
const SIGNED_URL_PATH = '/signed-url';
const MEDIA_PATH = '/media';
const USER_PATH = '/users';
const COMPANY_PATH = '/company';
const DASHBOARD_PATH = '/dashboard';
const SETTINGS_PATH = '/settings';

function detailedPath(currentPath: string, id: string, includePrefix: boolean = true): string {
  return includePrefix ? `/app${currentPath}/${id}` : `${currentPath}/${id}`;
}

function addPath(currentPath: string, includePrefix: boolean = true): string {
  return includePrefix ? `/app${currentPath}/new` : `${currentPath}/new`;
}

function basePath(currentPath: string, includePrefix: boolean = true): string {
  return includePrefix ? `/app${currentPath}` : currentPath;
}

function useGoToPath(): (toPath: string) => void {
  const navigate = useNavigate();
  return (toPath: string) => {
    navigate(toPath);
  };
}

const CorePath: PathService = {
  useGoToPath,
  saleOrders: {
    viewSaleOrder: (id: string, includePrefix: boolean = true) => detailedPath(SALE_ORDERS_PATH, id, includePrefix),
    addSaleOrder: (includePrefix: boolean = true) => addPath(SALE_ORDERS_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(SALE_ORDERS_PATH, includePrefix),
  },
  purchaseOrders: {
    viewPurchaseOrder: (id: string, includePrefix: boolean = true) => detailedPath('/purchase-orders', id, includePrefix),
    addPurchaseOrder: (includePrefix: boolean = true) => addPath('/purchase-orders', includePrefix),
    addPurchaseOrderFromExternal: (from: string, includePrefix: boolean = true) => basePath(`new/${from}`, includePrefix),
    base: (includePrefix: boolean = true) => basePath('/purchase-orders', includePrefix),
  },
  clients: {
    viewClient: (id: string, includePrefix: boolean = true) => detailedPath(CLIENTS_PATH, id, includePrefix),
    addClient: (includePrefix: boolean = true) => addPath(CLIENTS_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(CLIENTS_PATH, includePrefix),
  },
  providers: {
    viewProvider: (id: string, includePrefix: boolean = true) => detailedPath(PROVIDERS_PATH, id, includePrefix),
    addProvider: (includePrefix: boolean = true) => addPath(PROVIDERS_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(PROVIDERS_PATH, includePrefix),
    viewProviderCatalog: (id: string, includePrefix: boolean = true) => detailedPath(`${PROVIDERS_PATH}${CATALOG_PATH}`, id, includePrefix),
    providerCatalogBase: (includePrefix: boolean = true) => basePath(`${PROVIDERS_PATH}${CATALOG_PATH}`, includePrefix),
  },
  inventory: {
    viewInventory: (id: string, includePrefix: boolean = true) => detailedPath(INVENTORY_PATH, id, includePrefix),
    addInventory: (includePrefix: boolean = true) => addPath(INVENTORY_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(INVENTORY_PATH, includePrefix),
  },
  catalog: {
    viewCatalog: (id: string, includePrefix: boolean = true) => detailedPath(CATALOG_PATH, id, includePrefix),
    addCatalog: (includePrefix: boolean = true) => addPath(CATALOG_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(CATALOG_PATH, includePrefix),
  },
  catalogDiscounts: {
    viewCatalogDiscount: (id: string, includePrefix: boolean = true) => detailedPath(CATALOG_DISCOUNT_PATH, id, includePrefix),
    addCatalogDiscount: (includePrefix: boolean = true) => addPath(CATALOG_DISCOUNT_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(CATALOG_DISCOUNT_PATH, includePrefix),
  },
  storeDiscounts: {
    viewStoreDiscount: (id: string, includePrefix: boolean = true) => detailedPath(STORE_DISCOUNT_PATH, id, includePrefix),
    addStoreDiscount: (includePrefix: boolean = true) => addPath(STORE_DISCOUNT_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(STORE_DISCOUNT_PATH, includePrefix),
  },
  store: {
    base: (includePrefix: boolean = true) => basePath(STORE_PATH, includePrefix),
  },
  claudia: {
    base: (includePrefix: boolean = true) => basePath(CLAUDIA_PATH, includePrefix),
  },
  users: {
    viewUser: (id: string, includePrefix: boolean = true) => detailedPath(USER_PATH, id, includePrefix),
    addUser: (includePrefix: boolean = true) => addPath(USER_PATH, includePrefix),
    base: (includePrefix: boolean = true) => basePath(USER_PATH, includePrefix),
  },
  company: {
    base: (includePrefix: boolean = true) => basePath(COMPANY_PATH, includePrefix),
  },
  dashboard: {
    base: (includePrefix: boolean = true) => basePath(DASHBOARD_PATH, includePrefix),
  },
  signedUrl: {
    base: () => `${GENIA_NODE_API}${SIGNED_URL_PATH}`,
  },
  media: {
    base: () => `${GENIA_NODE_API}${MEDIA_PATH}`,
  },
  settings: {
    base: (includePrefix: boolean = true) => basePath(SETTINGS_PATH, includePrefix),
  },
};

export default CorePath;
