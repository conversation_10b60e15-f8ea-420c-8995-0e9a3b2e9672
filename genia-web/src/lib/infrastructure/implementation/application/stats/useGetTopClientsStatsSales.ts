import { gql, useQuery } from '@apollo/client';
import { useContext } from 'react';

import { GetStatsDataResponse, TopStats } from '#application/stats/Stats.Type';
import { AuthContext } from '#infrastructure/AuthState.Context';

export const useGetTopClients = (): GetStatsDataResponse<TopStats> => {
  const { systemUser: { companyId } } = useContext(AuthContext);

  const query = gql`
        query GetTopClients($company_id: uuid!) {
            get_sale_orders_totals_by_clients(args: {company_id: $company_id}) {
                data_points
            }
        }
    `;

  const { data, loading, error } = useQuery(query, {
    variables: { company_id: companyId },
  });

  return {
    items: data?.get_sale_orders_totals_by_clients[0]?.data_points || [],
    total: 0,
    loading,
    error: error as Error,
  };
};
