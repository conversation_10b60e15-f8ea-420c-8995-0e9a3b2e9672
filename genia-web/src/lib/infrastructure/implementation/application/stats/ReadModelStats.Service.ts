import { StatsService } from '#application/stats/services/Stats.Service';
import { GetStatsDataProps, GetStatsDataResponse, StatsByPeriod } from '#application/stats/Stats.Type';
import { useGetSaleOrdersTotalsByLast30Days } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsByLast30Days';
import { useGetSaleOrdersTotalsByLastMonth } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsByLastMonth';
import { useGetSaleOrdersTotalsByLastYear } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsByLastYear';
import { useGetSaleOrdersTotalsByQuarter } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsByQuarter';
import { useGetSaleOrdersTotalsBySemester } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsBySemester';
import { useGetSaleOrdersTotalsByThisMonth } from '#infrastructure/implementation/application/stats/useGetSaleOrdersTotalsByThisMonth';
import { useGetTopClients } from '#infrastructure/implementation/application/stats/useGetTopClientsStatsSales';

const statsPeriodMap = {
  last30Days: useGetSaleOrdersTotalsByLast30Days,
  thisMonth: useGetSaleOrdersTotalsByThisMonth,
  lastMonth: useGetSaleOrdersTotalsByLastMonth,
  byQuarter: useGetSaleOrdersTotalsByQuarter,
  bySemester: useGetSaleOrdersTotalsBySemester,
  lastYear: useGetSaleOrdersTotalsByLastYear,
};

const useGetTotalSalesByPeriod = (props: GetStatsDataProps): GetStatsDataResponse<StatsByPeriod> => {
  const { period = 'last30Days' } = props;
  const hook = statsPeriodMap[period as keyof typeof statsPeriodMap];

  return hook();
};

const ReadModelStatsService: StatsService = {
  useGetTotalSalesByPeriod,
  useGetTopClients,
};

export default ReadModelStatsService;
