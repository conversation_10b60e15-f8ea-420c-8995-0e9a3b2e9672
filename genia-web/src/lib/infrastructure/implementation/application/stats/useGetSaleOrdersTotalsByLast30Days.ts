import { gql, useQuery } from '@apollo/client';
import { useContext } from 'react';

import { GetStatsDataResponse, StatsByPeriod } from '#application/stats/Stats.Type';
import { AuthContext } from '#infrastructure/AuthState.Context';

export const useGetSaleOrdersTotalsByLast30Days = (): GetStatsDataResponse<StatsByPeriod> => {
  const { systemUser: { companyId } } = useContext(AuthContext);

  const query = gql`
        query GetLast30DaysQuery($company_id: uuid!) {
            get_sale_orders_totals_by_last_30_days(args: {company_id: $company_id}) {
                data_points
                period_total
            }
        }
    `;

  const { data, loading, error } = useQuery(query, {
    variables: { company_id: companyId },
  });

  return {
    items: data?.get_sale_orders_totals_by_last_30_days[0]?.data_points || [],
    total: data?.get_sale_orders_totals_by_last_30_days[0]?.period_total || 0,
    loading,
    error: error as Error,
  };
};
