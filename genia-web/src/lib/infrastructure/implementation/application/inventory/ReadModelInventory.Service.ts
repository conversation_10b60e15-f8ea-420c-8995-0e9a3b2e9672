import { gql, useLazyQuery, useQuery } from '@apollo/client';
import { AxiosResponse } from 'axios';
import { useEffect } from 'react';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { NewFormInventoryProps } from '#application/deprecated/DashboardPages.Type';
import { CreateHistoryMovementProps, InventoryItem } from '#application/inventory/Inventory.Type';
import { InventoryService } from '#application/inventory/services/Inventory.Service';
import { InventoryHttps } from '#infrastructure/api/http/Inventory.api';
import { useGetInventoryHistory } from '#infrastructure/implementation/application/inventory/useGetInventoryHistory';

const columns = `
  id
  name
  sku
  stock
  attributes
  measurementUnit
  disabledAt
  hasStockValidation
  companyId
  createdAt
  description
  standardIdentifier
  restrictedStock
  type
  inventoryMedia {
    mediaType
    url
  }
`;

const GET_INVENTORY = gql`
  query GetInventory($limit: Int!, $offset: Int!, $orderBy: [inventory_order_by!], $filters: inventory_bool_exp) {
    inventory(
      limit: $limit,
      offset: $offset,
      order_by: $orderBy,
      where: $filters
    ) {
      ${columns}
    }
    inventory_aggregate(where: $filters){
      aggregate {
        count
      }
    }
  }
`;

const GET_INVENTORY_BY_SEARCH = gql`
  query GetInventoryBySearch($term: String!) {
    inventory_search(args: {search_term: $term}) {
      ${columns}
    }
    inventory_search_aggregate(args: {search_term: $term}) {
      aggregate {
        count
      }
    }
  }
`;

const GET_INVENTORY_ITEM = gql`
  query GetInventoryItem($id: uuid!) {
    inventory(where: {id: {_eq: $id}}) {
      attributes
      createdAt
      description
      disabledAt
      hasStockValidation
      name
      sku
      stock
      restrictedStock
      standardIdentifier
      type
      updatedAt
      measurementUnit
      inventoryMedia {
        id
        mediaType
        url
      }
    }
  }
`;

export const useGetInventory = ({
  limit = 10, offset = 0, orderBy = 'createdAt', order = 'desc', searchTerm, filters,
}: GetTableDataProps): GetTableDataResponse<InventoryItem> => {
  let filter;
  if (filters && filters.id && filters.id.length > 0) {
    filter = { id: { _in: filters.id } };
  }

  const {
    data, loading, error, refetch,
  } = useQuery(GET_INVENTORY, {
    fetchPolicy: 'network-only',
    skip: limit === 0,
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      filters: filter,
    },
  });

  const [
    getInventoryBySearch, { data: dataGetInventoryBySearch, loading: loadingGetInventoryBySearch },
  ] = useLazyQuery(GET_INVENTORY_BY_SEARCH, {
    fetchPolicy: 'network-only',
    variables: {
      term: searchTerm,
    },
  });

  useEffect(() => {
    if (searchTerm) {
      getInventoryBySearch();
    }
  }, [searchTerm, getInventoryBySearch]);

  return {
    items: searchTerm ? dataGetInventoryBySearch?.inventory_search : data?.inventory,
    totalNumberOfItems: searchTerm ? dataGetInventoryBySearch?.inventory_search_aggregate?.aggregate?.count : data?.inventory_aggregate?.aggregate?.count,
    loading: loadingGetInventoryBySearch || loading,
    error: error as Error,
    refetch,
  };
};

const useGetInventoryItem = (id: string) => {
  const [getInventoryItem, {
    data, loading, error, refetch: refetchInventoryItem,
  }] = useLazyQuery(GET_INVENTORY_ITEM, {
    fetchPolicy: 'network-only',
    variables: {
      id,
    },
  });

  useEffect(() => {
    if (id) {
      getInventoryItem();
    }
  }, [id]);

  return {
    inventoryItem: data?.inventory[0],
    loading,
    error: error as Error,
    refetchInventoryItem,
  };
};

const createInventory = async (token: string, payload: NewFormInventoryProps): Promise<AxiosResponse> => InventoryHttps.createInventory(token, payload);
const updateInventory = async (token: string, id: string, payload: NewFormInventoryProps): Promise<AxiosResponse> => InventoryHttps.updateInventory(token, id, payload);

const createInventoryHistoryMovement = async (props: CreateHistoryMovementProps): Promise<AxiosResponse> => InventoryHttps.createHistoryMovement(props);

const ReadModelInventoryService: InventoryService = {
  useGetInventory,
  useGetInventoryItem,
  createInventory,
  updateInventory,
  useGetInventoryHistory,
  createInventoryHistoryMovement,
};

export default ReadModelInventoryService;
