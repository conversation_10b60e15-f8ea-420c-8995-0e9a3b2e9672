import { gql, useQuery } from '@apollo/client';

import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { InventoryHistory } from '#application/inventory/Inventory.Type';

const GET_INVENTORY_HISTORY = gql`
    query GetInventoryHistory(
      $limit: Int!,
      $offset: Int!,
      $orderBy: [inventoryHistory_order_by!],
      $filters: inventoryHistory_bool_exp
    ) {
      inventoryHistory(
        limit: $limit,
        offset: $offset,
        order_by: $orderBy,
        where: $filters 
      ){ 
      reason
      quantity
      userId
      inventoryId
      measurementUnit
      movementType
      id
      createdAt
      user {
        email
      }
    }
    inventoryHistory_aggregate (
      where: $filters
    ) {
        aggregate {
        count
        }
      }
    }
`;

export const useGetInventoryHistory = ({
  limit = 10, offset = 0, orderBy = 'createdAt', order = 'desc', filters,
}: GetTableDataProps): GetTableDataResponse<InventoryHistory> => {
  let filter;
  if (filters && filters.inventoryId.length > 0) {
    filter = { inventoryId: { _eq: filters.inventoryId[0] } };
  }
  const {
    data, loading, error, refetch,
  } = useQuery(GET_INVENTORY_HISTORY, {
    fetchPolicy: 'network-only',
    variables: {
      limit,
      offset,
      orderBy: [{ [orderBy]: order }],
      filters: filter,
    },
  });

  return {
    items: data?.inventoryHistory,
    totalNumberOfItems: data?.inventoryHistory_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};
