export type MovementType = 'inbound' | 'outbound';

export interface Field<T = string | number> {
    value: T;
}

interface InventoryHistoryValueObjectParams {
    movementType: Field<MovementType>;
    quantity: Field<number>;
    reason: Field<string | null>;
}

export class InventoryHistoryValueObject {
  movementType: Field<MovementType>;

  quantity: Field<number>;

  reason: Field<string | null>;

  constructor(params: InventoryHistoryValueObjectParams) {
    this.movementType = params.movementType;
    this.quantity = params.quantity;
    this.reason = params.reason;
  }

  setMovementType(value: Field<MovementType>): InventoryHistoryValueObject {
    return new InventoryHistoryValueObject({
      ...this,
      movementType: value,
    });
  }

  setQuantity(value: Field<number>): InventoryHistoryValueObject {
    return new InventoryHistoryValueObject({
      ...this,
      quantity: value,
    });
  }

  setReason(value: Field<string>): InventoryHistoryValueObject {
    return new InventoryHistoryValueObject({
      ...this,
      reason: value,
    });
  }
}
