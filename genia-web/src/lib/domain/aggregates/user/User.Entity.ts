export enum UserRole {
    ADMIN = 'ADMIN',
    USER = 'USER',
  }

export class User {
  id: string;

  email: string;

  name?: string;

  lastName?: string;

  appPhoneNumber?: string;

  disabledAt?: Date | null;

  userCompanies?: {
      company: {
        id: string;
        name: string;
      };
      role: UserRole;
    }[];

  constructor(user: User) {
    this.id = user.id;
    this.email = user.email;
    this.name = user.name;
    this.lastName = user.lastName;
    this.appPhoneNumber = user.appPhoneNumber;
    this.disabledAt = user.disabledAt;
    this.userCompanies = user.userCompanies;
  }
}
