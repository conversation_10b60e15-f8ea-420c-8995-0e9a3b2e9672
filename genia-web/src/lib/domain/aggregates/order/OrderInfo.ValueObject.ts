import { OrderStatus, OrderStatusValueObject, OrderStatusValueObjectParams } from '#domain/aggregates/order/OrderStatus.ValueObject';
import { OrderSummary, OrderSummaryValueObject, OrderSummaryValueObjectParams } from '#domain/aggregates/order/OrderSummary.ValueObject';

export interface OrderInfo {
  notes: string;
  orderNumber: string;
  shippingPrice: number;
  shippingAddress: string;
  deliveryDate: string;
  summary: OrderSummary;
  status: OrderStatus;
  assignedUser: {
    id: string;
    email: string;
  } | null;
}

export interface Detail<T = string | number> {
  value: T;
  disabled: boolean;
  hidden: boolean;
}

export interface OrderInfoValueObjectParams {
  notes: Detail<string>;
  orderNumber: Detail<string>;
  shippingPrice: Detail<number | null>;
  shippingAddress: Detail<string>;
  deliveryDate: Detail<string>;
  summary: OrderSummaryValueObject;
  status: Detail<OrderStatusValueObject>;
  assignedUser: {
    id: string;
    email: string;
  } | null;
}

export class OrderInfoValueObject {
  notes: Detail<string>;

  orderNumber: Detail<string>;

  shippingPrice: Detail<number | null>;

  shippingAddress: Detail<string>;

  deliveryDate: Detail<string>;

  summary: OrderSummaryValueObject;

  status: Detail<OrderStatusValueObject>;

  assignedUser: {
    id: string;
    email: string;
  } | null;

  constructor(params: OrderInfoValueObjectParams) {
    this.orderNumber = params.orderNumber;
    this.shippingPrice = params.shippingPrice;
    this.shippingAddress = params.shippingAddress;
    this.deliveryDate = params.deliveryDate;
    this.notes = params.notes;
    this.summary = params.summary;
    this.status = params.status;
    this.assignedUser = params.assignedUser;
  }

  setOrderNumber(value: Detail<string>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      orderNumber: value,
    });
  }

  setShippingPrice(value: Detail<number | null>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      shippingPrice: value,
    });
  }

  setShippingAddress(value: Detail<string>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      shippingAddress: value,
    });
  }

  setDeliveryDate(value: Detail<string>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      deliveryDate: value,
    });
  }

  setNotes(value: Detail<string>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      notes: value,
    });
  }

  setSummary(value: OrderSummaryValueObjectParams): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      summary: new OrderSummaryValueObject(value),
    });
  }

  setStatus(value: Detail<OrderStatusValueObjectParams>): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      status: value,
    });
  }

  disableOrderNumber(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      orderNumber: {
        ...this.orderNumber,
        disabled: disable,
      },
    });
  }

  hideOrderNumber(hide: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      orderNumber: {
        ...this.orderNumber,
        hidden: hide,
      },
    });
  }

  disableShippingPrice(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      shippingPrice: {
        ...this.shippingPrice,
        disabled: disable,
      },
    });
  }

  disableShippingAddress(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      shippingAddress: {
        ...this.shippingAddress,
        disabled: disable,
      },
    });
  }

  disableDeliveryDate(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      deliveryDate: {
        ...this.deliveryDate,
        disabled: disable,
      },
    });
  }

  disableNotes(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      notes: {
        ...this.notes,
        disabled: disable,
      },
    });
  }

  disableStatus(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      status: {
        ...this.status,
        disabled: disable,
      },
    });
  }

  disable(disable: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      orderNumber: {
        ...this.orderNumber,
        disabled: disable,
      },
      shippingPrice: {
        ...this.shippingPrice,
        disabled: disable,
      },
      shippingAddress: {
        ...this.shippingAddress,
        disabled: disable,
      },
      deliveryDate: {
        ...this.deliveryDate,
        disabled: disable,
      },
      notes: {
        ...this.notes,
        disabled: disable,
      },
    });
  }

  hideStatus(hide: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      status: {
        ...this.status,
        hidden: hide,
      },
    });
  }

  hideShippingPrice(hide: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      shippingPrice: {
        ...this.shippingPrice,
        hidden: hide,
      },
    });
  }

  hideDeliveryDate(hide: boolean): OrderInfoValueObject {
    return new OrderInfoValueObject({
      ...this,
      deliveryDate: {
        ...this.deliveryDate,
        hidden: hide,
      },
    });
  }

  toJson(): OrderInfo {
    return {
      orderNumber: this.orderNumber.value,
      shippingPrice: this.shippingPrice.value || 0,
      shippingAddress: this.shippingAddress.value,
      summary: this.summary.toJson(),
      deliveryDate: this.deliveryDate.value,
      notes: this.notes.value,
      status: this.status.value.toJson(),
      assignedUser: this.assignedUser,
    };
  }

  static fromJson(json: OrderInfo): OrderInfoValueObject {
    return new OrderInfoValueObject({
      orderNumber: { value: json.orderNumber, disabled: false, hidden: false },
      shippingPrice: { value: json.shippingPrice, disabled: false, hidden: false },
      shippingAddress: { value: json.shippingAddress, disabled: false, hidden: false },
      deliveryDate: { value: json.deliveryDate, disabled: false, hidden: false },
      notes: { value: json.notes, disabled: false, hidden: false },
      summary: OrderSummaryValueObject.fromJson(json.summary),
      status: { value: OrderStatusValueObject.fromJson(json.status), disabled: false, hidden: false },
      assignedUser: json.assignedUser,
    });
  }

  static empty(): OrderInfoValueObject {
    return new OrderInfoValueObject({
      orderNumber: { value: '', disabled: false, hidden: false },
      shippingPrice: { value: 0, disabled: false, hidden: false },
      shippingAddress: { value: '', disabled: false, hidden: false },
      deliveryDate: { value: '', disabled: false, hidden: false },
      notes: { value: '', disabled: false, hidden: false },
      summary: OrderSummaryValueObject.empty(),
      status: { value: OrderStatusValueObject.empty(), disabled: false, hidden: false },
      assignedUser: null,
    });
  }
}
