export interface OrderItemDiscount {
  id: string;
  discountValue: number;
  discountType: string;
  name: string;
  priceAfterDiscount: number;
  startDate?: string | null;
  endDate?: string | null;
  discountCategory: 'catalog' | 'store';
  required: number;
}

export interface OrderItemDiscountParams {
  id: string;
  discountValue: number;
  discountType: string;
  name: string;
  priceAfterDiscount: number;
  startDate?: string | null;
  endDate?: string | null;
  discountCategory: 'catalog' | 'store';
  required: number;
}

export class OrderItemDiscountEntity {
  id: string;

  discountValue: number;

  discountType: string;

  name: string;

  priceAfterDiscount: number;

  startDate?: string | null;

  endDate?: string | null;

  discountCategory: 'catalog' | 'store';

  required: number;

  constructor(params: OrderItemDiscountParams) {
    this.id = params.id;
    this.discountValue = params.discountValue;
    this.discountType = params.discountType;
    this.name = params.name;
    this.priceAfterDiscount = params.priceAfterDiscount;
    this.startDate = params.startDate;
    this.endDate = params.endDate;
    this.discountCategory = params.discountCategory;
    this.required = params.required;
  }

  toJson(): OrderItemDiscount {
    return {
      id: this.id,
      discountValue: this.discountValue,
      discountType: this.discountType,
      name: this.name,
      priceAfterDiscount: this.priceAfterDiscount,
      startDate: this.startDate,
      endDate: this.endDate,
      discountCategory: this.discountCategory,
      required: this.required,
    };
  }

  static fromJson(json: OrderItemDiscount): OrderItemDiscountEntity {
    return new OrderItemDiscountEntity({
      id: json.id,
      discountValue: json.discountValue,
      discountType: json.discountType,
      name: json.name,
      priceAfterDiscount: json.priceAfterDiscount,
      startDate: json.startDate,
      endDate: json.endDate,
      discountCategory: json.discountCategory,
      required: json.required,
    });
  }
}
