export interface OrderReceiver {
  id: string;
  name: string;
  type: 'client' | 'provider';
  companyId: string | null;
  providerCompanyId?: string | null;
}

export interface OrderReceiverEntityParams {
  id: string;
  name: string;
  disabled: boolean;
  type: 'client' | 'provider';
  companyId: string | null;
}

export class OrderReceiverEntity {
  id: string;

  name: string;

  disabled: boolean;

  type: 'client' | 'provider';

  companyId: string | null = null;

  constructor(params: OrderReceiverEntityParams) {
    this.id = params.id;
    this.name = params.name;
    this.disabled = params.disabled;
    this.type = params.type;
    this.companyId = params.companyId;
  }

  setId(id: string): OrderReceiverEntity {
    return new OrderReceiverEntity({
      ...this,
      id,
    });
  }

  setName(name: string): OrderReceiverEntity {
    return new OrderReceiverEntity({
      ...this,
      name,
    });
  }

  setDisabled(disabled: boolean): OrderReceiverEntity {
    return new OrderReceiverEntity({
      ...this,
      disabled,
    });
  }

  setType(type: 'client' | 'provider'): OrderReceiverEntity {
    return new OrderReceiverEntity({
      ...this,
      type,
    });
  }

  setCompanyId(companyId: string | null): OrderReceiverEntity {
    return new OrderReceiverEntity({
      ...this,
      companyId,
    });
  }

  toJson(): OrderReceiver {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      companyId: this.companyId,
    };
  }

  isValid(): boolean {
    return !!this.id && !!this.name && !!this.type;
  }

  static fromJson(json: OrderReceiver): OrderReceiverEntity {
    return new OrderReceiverEntity({
      id: json.id,
      name: json.name,
      disabled: false,
      type: json.type,
      companyId: json.companyId || null,
    });
  }

  static empty(type: 'client' | 'provider'): OrderReceiverEntity {
    return new OrderReceiverEntity({
      id: '',
      name: '',
      disabled: false,
      type,
      companyId: null,
    });
  }
}
