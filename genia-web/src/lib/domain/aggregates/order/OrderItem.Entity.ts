import { OrderItemDiscount, OrderItemDiscountEntity } from '#domain/aggregates/order/OrderItemDiscount.Entity';
import { OrderSummary, OrderSummaryValueObject, OrderSummaryValueObjectParams } from '#domain/aggregates/order/OrderSummary.ValueObject';

interface OrderItemProperty<T = string | number> {
  value: T;
  disabled: boolean;
  error?: string;
}

export enum OrderItemError {
  UNIT_PRICE_REQUIRED = 'UNIT_PRICE_REQUIRED',
  NAME_REQUIRED = 'NAME_REQUIRED',
  PRODUCT_NUMBER_REQUIRED = 'PRODUCT_NUMBER_REQUIRED',
  QUANTITY_REQUIRED = 'QUANTITY_REQUIRED',
  UNIT_PRICE_AFTER_DISCOUNT_REQUIRED = 'UNIT_PRICE_AFTER_DISCOUNT_REQUIRED',
  UNIT_PRICE_AFTER_DISCOUNT_INVALID = 'UNIT_PRICE_AFTER_DISCOUNT_INVALID',
}

export interface OrderItemInventoryRelation {
  id: string;
  sku: string;
  name: string;
  quantity: number;
  image: string;
  total: number;
}

export interface OrderItemDiscounts {
  applied: OrderItemDiscountEntity | null;
  applicable: OrderItemDiscountEntity[];
  loading: boolean;
}

interface OrderItemParams {
  id: string;
  name: OrderItemProperty<string>;
  productNumber: OrderItemProperty<string>;
  quantity: OrderItemProperty<number>;
  unitPrice: OrderItemProperty<number>;
  unitPriceAfterDiscount: OrderItemProperty<number>;
  unitPriceAfterDiscountAndTaxes: OrderItemProperty<number>;
  image: OrderItemProperty<string>;
  summary: OrderSummaryValueObject;
  inventoryRelations: OrderItemInventoryRelation[];
  discounts: OrderItemDiscounts;
}

export interface OrderItem {
  id: string;
  name: string;
  productNumber: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  image: string;
  summary: OrderSummary;
  inventoryRelations : OrderItemInventoryRelation[];
  discounts: {
    applied: OrderItemDiscount | null;
    applicable: OrderItemDiscount[];
  };

}

export class OrderItemEntity {
  id: string;

  name: OrderItemProperty<string>;

  productNumber: OrderItemProperty<string>;

  quantity: OrderItemProperty<number>;

  unitPrice: OrderItemProperty<number>;

  unitPriceAfterDiscount: OrderItemProperty<number>;

  unitPriceAfterDiscountAndTaxes: OrderItemProperty<number>;

  image: OrderItemProperty<string>;

  summary: OrderSummaryValueObject;

  inventoryRelations : OrderItemInventoryRelation[];

  discounts: OrderItemDiscounts;

  constructor(params: OrderItemParams) {
    this.id = params.id;
    this.name = params.name;
    this.productNumber = params.productNumber;
    this.quantity = params.quantity;
    this.unitPrice = params.unitPrice;
    this.unitPriceAfterDiscount = params.unitPriceAfterDiscount;
    this.image = params.image;
    this.summary = params.summary;
    this.inventoryRelations = params.inventoryRelations;
    this.unitPriceAfterDiscountAndTaxes = params.unitPriceAfterDiscountAndTaxes;
    this.discounts = params.discounts;
  }

  setId(id: string) {
    return new OrderItemEntity({
      ...this,
      id,
    });
  }

  setName(name: OrderItemProperty<string>) {
    return new OrderItemEntity({
      ...this,
      name,
    });
  }

  setProductNumber(productNumber: OrderItemProperty<string>) {
    return new OrderItemEntity({
      ...this,
      productNumber,
    });
  }

  setQuantity(quantity: OrderItemProperty<number>) {
    return new OrderItemEntity({
      ...this,
      quantity,
    });
  }

  setUnitPrice(unitPrice: OrderItemProperty<number>) {
    return new OrderItemEntity({
      ...this,
      unitPrice,
    });
  }

  setUnitPriceAfterDiscount(unitPriceAfterDiscount: OrderItemProperty<number>) {
    return new OrderItemEntity({
      ...this,
      unitPriceAfterDiscount,
    });
  }

  setTotal(total: OrderItemProperty<number>) {
    return new OrderItemEntity({
      ...this,
      total,
    });
  }

  setImage(image: OrderItemProperty<string>) {
    return new OrderItemEntity({
      ...this,
      image,
    });
  }

  setSummary(summary: OrderSummaryValueObjectParams) {
    return new OrderItemEntity({
      ...this,
      summary: new OrderSummaryValueObject(summary),
    });
  }

  setUnitPriceAfterDiscountAndTaxes(unitPriceAfterDiscountAndTaxes: OrderItemProperty<number>) {
    return new OrderItemEntity({
      ...this,
      unitPriceAfterDiscountAndTaxes,
    });
  }

  disableName(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      name: {
        ...this.name,
        disabled: disable,
      },
    });
  }

  disableQuantity(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      quantity: {
        ...this.quantity,
        disabled: disable,
      },
    });
  }

  disableProductNumber(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      productNumber: {
        ...this.productNumber,
        disabled: disable,
      },
    });
  }

  disableUnitPrice(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      unitPrice: {
        ...this.unitPrice,
        disabled: disable,
      },
    });
  }

  disableUnitPriceAfterDiscount(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      unitPriceAfterDiscount: {
        ...this.unitPriceAfterDiscount,
        disabled: disable,
      },
    });
  }

  disableUnitPriceAfterDiscountAndTaxes(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      unitPriceAfterDiscountAndTaxes: {
        ...this.unitPriceAfterDiscountAndTaxes,
        disabled: disable,
      },
    });
  }

  setInventoryRelations(inventoryRelations: OrderItemInventoryRelation[]) {
    return new OrderItemEntity({
      ...this,
      inventoryRelations,
    });
  }

  addInventoryRelation(inventoryRelation: OrderItemInventoryRelation) {
    return new OrderItemEntity({
      ...this,
      inventoryRelations: [...this.inventoryRelations, inventoryRelation],
    });
  }

  removeInventoryRelation(inventoryRelation: OrderItemInventoryRelation) {
    return new OrderItemEntity({
      ...this,
      inventoryRelations: this.inventoryRelations.filter((relation) => relation.id !== inventoryRelation.id),
    });
  }

  setApplicableDiscounts(discounts: OrderItemDiscountEntity[]) {
    return new OrderItemEntity({
      ...this,
      discounts: {
        ...this.discounts,
        applicable: discounts,
      },
    });
  }

  setAppliedDiscount(discount: OrderItemDiscountEntity | null) {
    return new OrderItemEntity({
      ...this,
      discounts: {
        ...this.discounts,
        applied: discount,
      },
    });
  }

  disable(disable: boolean) {
    return new OrderItemEntity({
      ...this,
      name: {
        ...this.name,
        disabled: disable,
      },
      productNumber: {
        ...this.productNumber,
        disabled: disable,
      },
      quantity: {
        ...this.quantity,
        disabled: disable,
      },
      unitPrice: {
        ...this.unitPrice,
        disabled: disable,
      },
      unitPriceAfterDiscount: {
        ...this.unitPriceAfterDiscount,
        disabled: disable,
      },
      unitPriceAfterDiscountAndTaxes: {
        ...this.unitPriceAfterDiscountAndTaxes,
        disabled: disable,
      },
    });
  }

  validate(): OrderItemEntity {
    if (!this.name.value) {
      this.name = {
        ...this.name,
        error: OrderItemError.NAME_REQUIRED,
      };
    } else {
      this.name = {
        ...this.name,
        error: undefined,
      };
    }

    if (!this.productNumber.value) {
      this.productNumber = {
        ...this.productNumber,
        error: OrderItemError.PRODUCT_NUMBER_REQUIRED,
      };
    } else {
      this.productNumber = {
        ...this.productNumber,
        error: undefined,
      };
    }

    if (!this.quantity.value) {
      this.quantity = {
        ...this.quantity,
        error: OrderItemError.QUANTITY_REQUIRED,
      };
    } else {
      this.quantity = {
        ...this.quantity,
        error: undefined,
      };
    }

    if (!this.unitPrice.value) {
      this.unitPrice = {
        ...this.unitPrice,
        error: OrderItemError.UNIT_PRICE_REQUIRED,
      };
    } else {
      this.unitPrice = {
        ...this.unitPrice,
        error: undefined,
      };
    }

    if (!this.unitPriceAfterDiscount.value) {
      this.unitPriceAfterDiscount = {
        ...this.unitPriceAfterDiscount,
        error: OrderItemError.UNIT_PRICE_AFTER_DISCOUNT_REQUIRED,
      };
    } else if (this.unitPriceAfterDiscount.value > this.unitPrice.value) {
      this.unitPriceAfterDiscount = {
        ...this.unitPriceAfterDiscount,
        error: OrderItemError.UNIT_PRICE_AFTER_DISCOUNT_INVALID,
      };
    } else {
      this.unitPriceAfterDiscount = {
        ...this.unitPriceAfterDiscount,
        error: undefined,
      };
    }

    return new OrderItemEntity({
      ...this,
    });
  }

  isValid() {
    return this.name.value
      && this.productNumber.value
      && this.quantity.value
      && this.unitPrice.value
      && this.unitPriceAfterDiscount.value <= this.unitPrice.value
      && this.id;
  }

  static empty() {
    return new OrderItemEntity({
      id: '',
      name: { value: '', disabled: false },
      productNumber: { value: '', disabled: false },
      quantity: { value: 0, disabled: false },
      unitPrice: { value: 0, disabled: false },
      unitPriceAfterDiscount: { value: 0, disabled: false },
      unitPriceAfterDiscountAndTaxes: { value: 0, disabled: false },
      image: { value: '', disabled: false },
      summary: OrderSummaryValueObject.empty()
        .setSubtotalBeforeDiscount({ hidden: true, value: 0 })
        .setShippingPrice({ hidden: true, value: 0 })
        .setTotalDiscount({ hidden: true, value: 0 }),
      inventoryRelations: [],
      discounts: {
        applied: null,
        applicable: [],
        loading: false,
      },
    });
  }

  toJson(): OrderItem {
    return {
      id: this.id,
      name: this.name.value,
      productNumber: this.productNumber.value,
      quantity: this.quantity.value,
      unitPrice: this.unitPrice.value,
      unitPriceAfterDiscountAndTaxes: this.unitPriceAfterDiscountAndTaxes.value,
      unitPriceAfterDiscount: this.unitPriceAfterDiscount.value,
      image: this.image.value,
      summary: this.summary.toJson(),
      inventoryRelations: this.inventoryRelations,
      discounts: {
        applied: this.discounts.applied ? this.discounts.applied.toJson() : null,
        applicable: this.discounts.applicable.map((discount) => discount.toJson()),
      },
    };
  }

  static fromJson(json: OrderItem) {
    return new OrderItemEntity({
      id: json.id,
      name: { value: json.name, disabled: false },
      productNumber: { value: json.productNumber, disabled: false },
      quantity: { value: json.quantity, disabled: false },
      unitPrice: { value: json.unitPrice, disabled: false },
      unitPriceAfterDiscountAndTaxes: { value: json.unitPriceAfterDiscountAndTaxes, disabled: false },
      unitPriceAfterDiscount: { value: json.unitPriceAfterDiscount, disabled: false },
      image: { value: json.image, disabled: false },
      summary: OrderSummaryValueObject.fromJson(json.summary as OrderSummary)
        .setSubtotalBeforeDiscount({ hidden: true, value: 0 })
        .setShippingPrice({ hidden: true, value: 0 })
        .setTotalDiscount({ hidden: true, value: 0 }),
      inventoryRelations: json.inventoryRelations,
      discounts: {
        applied: json.discounts.applied ? OrderItemDiscountEntity.fromJson(json.discounts.applied) : null,
        applicable: json.discounts.applicable.map((discount) => OrderItemDiscountEntity.fromJson(discount)),
        loading: false,
      },
    });
  }
}
