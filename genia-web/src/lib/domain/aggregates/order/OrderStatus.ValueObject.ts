export enum OrderStatusIds{
  CLIENT_APPROVAL = 'client_approval',
  APPROVED_BY_CLIENT = 'approved_by_client',
  PENDING = 'pending',
  PROCESSING = 'processing',
  IN_TRANSIT = 'in_transit',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  IN_REVIEW = 'in_review',
}

export interface OrderStatus {
  id: OrderStatusIds;
  name: string;
}

export interface OrderStatusValueObjectParams {
  id: OrderStatusIds;
  name: string;
  color: string;
}

export class OrderStatusValueObject {
  id: OrderStatusIds;

  name: string;

  color: string;

  constructor(params: OrderStatusValueObjectParams) {
    this.id = params.id;
    this.name = params.name;
    this.color = params.color;
  }

  toJson(): OrderStatus {
    return {
      id: this.id,
      name: this.name,
    };
  }

  setId(id: string): OrderStatusValueObject {
    return new OrderStatusValueObject({
      ...this,
      id,
    });
  }

  setName(name: string): OrderStatusValueObject {
    return new OrderStatusValueObject({
      ...this,
      name,
    });
  }

  static fromJson(json: OrderStatus): OrderStatusValueObject {
    return new OrderStatusValueObject({
      id: json.id,
      name: json.name,
      color: '',
    });
  }

  static empty(): OrderStatusValueObject {
    return new OrderStatusValueObject({
      id: OrderStatusIds.PENDING,
      name: '',
      color: '',
    });
  }
}
