import { OrderInfo, OrderInfoValueObject, OrderInfoValueObjectParams } from '#domain/aggregates/order/OrderInfo.ValueObject';
import { OrderItem, OrderItemEntity } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderReceiver, OrderReceiverEntity, OrderReceiverEntityParams } from '#domain/aggregates/order/OrderReceiver.Entity';
import { OrderStatusIds } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface Order {
  id: string;
  receiver: OrderReceiver;
  orderItems: OrderItem[];
  orderInfo: OrderInfo;
  createdAt: string | null;
  updatedAt: string | null;
}

export enum OrderError {
  INVALID_DELIVERY_DATE = 'INVALID_DELIVERY_DATE',
  DUPLICATED_ITEM = 'DUPLICATED_ITEM',
  ORDER_NUMBER_REQUIRED = 'ORDER_NUMBER_REQUIRED',
  ORDER_INVALID = 'ORDER_INVALID',
}

export interface OrderEntityParams {
  id: string;
  receiver: OrderReceiverEntity;
  orderItems: OrderItemEntity[];
  info: OrderInfoValueObject;
  errors?: Record<OrderError, string>;
}

export class OrderEntity {
  id: string;

  receiver: OrderReceiverEntity;

  info: OrderInfoValueObject;

  orderItems: OrderItemEntity[];

  errors: Record<OrderError, string>;

  createdAt: string | null = null;

  updatedAt: string | null = null;

  constructor(params: OrderEntityParams) {
    this.id = params.id;
    this.receiver = params.receiver;
    this.orderItems = params.orderItems;
    this.info = params.info;
    this.errors = params.errors || {} as Record<OrderError, string>;
  }

  private validateDuplicateProductNumbers(): {items: OrderItemEntity[], areValid: boolean} {
    const seen = new Set<string>();
    let areValid = true;

    const items = this.orderItems.map((item) => {
      const productNumber = item?.productNumber?.value;
      if (!productNumber) return item;

      if (seen.has(productNumber)) {
        areValid = false;
        return item.setProductNumber({
          ...item.productNumber,
          error: OrderError.DUPLICATED_ITEM,
        });
      }

      seen.add(productNumber);
      return item.setProductNumber({
        ...item.productNumber,
        error: undefined,
      });
    });

    return {
      items,
      areValid,
    };
  }

  validateOrderItems(): {order: OrderEntity, areValid: boolean} {
    const items = this.orderItems.map((item) => item.validate());
    const areValid = items.every((item) => item.isValid());
    const order = new OrderEntity({
      ...this,
      orderItems: items,
    });

    return {
      order,
      areValid,
    };
  }

  setShippingPrice(price: number) {
    return new OrderEntity({
      ...this,
      info: this.info.setShippingPrice({
        ...this.info.shippingPrice,
        value: price,
      }),
    });
  }

  setOrderItems(items: OrderItemEntity[]) {
    let newEntity = new OrderEntity({
      ...this,
      orderItems: items,
    });

    const { order: validateOrder, areValid } = newEntity.validateOrderItems();
    const { items: validatedDuplicateItems, areValid: areDuplicateValid } = validateOrder.validateDuplicateProductNumbers();

    newEntity = validateOrder;

    if (!areValid || !areDuplicateValid) {
      newEntity = validateOrder.addError(OrderError.ORDER_INVALID, 'Invalid order');
    } else {
      newEntity = validateOrder.clearError(OrderError.ORDER_INVALID);
    }

    newEntity = new OrderEntity({
      ...newEntity,
      orderItems: validatedDuplicateItems,
    });

    return newEntity;
  }

  setInfo(info: OrderInfoValueObjectParams) {
    return new OrderEntity({
      ...this,
      info: new OrderInfoValueObject({
        ...this.info,
        ...info,
      }),
    });
  }

  setReceiver(receiver: OrderReceiverEntityParams) {
    return new OrderEntity({
      ...this,
      receiver: new OrderReceiverEntity({
        ...this.receiver,
        ...receiver,
      }),
    });
  }

  isFinished() {
    if (this.info.status.value.id === OrderStatusIds.CANCELLED || this.info.status.value.id === OrderStatusIds.COMPLETED) {
      return true;
    }

    return false;
  }

  isBlocked() {
    if (this.receiver.type === 'provider' && this.receiver.companyId && this.info.status.value.id !== OrderStatusIds.CLIENT_APPROVAL) {
      return true;
    }
    return false;
  }

  hasError(error: OrderError) {
    return Object.keys(this.errors).includes(error);
  }

  setErrors(errors: Record<OrderError, string>) {
    return new OrderEntity({
      ...this,
      errors: {
        ...errors,
      },
    });
  }

  addError(id: OrderError, error: string): OrderEntity {
    return new OrderEntity({
      ...this,
      errors: {
        ...this.errors,
        [id]: error,
      },
    });
  }

  clearError(id: OrderError) {
    const { [id]: _, ...rest } = this.errors;
    return new OrderEntity({
      ...this,
      errors: {
        ...rest,
      },
    });
  }

  isValid() {
    return Object.keys(this.errors).length === 0
      && this.orderItems.every((item) => item.isValid()
      && this.receiver.isValid());
  }

  addItem(item: OrderItemEntity) {
    const itemAlreadyExists = this.orderItems.find((i) => i.id === item.id);

    if (itemAlreadyExists) {
      this.addError(OrderError.DUPLICATED_ITEM, 'Item already exists');
    }

    const newEntity = new OrderEntity({
      ...this,
      orderItems: [...this.orderItems, item],
    });

    const { order } = newEntity.validateOrderItems();

    return order;
  }

  disableItems(disable?: boolean): OrderEntity {
    const state = !!disable;

    const items = this.orderItems.map((item) => item
      .setQuantity({ ...item.quantity, disabled: state })
      .setUnitPrice({ ...item.unitPrice, disabled: state })
      .setUnitPriceAfterDiscount({ ...item.unitPriceAfterDiscount, disabled: state })
      .setImage({ ...item.image, disabled: state })
      .setName({ ...item.name, disabled: state })
      .setProductNumber({ ...item.productNumber, disabled: state })
      .setUnitPriceAfterDiscountAndTaxes({ ...item.unitPriceAfterDiscountAndTaxes, disabled: state }));

    return new OrderEntity({
      ...this,
      orderItems: items,
    });
  }

  clean(): OrderEntity {
    return new OrderEntity({
      ...this,
      orderItems: [OrderItemEntity.empty()],
      info: OrderInfoValueObject.empty(),
    });
  }

  toJson(): Order {
    return {
      id: this.id,
      receiver: this.receiver.toJson(),
      orderItems: this.orderItems.map((item) => item.toJson()),
      orderInfo: this.info.toJson(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  static fromJson(json: Order): OrderEntity {
    return new OrderEntity({
      id: json.id,
      receiver: OrderReceiverEntity.fromJson(json.receiver),
      orderItems: json.orderItems.map((item) => OrderItemEntity.fromJson(item)),
      info: OrderInfoValueObject.fromJson(json.orderInfo),
    });
  }

  static empty(receiverType: 'client' | 'provider'): OrderEntity {
    return new OrderEntity({
      id: '',
      receiver: OrderReceiverEntity.empty(receiverType),
      orderItems: [
        OrderItemEntity.empty(),
      ],
      info: OrderInfoValueObject.empty(),
    });
  }
}
