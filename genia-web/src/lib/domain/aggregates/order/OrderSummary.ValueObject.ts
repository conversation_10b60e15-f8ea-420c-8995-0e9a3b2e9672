import { OrderTax, OrderTaxValueObject } from '#/lib/domain/aggregates/order/OrderTax.ValueObject';

export interface OrderSummaryValueObjectProperty {
  value: number;
  hidden: boolean;
}

export interface OrderSummary {
  subtotalBeforeDiscount: number;
  totalDiscount: number;
  subtotal: number;
  taxes: OrderTax[];
  total: number;
  shippingPrice: number;
}

export interface OrderSummaryValueObjectParams {
  subtotalBeforeDiscount: OrderSummaryValueObjectProperty;
  totalDiscount: OrderSummaryValueObjectProperty;
  subtotal: OrderSummaryValueObjectProperty;
  taxes: OrderTaxValueObject[];
  total: OrderSummaryValueObjectProperty;
  shippingPrice: OrderSummaryValueObjectProperty;
  loading: boolean;
}

export class OrderSummaryValueObject {
  subtotalBeforeDiscount: OrderSummaryValueObjectProperty;

  totalDiscount: OrderSummaryValueObjectProperty;

  subtotal: OrderSummaryValueObjectProperty;

  taxes: OrderTaxValueObject[];

  total: OrderSummaryValueObjectProperty;

  shippingPrice: OrderSummaryValueObjectProperty;

  loading: boolean;

  constructor(params: OrderSummaryValueObjectParams) {
    this.subtotalBeforeDiscount = params.subtotalBeforeDiscount || { value: 0, hidden: false };
    this.totalDiscount = params.totalDiscount || { value: 0, hidden: false };
    this.subtotal = params.subtotal || { value: 0, hidden: false };
    this.taxes = params.taxes || [];
    this.total = params.total || { value: 0, hidden: false };
    this.shippingPrice = params.shippingPrice || { value: 0, hidden: false };
    this.loading = params.loading;
  }

  toJson(): OrderSummary {
    return {
      subtotalBeforeDiscount: this.subtotalBeforeDiscount.value,
      totalDiscount: this.totalDiscount.value,
      subtotal: this.subtotal.value,
      taxes: this.taxes.map((tax) => tax.toJson()),
      total: this.total.value,
      shippingPrice: this.shippingPrice.value,
    };
  }

  setSubtotalBeforeDiscount(value: OrderSummaryValueObjectProperty): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      subtotalBeforeDiscount: value,
    });
  }

  setTotalDiscount(value: OrderSummaryValueObjectProperty): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      totalDiscount: value,
    });
  }

  setSubtotal(value: OrderSummaryValueObjectProperty): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      subtotal: value,
    });
  }

  setTaxes(value: OrderTaxValueObject[]): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      taxes: value,
    });
  }

  setTotal(value: OrderSummaryValueObjectProperty): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      total: value,
    });
  }

  setShippingPrice(value: OrderSummaryValueObjectProperty): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      shippingPrice: value,
    });
  }

  setLoading(value: boolean): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      ...this,
      loading: value,
    });
  }

  static fromJson(json: OrderSummary): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      subtotalBeforeDiscount: { value: json.subtotalBeforeDiscount, hidden: false },
      totalDiscount: { value: json.totalDiscount, hidden: false },
      subtotal: { value: json.subtotal, hidden: false },
      taxes: json.taxes.map((tax) => new OrderTaxValueObject(tax)),
      total: { value: json.total, hidden: false },
      shippingPrice: { value: json.shippingPrice, hidden: false },
      loading: false,
    });
  }

  static empty(): OrderSummaryValueObject {
    return new OrderSummaryValueObject({
      subtotalBeforeDiscount: { value: 0, hidden: false },
      totalDiscount: { value: 0, hidden: false },
      subtotal: { value: 0, hidden: false },
      taxes: [],
      total: { value: 0, hidden: false },
      shippingPrice: { value: 0, hidden: false },
      loading: false,
    });
  }
}
