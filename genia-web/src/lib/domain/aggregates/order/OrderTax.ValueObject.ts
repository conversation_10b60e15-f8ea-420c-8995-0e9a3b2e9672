export interface OrderTax {
  id: string;
  name: string;
  amount: number;
  value: number;
  type: string;
}

export interface OrderTaxValueObjectParams {
  id: string;
  name: string;
  amount: number;
  value: number;
  type: string;
}

export class OrderTaxValueObject {
  id: string;

  name: string;

  amount: number;

  value: number;

  type: string;

  constructor(params: OrderTaxValueObjectParams) {
    this.id = params.id;
    this.name = params.name;
    this.amount = params.amount;
    this.value = params.value;
    this.type = params.type;
  }

  toJson(): OrderTax {
    return {
      id: this.id,
      name: this.name,
      amount: this.amount,
      value: this.value,
      type: this.type,
    };
  }
}
