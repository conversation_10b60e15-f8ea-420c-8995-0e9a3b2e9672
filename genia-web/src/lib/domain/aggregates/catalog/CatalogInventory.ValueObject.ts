export interface CatalogInventory {
  inventoryId: string;
  catalogId: string;
  quantity: number;
  sku: string;
  name: string;
  image: string;
}

export interface CatalogMediaProps {
  catalogIds: string[];
}

export interface CatalogMediaItem {
  id: string;
  url: string;
  processing?: boolean;
}
export interface GetCatalogMediaResult {
  mediaByCatalogId: Record<string, CatalogMediaItem[]>;
  loading: boolean;
  error: unknown;
}
