import { IconImporter, RouteLink } from '@pitsdepot/storybook';
import { Link } from 'react-router-dom';

export function PlusRoundedButton({ to }: {to: string}) {
  return (
    <RouteLink
      as={Link}
      to={to}
    >
      <IconImporter
        size={24}
        name='plus'
        className="text-dark-500 hover:text-dark-700 cursor-pointer transition-all ease-in-out border border-dark-400 rounded-full p-1"
      />
    </RouteLink>
  );
}
