import { CartCard } from '@pitsdepot/storybook';
import { useCallback, useState } from 'react';

import noImage from '#/assets/no-image.png';
import { BottomPriceChild } from '#appComponent/common/BottomPriceChild.Component';
import { AttributesCell, CategoryProps } from '#appComponent/table/TableCells.Component';
import { StoreItem } from '#application/store/Store.Type';

interface StoreCardProps {
  item: StoreItem;
  className?: string;
  actionButton: React.ReactNode;
}

const StoreCard = ({ item, className, actionButton }: StoreCardProps) => {
  const {
    id, name, price, appliedDiscount, applicableDiscounts, media, attributes, readId,
  } = item;

  const [imgUrl, setImgUrl] = useState<string>(media?.[0]?.url || noImage);

  const handleImageError = useCallback(() => {
    setImgUrl(noImage);
  }, []);

  const priceToShow = appliedDiscount.catalogDiscount?.priceAfterDiscount || appliedDiscount.storeDiscount?.priceAfterDiscount || price;
  const originalPriceToShow = appliedDiscount.catalogDiscount?.priceAfterDiscount || appliedDiscount.storeDiscount?.priceAfterDiscount ? price : undefined;
  const bottomPriceChild = <BottomPriceChild applicableDiscounts={applicableDiscounts} />;

  const renderAttributes = attributes?.length === 0 ? undefined : AttributesCell(attributes ? attributes as unknown as CategoryProps[] : []);

  const topPriceChild = (
    <div className='flex flex-col gap-2'>
      <div className='text-base text-dark-600 font-medium'>{readId}</div>
      {renderAttributes}
    </div>
  );
  return (
    <CartCard
      id={id}
      title={name}
      redirectTo={{ to: '/', as: 'link' }}
      price={priceToShow}
      originalPrice={originalPriceToShow}
      appliedDiscountType={appliedDiscount.catalogDiscount?.discountType || appliedDiscount.storeDiscount?.discountType}
      appliedDiscount={appliedDiscount.catalogDiscount?.discountValue || appliedDiscount.storeDiscount?.discountValue}
      imageUrl={imgUrl}
      onImageError={handleImageError}
      topPriceChild={topPriceChild}
      bottomPriceChild={bottomPriceChild}
      className={className}
      actionButton={actionButton}
    />
  );
};

export default StoreCard;
