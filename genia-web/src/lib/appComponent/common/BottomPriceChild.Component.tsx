import {
  DiscountBlockWrapper,
  SortAndMergeDiscounts,
} from '@pitsdepot/storybook';
import { useCallback } from 'react';

import { ApplicableDiscounts } from '#application/store/Store.Type';

export function BottomPriceChild({
  applicableDiscounts,
}: {
  applicableDiscounts: ApplicableDiscounts
}) {
  const finalApplicableDiscounts = useCallback(() => (
    SortAndMergeDiscounts(applicableDiscounts)
  ), [applicableDiscounts])();

  return (
    <DiscountBlockWrapper discounts={finalApplicableDiscounts} defaultApplied />
  );
}
