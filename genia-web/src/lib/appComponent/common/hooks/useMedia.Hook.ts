import { UseUploadMedia } from '#application/common/hooks/UseUploadMedia.Hook';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';

import { useMediaToRemove } from '../media/MediaToRemove.Context';
import { Notification } from '../Notification.Component';

interface UseMediaProps {
  entity: 'catalog' | 'inventory';
  entityId: string;
  images?: (File | { id: string; url: string; name: string })[];
  token: string;
}

export const useMedia = () => {
  const { imagesToRemove, clearImagesToRemove } = useMediaToRemove();

  const handleMediaOperations = async ({
    entity,
    entityId,
    images,
    token,
  }: UseMediaProps) => {
    try {
      if (imagesToRemove.length > 0 && entityId) {
        try {
          await ApplicationRegistry.MediaService.deleteMedia(
            imagesToRemove,
            entity,
            entityId,
            token,
          );

          clearImagesToRemove();
        } catch (error) {
          Notification({ message: error as string, type: MSG_ERROR_TYPES.ERROR });
        }
      }

      const filesToUpload = images?.filter((img): img is File => img instanceof File) || [];

      if (filesToUpload.length > 0) {
        const getSignedUrls = await ApplicationRegistry.MediaService.getSignedUrlsInParallel({
          filesToUpload,
          entity,
          entityId,
          token,
          kind: 'media',
          getSignedUrlEndpoint: ApplicationRegistry.PathService.signedUrl.base(),
        });

        const urlsArray = getSignedUrls.map((result) => ({ url: result.data?.fullGcsUri }));

        await ApplicationRegistry.MediaService.assignMediaToEntity(
          entity,
          entityId,
          urlsArray,
          token,
          ApplicationRegistry.PathService.media.base(),
        );

        UseUploadMedia({
          images: images || [],
          entity,
          entityId,
          token,
          kind: 'media',
          signedUrlResults: getSignedUrls,
        });
      }

      return true;
    } catch (error) {
      Notification({ message: error as string, type: MSG_ERROR_TYPES.ERROR });
      return false;
    }
  };

  return {
    handleMediaOperations,
  };
};
