import { useEffect, useState } from 'react';

type UseDebounceOptions = {
  delay?: number;
  onDebounce?: () => void;
};

export function useDebounce<T>(value: T, options: UseDebounceOptions = {}) {
  const { delay = 1000, onDebounce } = options;
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const [isDebouncing, setIsDebouncing] = useState(false);

  useEffect(() => {
    setIsDebouncing(true);
    const timer = setTimeout(() => {
      setDebouncedValue(value);
      setIsDebouncing(false);
      onDebounce?.();
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay, onDebounce]);

  return { debouncedValue, isDebouncing };
}

export function useDebounceCallback<T extends unknown[]>(
  callback: (...args: T) => void | Promise<void>,
  dependencies: unknown[],
  delay = 1000,
) {
  const [debounceId, setDebounceId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (debounceId !== null) {
      window.clearTimeout(debounceId);
    }

    const timeoutId = window.setTimeout(async () => {
      setIsLoading(true);
      try {
        await callback(...([] as unknown as T));
      } finally {
        setIsLoading(false);
        setDebounceId(null);
      }
    }, delay);

    setDebounceId(timeoutId);

    return () => {
      if (debounceId !== null) {
        window.clearTimeout(debounceId);
      }
    };
  }, [...dependencies]);

  return { isLoading };
}
