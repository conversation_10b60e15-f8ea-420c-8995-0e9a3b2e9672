import {
  useCallback, useEffect, useMemo, useState,
} from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { DropdownSearchProps, DropDownSearchResponse } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';

import { usePagination } from '../../../infrastructure/implementation/application/hooks/usePagination.Hook';

export function useDropdownSearch<T>({
  itemsPerPage,
  getItems,
  selectedItemIds = [],
  getId,
  minSearchLength = 1,
}: DropdownSearchProps<T>): DropDownSearchResponse<T> {
  const [accumulatedItems, setAccumulatedItems] = useState<T[]>([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const {
    currentPage, offset, handlePageChange,
  } = usePagination(itemsPerPage);

  const { items, totalNumberOfItems } = getItems({
    limit: itemsPerPage,
    offset,
    orderBy: 'name',
    order: 'asc',
    searchTerm: searchValue,
  });

  useEffect(() => {
    if (!items) return;

    if (offset === 0 || searchValue !== '') {
      setAccumulatedItems(items);
      setIsLoading(false);
      return;
    }

    if (items.length === 0) {
      setIsLoading(false);
      return;
    }

    setAccumulatedItems((prev) => [...prev, ...items]);
    setIsLoading(false);
  }, [items, offset, searchValue]);

  useEffect(() => {
    if (searchValue) {
      handlePageChange(1);
    }
  }, [searchValue, handlePageChange]);

  const loadMoreOptions = useCallback(() => {
    if (isLoading) return;
    if (!items || accumulatedItems.length >= (totalNumberOfItems || 0) || items.length === 0) return;

    setIsLoading(true);
    handlePageChange(currentPage + 1);
  }, [isLoading, items, accumulatedItems.length, totalNumberOfItems, handlePageChange, currentPage]);

  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value);
    if (value.trim() === '') {
      setSearchValue('');
    }
  }, []);

  const handleDropdownToggle = useCallback((isOpen: boolean) => {
    if (!isOpen) {
      setSearchInput('');
      setSearchValue('');
      handlePageChange(1);
    }
  }, [handlePageChange]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const trimmedInput = searchInput.trim();
      if (trimmedInput.length < minSearchLength) {
        Notification({ message: 'Too short', type: MSG_ERROR_TYPES.ERROR });
        return;
      }
      setSearchValue(trimmedInput);
    }
  }, [searchInput, minSearchLength]);

  const filteredOptions = useMemo(() => accumulatedItems.filter(
    (item) => !selectedItemIds.includes(getId(item)),
  ), [accumulatedItems, selectedItemIds, getId]);

  return {
    searchInput,
    setSearchInput,
    searchValue,
    setSearchValue,
    isLoading,
    filteredOptions,
    totalNumberOfItems,
    loadMoreOptions,
    handleInputChange,
    handleDropdownToggle,
    handleKeyDown,
    resetSearch: () => {
      setSearchInput('');
      setSearchValue('');
      handlePageChange(1);
    },
  };
}
