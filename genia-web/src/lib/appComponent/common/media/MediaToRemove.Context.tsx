import React, {
  createContext,
  ReactNode,
  useContext, useState,
} from 'react';

interface MediaToRemoveContextType {
  imagesToRemove: string[];
  addImageToRemove: (id: string) => void;
  clearImagesToRemove: () => void;
}

const MediaToRemoveContext = createContext<MediaToRemoveContextType | undefined>(undefined);

export const MediaToRemoveProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [imagesToRemove, setImagesToRemove] = useState<string[]>([]);

  const addImageToRemove = (id: string) => {
    setImagesToRemove((prev) => {
      if (prev.includes(id)) return prev;
      return [...prev, id];
    });
  };

  const clearImagesToRemove = () => {
    setImagesToRemove([]);
  };

  return (
    <MediaToRemoveContext.Provider
      value={{
        imagesToRemove,
        addImageToRemove,
        clearImagesToRemove,
      }}
    >
      {children}
    </MediaToRemoveContext.Provider>
  );
};

export const useMediaToRemove = (): MediaToRemoveContextType => {
  const context = useContext(MediaToRemoveContext);
  if (context === undefined) {
    throw new Error('useMediaToRemove must be used within a MediaToRemoveProvider');
  }
  return context;
};
