import React from 'react';

import ApplicationRegistry from '#composition/Application.Registry';

interface UserProfileProps {
  showEmail?: boolean;
  showRole?: boolean;
  showCompanyId?: boolean;
  className?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  showEmail = false,
  showRole = false,
  showCompanyId = false,
  className = '',
}) => {
  const { user, loading, error } = ApplicationRegistry.UsersService.useGetCurrentUser();

  if (loading) {
    return <div className={`animate-pulse ${className}`}>Cargando información del usuario...</div>;
  }

  if (error) {
    return <div className={`text-red-500 ${className}`}>Error al cargar información del usuario</div>;
  }

  if (!user) {
    return <div className={`text-gray-500 ${className}`}>Usuario no encontrado</div>;
  }

  return (
    <div className={`user-profile ${className}`}>
      <div className="flex items-center gap-3">
        {user.picture && (
          <img
            src={user.picture}
            alt={`${user.name} profile`}
            className="w-10 h-10 rounded-full object-cover"
          />
        )}
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">{user.name}</span>
          {showEmail && user.email && (
            <span className="text-sm text-gray-600">{user.email}</span>
          )}
          {showRole && user.role && (
            <span className="text-xs text-blue-600 font-medium">{user.role}</span>
          )}
          {showCompanyId && user.companyId && (
            <span className="text-xs text-gray-500">Company: {user.companyId}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
