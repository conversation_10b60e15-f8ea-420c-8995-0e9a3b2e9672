import React from 'react';

import { TypeProps } from '../../application/deprecated/DashboardPages.Type';

interface HierarchySelectorProps {
  show: boolean;
  options: {
    type: string;
    label: string;
    testId: string;
  }[];
  onSelect: (type: TypeProps) => void;
}

const HierarchySelector: React.FC<HierarchySelectorProps> = ({
  show,
  options,
  onSelect,
}) => (
  <>
    {show && (
      <div className="pd-flex pd-flex-col pd-border pd-p-4 pd-rounded-lg pd-gap-4 pd-w-full">
        {options.map((option) => (
          <div
            key={option.type}
            data-testid={option.testId}
            className="pd-cursor-pointer hover:pd-text-dark-500"
            onClick={() => onSelect(option.type as TypeProps)}
          >
            {option.label}
          </div>
        ))}
      </div>
    )}
  </>
);

export default HierarchySelector;
