import { <PERSON><PERSON><PERSON><PERSON>, FormErrorComponent, IconImporter } from '@pitsdepot/storybook';
import React, {
  useCallback,
  useEffect, useRef, useState,
} from 'react';

import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';

const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => new Promise((resolve, reject) => {
  const img = new Image();
  img.onload = () => {
    resolve({
      width: img.width,
      height: img.height,
    });
    URL.revokeObjectURL(img.src);
  };
  img.onerror = () => {
    reject(new Error('Failed to load image'));
    URL.revokeObjectURL(img.src);
  };
  img.src = URL.createObjectURL(file);
});

interface ImageData {
  id: string;
  url: string;
  name: string;
  processing?: boolean;
}

interface InternalImageData {
  id: string;
  file?: File;
  previewUrl: string;
  name: string;
  processing?: boolean;
  markedForRemoval?: boolean;
}

interface MediaAppComponentProps {
  onChange: (images: (File | ImageData)[]) => void;
  initialImages?: ImageData[];
  maxFiles?: number;
  maxMBFileSize?: number;
  minImageDimensions?: { width: number; height: number };
  banner?: {
    title: string;
    description: string;
  };
  addImage?: boolean;
  showRemoveButton?: boolean;
  onImageRemove?: (id: string) => Promise<void> | void;
}

const SCROLL_AMOUNT = 104;

const MediaAppComponent: React.FC<MediaAppComponentProps> = ({
  onChange,
  initialImages = [],
  maxFiles = 15,
  maxMBFileSize = 2,
  minImageDimensions = { width: 500, height: 500 },
  banner,
  addImage = true,
  showRemoveButton = true,
  onImageRemove,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [images, setImages] = useState<InternalImageData[]>([]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { media } = TextService.getText();
  const maxSizeinBytes = maxMBFileSize ? maxMBFileSize * 1024 * 1024 : 0;

  useEffect(() => {
    if (!initialImages || initialImages.length === 0) {
      setImages([]);
      return;
    }
    const initialInternalImages = initialImages.map((img) => ({
      id: img.id,
      previewUrl: img.url,
      name: img.name,
      processing: img.processing || false,
    }));
    setImages(initialInternalImages);
  }, [initialImages]);

  useEffect(() => {
    const outputImages = images.map((img) => (img.file ? img.file : { id: img.id, url: img.previewUrl, name: img.name }));
    onChange(outputImages);
  }, [images, onChange]);

  const updateScrollButtonVisibility = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      setCanScrollLeft(scrollLeft > 5);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 5);
    }
  }, []);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      updateScrollButtonVisibility();

      const resizeObserver = new ResizeObserver(updateScrollButtonVisibility);
      resizeObserver.observe(container);

      return () => {
        if (container) {
          resizeObserver.unobserve(container);
        }
        resizeObserver.disconnect();
      };
    }
    return undefined;
  }, [images, updateScrollButtonVisibility]);

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const files = Array.from(e.target.files || []);

    if (files.length === 0) return;

    const hasLimitError = files.length + images.length > maxFiles;
    if (hasLimitError) {
      setError(`No puedes subir más de ${maxFiles} imágenes`);
      return;
    }

    const hasFileSizeError = files.some((file) => file.size > maxSizeinBytes);
    if (hasFileSizeError) {
      setError(`El tamaño máximo permitido es ${maxMBFileSize} MB`);
      return;
    }

    const imageFiles = files.filter((file) => file.type.startsWith('image/'));

    const dimensionChecks = imageFiles.map(async (file) => {
      try {
        const dimensions = await getImageDimensions(file);
        return dimensions.width >= minImageDimensions.width && dimensions.height >= minImageDimensions.height;
      } catch (err) {
        return false;
      }
    });

    const dimensionResults = await Promise.all(dimensionChecks);
    const hasFileDimensionError = dimensionResults.some((result) => !result);

    if (hasFileDimensionError) {
      setError(`Las imágenes deben tener un tamaño mínimo de ${minImageDimensions.width}x${minImageDimensions.height} píxeles`);
      return;
    }

    try {
      setImages((prevImages) => {
        const imagesLength = prevImages.length;
        const newInternalImages = imageFiles
          .slice(0, maxFiles - imagesLength)
          .map((file) => {
            const url = URL.createObjectURL(file);
            return {
              id: crypto.randomUUID(),
              file,
              previewUrl: url,
              name: file.name,
            };
          });
        return [...prevImages, ...newInternalImages];
      });
    } catch (processError) {
      setError(media.processingImageError);
    }
  }, [maxFiles, maxMBFileSize, maxSizeinBytes, images.length, minImageDimensions]);

  const removeImage = useCallback(async (id: string) => {
    try {
      const isInDB = initialImages.some((img) => img.id === id);

      if (isInDB) {
        if (onImageRemove) {
          await onImageRemove(id);
        }
        setImages((prevImages) => prevImages.map((img) => (img.id === id ? { ...img, markedForRemoval: true } : img)));
      } else {
        setImages((prevImages) => prevImages.filter((img) => {
          if (img.id === id && img.file) {
            URL.revokeObjectURL(img.previewUrl);
          }
          return img.id !== id;
        }));
      }
    } catch (err) {
      Notification({ message: 'Error eliminando la imagen', type: MSG_ERROR_TYPES.ERROR });
    }
    setError(null);
  }, [initialImages, onImageRemove]);

  const handleScroll = useCallback((direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (container) {
      const scrollValue = direction === 'left' ? -SCROLL_AMOUNT : SCROLL_AMOUNT;
      container.scrollBy({ left: scrollValue, behavior: 'smooth' });
    }
  }, []);

  const renderRemoveButton = useCallback((img: InternalImageData) => {
    if (showRemoveButton) {
      return (
        <button
          onClick={(e) => {
            e.preventDefault();
            removeImage(img.id);
          }}
          className={`
            absolute top-0.5 right-0.5 bg-black bg-opacity-60 text-white border-none rounded-full w-5 h-5 text-sm 
            leading-none cursor-pointer flex items-center justify-center
          `}
        >
          <IconImporter name='x' className="w-3 h-3" />
        </button>
      );
    }
    return null;
  }, [showRemoveButton, removeImage]);

  return (
    <div className="flex flex-col gap-4 flex-1 w-full">
      {banner && (
        <div className="bg-primary/10 text-gray-700 rounded-lg p-4 text-sm flex flex-col gap-2">
          <strong>{banner.title}</strong>
          <div>{banner.description}</div>
        </div>
      )}

      <div className="flex items-center gap-2 w-full">
        {addImage && <div
          className={`
            w-24 h-24 border-2 border-dashed border-line rounded-lg flex items-center justify-center text-xs cursor-pointer 
            flex-shrink-0 hover:text-toscaBlue hover:border-toscaBlue animated-all ease-in-out duration-200
          `}
          onClick={() => inputRef.current?.click()}
        >
          <span>{media.select}</span>
          <input
            type="file"
            multiple
            accept="image/*"
            ref={inputRef}
            className="hidden"
            onChange={handleFileSelect}
          />
        </div>}

        <div className="flex-1 relative overflow-hidden">
          {canScrollLeft && (
            <button
              type="button"
              onClick={() => handleScroll('left')}
              className={`
                absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white bg-opacity-75 hover:bg-opacity-100 p-1 rounded-full shadow-lg cursor-pointer border border-line
              `}
              aria-label="Scroll left"
            >
              <IconImporter name="caretLeft" className="w-5 h-5 text-dark-500" />
            </button>
          )}
          <div
            ref={scrollContainerRef}
            className="flex gap-2 pb-0.5 overflow-x-auto media-component-hide-scrollbar"
            onScroll={updateScrollButtonVisibility}
          >
            {images.map((img, index) => (
              <div
                key={img.id}
                className={`relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0 transition-opacity duration-300 
                  ${img.markedForRemoval ? 'opacity-40' : ''}`}
              >
                { img.processing ? (
                  <div className='bg-white w-full h-full flex flex-col gap-2 items-center justify-center'>
                    <CircleLoader size='md' className='!w-8 !h-8'/>
                    <div className='text-xxsm text-dark-500'>{media.processing}</div>
                    {renderRemoveButton(img)}
                  </div>
                ) : (
                  <>
                    <img src={img.previewUrl} alt={img.name} className="w-full h-full object-cover" />
                    {renderRemoveButton(img)}
                    {index === 0 && !img.markedForRemoval && (
                    <div className='bg-primary opacity-90 text-gray-700 text-xsm px-3 py-1 rounded-xl absolute bottom-1 left-1/2 transform -translate-x-1/2'>
                      {media.cover}
                    </div>
                    )}
                  </>
                )}

                {img.markedForRemoval && (
                  <div className="absolute inset-0 flex items-center justify-center text-center pointer-events-none">
                    <span className="text-black font-bold text-xxsm p-1">
                      {media.remove}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
          {canScrollRight && (
            <button
              type="button"
              onClick={() => handleScroll('right')}
              className={`
                absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white bg-opacity-75 hover:bg-opacity-100 p-1 rounded-full shadow-lg cursor-pointer border border-line
              `}
              aria-label="Scroll right"
            >
              <IconImporter name="caretRight" className="w-5 h-5 text-dark-500" />
            </button>
          )}
        </div>
      </div>
      { error && (
        <FormErrorComponent>
          {error}
        </FormErrorComponent>
      )}
    </div>
  );
};

export default MediaAppComponent;
