import {
  Avatar,
  DropdownWithSearch,
  IconImporter,
  OptionsDropdownProps,
} from '@pitsdepot/storybook';
import {
  memo,
  useEffect,
  useState,
} from 'react';

import { useDebounceCallback } from '#appComponent/common/hooks/useDebounce.Hook';
import TextService from '#composition/textService/Text.Service';
import { getAvatarStyles } from '#infrastructure/implementation/application/utils/avatarColorsCombination';

type DropdownPosition = 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

export type DropDownSearchAppComponentOption<T = {id: string, name: string, image?: string}> = T & {
  id: string;
  name: string;
  image?: string;
};

export type DropDownSearchFetch = ((params: { searchText: string; page: number }) => {
  options: DropDownSearchAppComponentOption[];
  refetch: () => void;
  isLoading: boolean;
});

export const DefaultRenderer = (value: DropDownSearchAppComponentOption) => {
  const { name, image } = value;

  const avatarBgColor = `bg-${getAvatarStyles(name).backgroundColor}-500`;
  const avatarTextColor = getAvatarStyles(name).textColor;

  return (
    <div className="flex items-center">
      <div className={`h-8 w-8 ${avatarBgColor} ${avatarTextColor} rounded-full flex items-center justify-center text-lg m-2`}>

        <Avatar
          src={image}
          name={name.substring(0, 10)}
          size={32}
          className={`${getAvatarStyles(name).textColor}`}
          initialsBackground={getAvatarStyles(name).backgroundColor}
        />
      </div>
      <div className='flex flex-col'>
        <div className='text-xsm line-clamp-1'>{name}</div>
      </div>
    </div>
  );
};

export interface DropDownSearchAppComponentProps<T extends DropDownSearchAppComponentOption = DropDownSearchAppComponentOption> {
  selected?: T | null;
  onSelect?: (option: T) => void;
  useFetch: DropDownSearchFetch;
  disabled?: boolean;
  className?: string;
  renderer?: (value: T) => JSX.Element;
  position?: DropdownPosition;
}

function PureDropDownSearchAppComponent<T extends DropDownSearchAppComponentOption = DropDownSearchAppComponentOption>(props: DropDownSearchAppComponentProps<T>) {
  const {
    selected,
    onSelect,
    useFetch,
    disabled,
    className,
    renderer,
    position,
  } = props;

  const text = TextService.getText();

  const [currentOptions, setCurrentOptions] = useState<DropDownSearchAppComponentOption[]>([]);
  const [LastOptionsLength, setLastOptionsLength] = useState<number>(0);
  const [debouncedValue, setDebouncedValue] = useState<string>('');
  const [inputValue, setInputValue] = useState('');
  const [page, setPage] = useState(1);
  const [currentSelectedOption, setCurrentSelectedOption] = useState<DropDownSearchAppComponentOption | null>(selected || null);

  const { options, refetch } = useFetch({ searchText: debouncedValue, page });

  useEffect(() => {
    const filteredOption = options.filter((option) => !(option.id === (currentSelectedOption?.id || '')));

    const newList = filteredOption.map((option) => ({
      ...option,
      renderer: renderer || DefaultRenderer,
    }));

    if (page > 1 && newList.length > 0) {
      const concatenatedList = [...currentOptions, ...newList];

      setLastOptionsLength(concatenatedList.length);
      return setCurrentOptions(concatenatedList);
    }

    if (page > 1 && newList.length === 0) {
      return setLastOptionsLength(0);
    }

    setLastOptionsLength(newList.length);
    return setCurrentOptions(newList);
  }, [debouncedValue, JSON.stringify(options)]);

  const handleOptionSelect = (option: OptionsDropdownProps) => {
    const selectedProductFromList = currentOptions.find((p) => p.id === option.id);
    if (selectedProductFromList) {
      setCurrentSelectedOption(selectedProductFromList);
      onSelect?.(selectedProductFromList as T);
    } else {
      const newSelectedProduct = option;
      onSelect?.(newSelectedProduct as T);
    }
  };

  const handleSearch = (searchText: string) => {
    setCurrentOptions([]);
    setInputValue?.(searchText);
  };

  const handleDropdownToggle = (isOpen: boolean) => {
    if (!isOpen) {
      setInputValue?.('');
    } else {
      setPage(1);
      refetch();
    }
  };

  const handleLoadMore = () => {
    if (page > 10) return;
    setPage((prevPage) => {
      if (LastOptionsLength === 0) return prevPage;
      return prevPage + 1;
    });
  };

  useDebounceCallback(
    () => setDebouncedValue(inputValue),
    [inputValue],
    400,
  );

  return (
    <div className={`relative catalog-dropdown   ${className}`}>
      <DropdownWithSearch
        options={currentOptions}
        setSelectedOption={handleOptionSelect}
        itemsSelected={currentSelectedOption ? [currentSelectedOption] : []}
        disabled={disabled}
        onSearchChange={handleSearch}
        showAvatar={false}
        onToggle={handleDropdownToggle}
        loadMoreOptions={handleLoadMore}
        position={position}
      >
        <div className={`drop-down-search-display
            px-2 py-[6px] rounded-xl relative z-500 border h-[38px] flex items-center px-[12px]
            ${disabled ? 'bg-lightGray cursor-not-allowed pd-border-neutral-300' : 'bg-white'}
          `}>
          <div className='flex justify-between items-center w-full'>
            <div className={'flex flex-row items-center gap-2 truncate max-w-[85%]'}>
              {currentSelectedOption?.id && <Avatar
                src={currentSelectedOption.image || ''}
                name={currentSelectedOption.name}
                size={28}
                className={`${disabled ? 'pd-opacity-60' : ''} min-w-[28px] min-h-[28px] w-[28px] h-[28px] shrink-0`}
              />}

              <span
                className={currentSelectedOption ? `${disabled ? 'pd-text-neutral-500' : 'text-black'} truncate w-full` : 'text-gray-400 truncate w-full'}
              >
                {currentSelectedOption?.name || text.common.select}
              </span>
            </div>
            {!disabled && <div className="flex items-center">
              <IconImporter
                size={16}
                name='caretDown'
                className={'pd-transition-all pd-ease-in-out pd-text-dark-400 hover:pd-text-dark-700'}
              />
            </div> }

          </div>
        </div>
      </DropdownWithSearch>
    </div>
  );
}

export const DropDownSearchAppComponent = memo(PureDropDownSearchAppComponent, (prevProps, nextProps) => (
  prevProps.selected?.id === nextProps.selected?.id
    && prevProps.disabled === nextProps.disabled
    && prevProps.className === nextProps.className
    && prevProps.renderer === nextProps.renderer
    && prevProps.useFetch === nextProps.useFetch
    && prevProps.onSelect === nextProps.onSelect
    && prevProps.position === nextProps.position
)) as typeof PureDropDownSearchAppComponent;
