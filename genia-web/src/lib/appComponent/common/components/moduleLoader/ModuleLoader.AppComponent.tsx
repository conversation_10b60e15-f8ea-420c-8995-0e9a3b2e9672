import { ThreeDotsLoader } from '@pitsdepot/storybook';

import TextService from '#composition/textService/Text.Service';

export function ModuleLoaderAppComponent() {
  return (
    <div className='flex flex-col gap-4 items-center justify-center w-full min-h-screen'>
      <p className='text-sm font-semibold'>{TextService.getText().common.loading}</p>
      <ThreeDotsLoader size='lg' />
    </div>
  );
}
