import { IconImporter, RouteLink } from '@pitsdepot/storybook';
import { useCallback, useEffect, useState } from 'react';

import noImage from '#/assets/no-image.png';
import ApplicationRegistry from '#composition/Application.Registry';

export interface RelatedItem {
  id: string;
  name?: string;
  sku?: string | null;
  quantity?: number;
  url?: string;
  onRemoveItem?: () => void;
}
export interface SelectedItemProps {
  item: RelatedItem;
  disabled?: boolean;
  onQuantityChange: (id: string, newQuantity: number) => void;
}

export function ControlledSelectedItem(props: SelectedItemProps) {
  const { item, disabled, onQuantityChange } = props;
  const {
    name, sku, quantity, url, onRemoveItem, id,
  } = item;
  const [inputValue, setInputValue] = useState(quantity?.toString() || '1');
  const [imgUrl, setImgUrl] = useState<string>(url || noImage);
  const [isInvalid, setIsInvalid] = useState(false);

  useEffect(() => {
    setInputValue(quantity?.toString() || '1');
  }, [quantity]);

  const handleImageError = useCallback(() => {
    setImgUrl(noImage);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (/^\d*$/.test(value)) {
      setInputValue(value);
      setIsInvalid(false);
      const num = Number(value);
      if (num > 100 || num < 1) {
        setIsInvalid(true);
      } else if (!Number.isNaN(num) && num >= 1 && num <= 100) {
        onQuantityChange(id, num);
      }
    } else {
      setIsInvalid(true);
    }
  };

  const onIncrement = () => {
    const num = Number(inputValue) || 1;
    if (num < 100) {
      const newQuantity = num + 1;
      setInputValue(newQuantity.toString());
      onQuantityChange(id, newQuantity);
      setIsInvalid(false);
    }
  };

  const onDecrement = () => {
    const num = Number(inputValue) || 1;
    if (num > 1) {
      const newQuantity = num - 1;
      setInputValue(newQuantity.toString());
      onQuantityChange(id, newQuantity);
      setIsInvalid(false);
    }
  };

  const listInventoryPath = ApplicationRegistry.PathService.inventory.base();

  return (
    <div className='flex gap-2 items-center' >
      <div className="relative w-12 h-12 rounded overflow-hidden flex-shrink-0">
        <img
          src={imgUrl}
          className='object-cover h-full w-auto'
          onError={handleImageError}
        />
      </div>
      <div className='flex flex-col gap-1 flex-1'>
        <RouteLink target='_blank' to={`${listInventoryPath}/${id}`} className='!text-xsm !line-clamp-1 !font-medium !text-dark-600' title={name}>{name}</RouteLink>
        <div className='text-xxsm line-clamp-1'>{sku}</div>
      </div>
      <div className="flex items-center gap-2 flex-1">
        <button
          // eslint-disable-next-line max-len
          className="h-full w-auto p-1 rounded-full hover:cursor-pointer disabled:cursor-not-allowed disabled:bg-transparent flex items-center justify-center hover:bg-white transition-border duration-300 ease-in-out"
          onClick={onDecrement}
          disabled={Number(inputValue) <= 1 || disabled}
          aria-label="Decrease quantity"
        >
          <IconImporter name='minus' size={16} />
        </button>
        <input
          value={inputValue}
          onChange={handleChange}
          className={`text-center border rounded-md w-full text-sm py-1 disabled:cursor-not-allowed ${isInvalid && 'focus-visible:outline-negative'}`}
          max={100}
          disabled={disabled}
        />
        <button
          // eslint-disable-next-line max-len
          className="h-full w-auto p-1 rounded-full hover:cursor-pointer flex items-center justify-center hover:bg-white transition-border duration-300 ease-in-out disabled:cursor-not-allowed disabled:bg-transparent"
          onClick={onIncrement}
          aria-label="Increase quantity"
          disabled={disabled}
        >
          <IconImporter name='plus' size={16} />
        </button>
      </div>
      {!disabled && <IconImporter name='x' className='cursor-pointer' onClick={onRemoveItem} />}
    </div>
  );
}
