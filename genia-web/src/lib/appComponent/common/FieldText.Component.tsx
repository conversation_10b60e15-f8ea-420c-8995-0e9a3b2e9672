import { IconImporter, Tooltip, TooltipProps } from '@pitsdepot/storybook';

interface FieldTextProps {
  label: string;
  value: string | number;
  tooltip?: TooltipProps;
}

function FieldText({ label, value = 0, tooltip }: FieldTextProps) {
  return (
    <div className='flex gap-2'>
      <div className='text-xsm text-dark-500 flex'>
        {label}
        <Tooltip
          content={tooltip?.content}
        >
          <IconImporter
            size={16}
            name='info'
            className='ml-1'
          />
        </Tooltip>

      </div>
      <p className='text-xsm'>{ value }</p>
    </div>
  );
}

export default FieldText;
