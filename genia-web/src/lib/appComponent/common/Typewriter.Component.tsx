import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface TypewriterProps {
  fixedText?: string;
  rotatingWords?: string[];
  text?: string; // Para compatibilidad con uso anterior
}

const Typewriter = ({ fixedText, rotatingWords, text }: TypewriterProps) => {
  const [animationState, setAnimationState] = useState<'typing' | 'showing' | 'erasing'>('typing');
  const [visibleChars, setVisibleChars] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // Si se usan las nuevas props, usar esas; sino usar el texto original
  const isRotatingMode = fixedText && rotatingWords && rotatingWords.length > 0;
  const currentWord = isRotatingMode ? rotatingWords[currentWordIndex] : '';
  const textToAnimate = isRotatingMode ? currentWord : (text || '');

  useEffect(() => {
    if (!isRotatingMode) {
      // Modo normal para texto estatico
      const animateText = () => {
        setVisibleChars(0);
        setAnimationState('typing');

        const typingInterval = setInterval(() => {
          setVisibleChars((prev) => {
            if (prev >= (text || '').length) {
              clearInterval(typingInterval);
              setTimeout(() => {
                setAnimationState('showing');
                setTimeout(() => {
                  setAnimationState('erasing');
                  const erasingInterval = setInterval(() => {
                    // eslint-disable-next-line @typescript-eslint/no-shadow
                    setVisibleChars((prev) => {
                      if (prev <= 0) {
                        clearInterval(erasingInterval);
                        setTimeout(() => animateText(), 200);
                        return 0;
                      }
                      return prev - 1;
                    });
                  }, 150);
                }, 5000);
              }, 100);
              return prev;
            }
            return prev + 1;
          });
        }, 200);
      };

      setTimeout(() => animateText(), 100);
      return;
    }

    // Modo rotativo - logica simplificada
    const runCycle = () => {
      // 1. Escribir
      setVisibleChars(0);
      setAnimationState('typing');

      let charIndex = 0;
      const typeChar = () => {
        if (charIndex < textToAnimate.length) {
          setVisibleChars(charIndex + 1);
          // eslint-disable-next-line no-plusplus
          charIndex++;
          setTimeout(typeChar, 200);
        } else {
          // 2. Mostrar
          setAnimationState('showing');
          setTimeout(() => {
            // 3. Borrar
            setAnimationState('erasing');
            let eraseIndex = textToAnimate.length;
            const eraseChar = () => {
              if (eraseIndex > 0) {
                setVisibleChars(eraseIndex - 1);
                // eslint-disable-next-line no-plusplus
                eraseIndex--;
                setTimeout(eraseChar, 150);
              } else {
                // 4. Siguiente palabra
                setTimeout(() => {
                  setCurrentWordIndex((prev) => (prev + 1) % rotatingWords!.length);
                }, 200);
              }
            };
            eraseChar();
          }, 3000);
        }
      };

      setTimeout(typeChar, 100);
    };

    runCycle();
  }, [currentWordIndex, isRotatingMode, text, textToAnimate, rotatingWords]);

  const containerVariants = {
    typing: {
      transition: {
        staggerChildren: 0.04,
        delayChildren: 0.1,
      },
    },
    showing: {},
    erasing: {
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1,
      },
    },
  };

  const letterVariants = {
    typing: {
      opacity: 1,
      transition: { duration: 0.1, ease: 'easeOut' as const },
    },
    showing: { opacity: 1 },
    erasing: {
      opacity: 0,
      transition: { duration: 0.05, ease: 'easeIn' as const },
    },
  };

  return (
    <div className="flex flex-col items-center">
      {/* Texto fijo (si existe) */}
      {isRotatingMode && fixedText && (
        <div className="mb-2">
          <span>{fixedText}</span>
        </div>
      )}

      {/* Texto animado (palabra rotativa o texto completo) */}
      <motion.h3
        variants={containerVariants}
        initial="typing"
        animate={animationState}
        className="m-0 text-gray-700"
      >
        {textToAnimate.split('').map((char, index) => {
          const shouldShow = animationState === 'showing'
                            || (animationState === 'typing' && index < visibleChars)
                            || (animationState === 'erasing' && index < visibleChars);

          return shouldShow ? (
            <motion.span
              key={`${char}-${index}-${currentWordIndex}`}
              variants={letterVariants}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="inline-block"
            >
              {char === ' ' ? '\u00A0' : char}
            </motion.span>
          ) : null;
        })}

        {/* Cursor parpadeante */}
        {((animationState === 'typing' && visibleChars < textToAnimate.length)
          || (animationState === 'erasing' && visibleChars > 0)) && (
          <motion.span
            animate={{ opacity: [1, 0, 1] }}
            transition={{ duration: 0.8, repeat: Infinity }}
            className="inline-block w-0.5 h-[1em] bg-current ml-0.5"
          />
        )}
      </motion.h3>
    </div>
  );
};

export default Typewriter;
