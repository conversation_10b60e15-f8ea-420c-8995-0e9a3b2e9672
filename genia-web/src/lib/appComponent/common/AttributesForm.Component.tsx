import {
  EditableHierarchy,
  EditableHierarchyComponent,
  HierarchyComponent,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { generateId } from 'ai';
import {
  useEffect,
  useMemo,
  useState,
} from 'react';

import { findHierarchy, reverseTransformData, transformData } from '#infrastructure/implementation/application/utils/transformAttributesData';

import {
  CATEGORY_LABEL, EMPTY_INPUT_ERROR, HIERARCHY_TYPE_CATEGORY, HIERARCHY_TYPE_VALUE, VALUE_LABEL,
} from '../../application/constants/texts/Attributes.Constants';
import { AttributesProps, TypeProps } from '../../application/deprecated/DashboardPages.Type';

import HierarchySelector from './HierarchySelector';

interface ChoseTypeProps {
  show: boolean;
  hierarchy: EditableHierarchy;
  level?: number
}

const inputValidator = (value: string): string | undefined => {
  if (!value) {
    return EMPTY_INPUT_ERROR;
  }

  return undefined;
};

const AttributesForm = (props: AttributesProps) => {
  const {
    attributeFormInputs, edit, onChange, labelProps, setError,
  } = props;

  const [newAttributes, setNewAttributes] = useState<EditableHierarchy[]>([]);

  useEffect(() => {
    if (attributeFormInputs) {
      const trasnformedData = transformData(attributeFormInputs);

      setNewAttributes(trasnformedData);
    }
  }, [attributeFormInputs]);

  const choseType = ({ show, hierarchy, level }: ChoseTypeProps) => {
    const handleSelection = (type: TypeProps) => {
      const newHierarchy = findHierarchy(newAttributes, hierarchy) as EditableHierarchy;

      newHierarchy.properties = {
        ...newHierarchy.properties,
        show: false,
      };

      newHierarchy.hierarchies = newHierarchy.hierarchies || [];

      newHierarchy.hierarchies.unshift({
        id: generateId(),
        name: '',
        properties: {
          type,
        },
      });

      setNewAttributes([...newAttributes]);
    };

    let options = [
      {
        type: HIERARCHY_TYPE_CATEGORY,
        label: CATEGORY_LABEL,
        testId: 'add-categories',
      },
      {
        type: HIERARCHY_TYPE_VALUE,
        label: VALUE_LABEL,
        testId: 'add-values',
      },
    ];

    if ((level || 0) > 2) {
      options = options.filter((opt) => opt.type !== HIERARCHY_TYPE_CATEGORY);
    }

    return (
      <HierarchySelector
        show={show}
        options={options}
        onSelect={(selectedType: TypeProps) => handleSelection(selectedType)}
      />
    );
  };

  const onRootAdd = (() => {
    const newHierarchy: EditableHierarchy = {
      name: '',
      id: generateId(),
      properties: {
        type: HIERARCHY_TYPE_CATEGORY,
      },
    };

    newAttributes.unshift(newHierarchy);

    setNewAttributes([...newAttributes]);
    setError?.(true);
  });

  const onAdd = (hierarchy: EditableHierarchy) => {
    const foundHierarchy = findHierarchy(newAttributes, hierarchy) as EditableHierarchy;

    const foundHierarchyArray = foundHierarchy.hierarchies || [];

    if (foundHierarchyArray?.length > 0) {
      const type = foundHierarchy?.hierarchies?.[0].properties?.type || '';

      foundHierarchy.hierarchies = [
        ...foundHierarchy.hierarchies || [],
        {
          id: generateId(),
          name: '',
          properties: {
            type,
          },
        },
      ];
      setNewAttributes([...newAttributes]);
      return;
    }

    foundHierarchy.properties = {
      ...(foundHierarchy.properties || {}),
      show: true,
    };

    setNewAttributes([...newAttributes]);
  };

  const mappedHierarchies = useMemo(() => {
    const mapHierarchies = (newHierarchies: EditableHierarchy[]): EditableHierarchy[] => newHierarchies.map((h) => {
      const type = h?.properties?.type;

      return ({
        ...h,
        onAdd,
        child: choseType as React.FC,
        hierarchies: h.hierarchies ? mapHierarchies(h.hierarchies) : undefined,
        title: type === HIERARCHY_TYPE_VALUE ? VALUE_LABEL : CATEGORY_LABEL as string,
        backgroundColor: type === HIERARCHY_TYPE_VALUE ? Theme.colors.fadedYellow : undefined,
        disableAdd: type === HIERARCHY_TYPE_VALUE,
        validator: inputValidator,
      });
    });

    return mapHierarchies(newAttributes);
  }, [newAttributes]);

  if (edit) {
    return (
      <EditableHierarchyComponent
        hierarchies={mappedHierarchies || []}
        onChange={(onChangeData) => {
          const newProps = reverseTransformData(onChangeData);
          onChange?.(newProps);
          setNewAttributes(onChangeData);
          setError?.(false);
        }}
        onRootAdd={onRootAdd}
        labelProps= {labelProps}
        className='!p-0'
      />
    );
  }

  return (
    <HierarchyComponent
      hierarchies={mappedHierarchies || []}
      labelProps= {labelProps}
      className='!p-0'
    />
  );
};

export default AttributesForm;
