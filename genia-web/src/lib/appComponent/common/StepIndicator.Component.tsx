import { IconImporter } from '@pitsdepot/storybook';

interface Step {
  id: string;
  label: string;
  completed?: boolean;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStepId: string;
}

export function StepIndicator({ steps, currentStepId }: StepIndicatorProps) {
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);

  const getStepTextColor = (isActive: boolean, isCompleted: boolean) => {
    if (isActive || isCompleted) return 'text-primary';
    return 'text-dark-500';
  };

  const getStepBackgroundColor = (isActive: boolean, isCompleted: boolean) => {
    if (isActive || isCompleted) return 'bg-primary';
    return 'bg-dark-500';
  };

  return (
    <div className='flex items-center gap-4 mb-4'>
      {steps.map((step, index) => {
        const isActive = step.id === currentStepId;
        const isCompleted = step.completed || index < currentStepIndex;
        const isLast = index === steps.length - 1;

        return (
          <div key={step.id} className='flex items-center gap-2'>
            <div className={`flex items-center gap-2 ${getStepTextColor(isActive, isCompleted)}`}>
              <div
                className={`
                  w-8 h-8 rounded-full flex items-center justify-center ${isActive ? 'text-gray-700' : 'text-white'}  ${getStepBackgroundColor(isActive, isCompleted)}
                `}
              >
                {isCompleted && !isActive ? (
                  <IconImporter name='check' size={16} />
                ) : (
                  index + 1
                )}
              </div>
              <span className='font-medium'>{step.label}</span>
            </div>

            {!isLast && (
              <div className={`w-8 h-1 ${isCompleted || isActive ? 'bg-primary' : 'bg-dark-300'}`}></div>
            )}
          </div>
        );
      })}
    </div>
  );
}
