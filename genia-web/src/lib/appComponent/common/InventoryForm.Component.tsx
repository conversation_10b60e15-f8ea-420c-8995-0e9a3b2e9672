import { FormComponent } from '@pitsdepot/storybook';
import { useEffect } from 'react';

import { InventoryItem, NewFormInventoryProps } from '../../application/deprecated/DashboardPages.Type';
import { useInventoryFormInputs } from '../../infrastructure/implementation/application/hooks/useInventoryFormInputs';

interface InventoryFormProps {
  editMode: boolean;
  defaultInputs:Partial<InventoryItem>;
  handleChildStateChange: (state: NewFormInventoryProps) => void;
  formValidation?: (error: boolean) => void;
  dataErrorName?: string;
}

const InventoryForm = ({
  editMode,
  defaultInputs,
  handleChildStateChange,
  formValidation,
  dataErrorName,
}: InventoryFormProps) => {
  const {
    inputs, formState, isSkuAutoGenerated, images,
  } = useInventoryFormInputs(defaultInputs, editMode);

  useEffect(() => {
    handleChildStateChange({ ...formState, isSkuAutoGenerated, images } as NewFormInventoryProps);
  }, [formState, images]);

  return (
    <FormComponent
      inputs={inputs}
      orientation='horizontal'
      columnsNumber={2}
      formValidation={formValidation}
      dataErrorName={dataErrorName}
    />);
};

export default InventoryForm;
