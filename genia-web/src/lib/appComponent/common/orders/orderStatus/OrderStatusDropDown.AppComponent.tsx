import {
  Badge, DropdownSimple, IconImporter, OptionsDropdownProps,
} from '@pitsdepot/storybook';
import { useCallback, useState } from 'react';

import { OrderStatusValueObject, OrderStatusValueObjectParams } from '#domain/aggregates/order/OrderStatus.ValueObject';

export function OrderStatusDropDownAppComponent({
  status,
  useFetch,
  onChange,
  disabled,
}: {
  status: OrderStatusValueObject;
  onChange?: (selectedOption: OrderStatusValueObject) => void;
  useFetch: () => OrderStatusValueObject[];
  disabled?: boolean;
}) {
  const statusOptions = useFetch();

  const [selectedOption, setSelectedOption] = useState<OrderStatusValueObjectParams>({
    id: status.id,
    name: status.name,
    color: status.color,
  });

  const onStatusChange = useCallback((newSelected: OptionsDropdownProps) => {
    const newState = statusOptions.find((option) => option.id === newSelected.id);

    if (!newState) return;

    setSelectedOption(newState);
    onChange?.(status.setId(newState.id).setName(newState.name));
  }, [onChange, status, statusOptions]);

  return (
    <div>
      <DropdownSimple
        options={statusOptions}
        showAvatar={false}
        setSelectedOption={onStatusChange}
        disabled={disabled}
      >
        <Badge color={selectedOption.color} className={`!text-sm ${!disabled ? 'cursor-pointer' : 'cursor-not-allowed'}`}>
          {selectedOption.name}
          {!disabled && <IconImporter
            size={16}
            name='caretDown'
          />}
        </Badge>
      </DropdownSimple>
    </div>
  );
}
