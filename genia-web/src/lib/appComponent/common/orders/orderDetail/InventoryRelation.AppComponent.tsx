import { Avatar } from '@pitsdepot/storybook';

import TextService from '#composition/textService/Text.Service';
import { OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';

export interface OrderItemRelatedInventoryAppComponentProps {
  inventoryItems: OrderItemInventoryRelation[]
}

function InventoryRenderer({ item }: { item: OrderItemInventoryRelation }) {
  return (
    <div className="relative flex items-center flex-1">
      <div className="flex items-center gap-2 ml-6 relative z-10 flex-1 min-w-[150px]">
        <div className='ml-1 mt-2 relative top-0 w-max flex-1'
          style={{
            minWidth: 'calc(100% - 24px)',
          }}>
          <div className='text-xsm ml-1 flex items-center gap-1 flex-1'>
            <div className='flex items-center flex-1 min-w-[150px]'>
              <Avatar
                size= {24}
                src={item.image}
                className='rounded-full mr-2 shrink-0'
              />
              <div className="flex items-center gap-1 mt-0 flex-1">
                {item.name} <span className='italic'>{item.sku}</span><span className="font-semibold">x {item.total}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute top-0 left-0 h-full w-full flex items-center justify-start">
        <div className="h-4 w-[10px] -mt-3 border-l-2 border-dashed border-gray-400" />
        <div className="h-2 w-[20px] -ml-2 border-b-2 border-dashed border-gray-400" />
      </div>
    </div>
  );
}

export function OrderItemRelatedInventoryAppComponent(props: OrderItemRelatedInventoryAppComponentProps) {
  const { inventoryItems } = props;

  return (
    <div>
      <p className='ml mt-1 text-xs font-medium'>{TextService.getText().inventory.automaticUpdate}</p>
      { inventoryItems.length ? inventoryItems.map((item) => (
        <div key={item.id}>
          <InventoryRenderer item={item} />
        </div>
      ))
        : <div className='ml-10 mt-2 text-xsm'>No hay piezas de inventario asociadas</div>}
    </div>
  );
}
