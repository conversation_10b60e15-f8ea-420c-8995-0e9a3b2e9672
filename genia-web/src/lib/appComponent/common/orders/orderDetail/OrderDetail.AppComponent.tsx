import { IconImporter } from '@pitsdepot/storybook';
import { useCallback, useContext, useMemo } from 'react';

import {
  DropDownSearchAppComponent,
  DropDownSearchAppComponentOption,
} from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { OrderDetailContext, defaultFetch } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import { OrderDetailOrderInfoAppComponent } from '#appComponent/common/orders/orderDetail/OrderDetailOrderInfo.AppComponent';
import { OrderItemsAppComponent } from '#appComponent/common/orders/orderDetail/OrderItems.AppComponent';
import TextService from '#composition/textService/Text.Service';
import { OrderReceiver } from '#domain/aggregates/order/OrderReceiver.Entity';

const textService = TextService.getText();

export function OrderDetailAppComponent() {
  const {
    order, onChange, useFetchReceiver, onAddProduct: addProduct, useFetchUsers, editable,
  } = useContext(OrderDetailContext);

  const onReceiverChange = useCallback((receiver: DropDownSearchAppComponentOption) => {
    const newOrder = order.setReceiver({
      ...order.receiver,
      id: receiver.id,
      name: receiver.name,
      companyId: (receiver as OrderReceiver).companyId,
    });
    onChange(newOrder);
  }, [JSON.stringify(order), order.receiver, onChange]);

  const onUserChange = useCallback((user: DropDownSearchAppComponentOption) => {
    const newUser = {
      id: user.id,
      email: user.name,
    };
    const newOrder = order.setInfo({
      ...order.info,
      assignedUser: newUser,
    });
    onChange(newOrder);

    if (onChange) onChange(newOrder);
  }, [order, onChange, onChange]);

  const memoizedUserDropDown = useMemo(
    () => (
      <DropDownSearchAppComponent
        className={`mt-2 text-sm ${order.receiver.disabled ? `
        [&_.drop-down-search-display]:!bg-white
        [&_.drop-down-search-display]:!border-none 
        [&_.drop-down-search-display]:!px-0 
        [&_.drop-down-search-display]:!font-semibold` : ''}`}
        selected={{
          id: order.info.assignedUser?.id || '',
          name: order.info.assignedUser?.email || '',
        }}
        useFetch={useFetchUsers || defaultFetch}
        onSelect={onUserChange}
        disabled={!editable || !order.id}/>
    ),
    [order.receiver.id, useFetchReceiver, onUserChange],
  );

  const memoizedClientDropDown = useMemo(
    () => (
      <DropDownSearchAppComponent
        className={`mt-2 text-sm ${order.receiver.disabled ? `
        [&_.drop-down-search-display]:!bg-white
        [&_.drop-down-search-display]:!border-none 
        [&_.drop-down-search-display]:!px-0 
        [&_.drop-down-search-display]:!font-semibold` : ''}`}
        selected={order.receiver}
        useFetch={useFetchReceiver}
        onSelect={onReceiverChange}
        disabled={order.receiver.disabled}/>
    ),
    [order.receiver.id, useFetchReceiver, onReceiverChange],
  );

  const memoizedOrderItems = useMemo(() => (
    <OrderItemsAppComponent/>
  ), [JSON.stringify(order.orderItems)]);

  return (
    <div className='flex flex-wrap gap-8"'>
      <div className="w-[350px]">
        <div className='max border rounded-2xl p-6'>
          <p className='text-sm font-medium'>{order.receiver.type === 'client' ? textService.clients.client : textService.providers.provider}: </p>
          {memoizedClientDropDown}

          {<div className = 'mt-4'>
            {!order.id ? null : <p className='text-sm font-medium'>{textService.user.assignedUser}</p>}
            {!order.id ? null : memoizedUserDropDown}
          </div>}

          <OrderDetailOrderInfoAppComponent/>
        </div>
      </div>
      <div className="w-full flex-1 flex flex-col px-7 pb-8 gap-4">
        <p className="text-sm font-medium">{textService.orders.orderItems}</p>
        {memoizedOrderItems}
        {addProduct && !addProduct.disabled && <div
          className='flex my-4  w-fit cursor-pointer'
          onClick={addProduct.onClick}
          >
          <IconImporter
            data-testid="add_new_order_product"
            size={24}
            name='plus'
            className='mr-4'
              />
          <p>{textService.product.addNew}</p>
          </div>}
      </div>
    </div>
  );
}
