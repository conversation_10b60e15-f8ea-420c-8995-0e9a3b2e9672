import {
  DateInputComponent, FormErrorComponent, FormHandlerComponent, FormHandlerState, InputComponent, InputPrice, TextAreaInput,
} from '@pitsdepot/storybook';
import { useContext, useState } from 'react';

import { OrderDetailContext } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import {
  OrderStatusDropDownAppComponent,
} from '#appComponent/common/orders/orderStatus/OrderStatusDropDown.AppComponent';
import { OrderSummaryAppComponent } from '#appComponent/common/orders/orderSummary/OrderSummary.AppComponent';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';
import { OrderEntity, OrderError } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusValueObject } from '#domain/aggregates/order/OrderStatus.ValueObject';

const timeService = ApplicationRegistry.TimeService;
const textService = TextService.getText();

function DisplayDate({ order }: { order: OrderEntity }) {
  const { deliveryDate } = order.info;
  return (
    <p className="text-xsm text-dark-600">
      {deliveryDate.value ? timeService.toLocalDateFormat(deliveryDate.value) : 'Sin información disponible'}
    </p>
  );
}

function DisplayAddress({ order }: { order: OrderEntity }) {
  const { shippingAddress } = order.info;
  return (
    <p className="text-xsm text-dark-600">
      {shippingAddress.value || 'Sin información disponible'}
    </p>
  );
}

function ShippingPrice({ order }: { order: OrderEntity }) {
  const { shippingPrice } = order.info;

  if (shippingPrice.hidden) return null;

  return (
    <div className='flex flex-col gap-2'>
      <p className='text-sm font-medium'>{textService.shipping.price}:</p>
      <InputPrice
        name="shippingPrice"
        className="w-full"
        disabled={shippingPrice.disabled}
      />
    </div>
  );
}

function DeliveryDate({
  order,
  editable,
  onChange,
}: {
  order: OrderEntity,
  editable: boolean,
  onChange: (order: OrderEntity) => void,
}) {
  const [error, setError] = useState<string | null>(null);
  const deliveryDateInfo = order.info.deliveryDate;

  if (deliveryDateInfo.hidden) return null;

  const onError = (newError: string | null) => {
    const newOrder = order;
    setError(newError);
    if (!newError) {
      if (order.errors[OrderError.INVALID_DELIVERY_DATE]) {
        // next render avoid conflicts with form handler
        setTimeout(() => {
          onChange(order.clearError(OrderError.INVALID_DELIVERY_DATE));
        }, 0);
      }

      return;
    }

    // next render avoid conflicts with form handler
    setTimeout(() => {
      onChange(newOrder.addError(OrderError.INVALID_DELIVERY_DATE, newError));
    }, 0);
  };
  const component = editable ? (
    <>
      <DateInputComponent
        name="deliveryDate"
        className="w-full"
        disabled={deliveryDateInfo.disabled}
        onError={onError}
      />
      {error && <FormErrorComponent>
        {textService.error.invalidDate}
      </FormErrorComponent>}
    </>
  ) : (
    <DisplayDate order={order} />
  );

  return (
    <div className='flex flex-col gap-2'>
      <p className='text-sm font-medium'>{textService.orders.deliveryDate}:</p>
      {component}
    </div>
  );
}

function ShippingAddress({
  order,
  editable,
}: {
  order: OrderEntity,
  editable: boolean,
}) {
  const shippingAddressInfo = order.info.shippingAddress;

  if (shippingAddressInfo.hidden) return null;

  const component = editable ? (
    <InputComponent
      name="shippingAddress"
      className="w-full"
      disabled={shippingAddressInfo.disabled}
      placeholder={textService.shipping.address}
    />
  ) : (
    <DisplayAddress order={order} />
  );

  return (
    <div className='flex flex-col gap-2'>
      <p className='text-sm font-medium'>{textService.shipping.address}:</p>
      {component}
    </div>
  );
}

function OrderNumber({
  order,
  state,
}: {
  order: OrderEntity,
  state: FormHandlerState,
}) {
  const orderNumberInfo = order.info.orderNumber;

  if (orderNumberInfo.hidden) return null;

  return (
    <div className='flex flex-col gap-2'>
      <p className='text-sm font-medium'>{textService.orders.orderNumber}:</p>
      <InputComponent
        name="orderNumber"
        className="w-full"
        value={state.orderNumber as string}
        disabled={state.generateOrderNumber as boolean || orderNumberInfo.disabled}
        placeholder={textService.orders.orderNumberPlaceholder}
      />
      {!orderNumberInfo.disabled && !state.generateOrderNumber && !state.orderNumber
      && <FormErrorComponent>
        {textService.common.requiredField}
      </FormErrorComponent>}
      <div className='flex items-center gap-2'>
        <label htmlFor='generateOrderNumber' className='text-xs font-light'>{textService.common.autoGenerate}:</label>
        <input
          id='generateOrderNumber'
          name='generateOrderNumber'
          type='checkbox'
          checked={state.generateOrderNumber as boolean}
          disabled={!order.receiver.id}
        ></input>
      </div>
    </div>
  );
}

function OrderStatus({
  order,
  editable,
  useFetchStatuses,
  onStatusChange,
}: {
  order: OrderEntity,
  editable: boolean,
  useFetchStatuses: () => OrderStatusValueObject[],
  onStatusChange: (status: OrderStatusValueObject) => void,
}) {
  const statusInfo = order.info.status;

  if (statusInfo.hidden) return null;

  return (
    <div className='flex flex-col gap-2'>
      <p className='text-sm font-medium'>{textService.orders.orderStatus}:</p>
      <OrderStatusDropDownAppComponent
        status={statusInfo.value}
        useFetch={useFetchStatuses}
        disabled={!editable || statusInfo.disabled}
        onChange={onStatusChange}/>
    </div>
  );
}

export function OrderDetailOrderInfoAppComponent() {
  const {
    order,
    editable,
    useFetchStatuses,
    onChange,
  } = useContext(OrderDetailContext);

  const [state, setState] = useState<FormHandlerState>({
    shippingAddress: order.info.shippingAddress.value,
    shippingPrice: order.info.shippingPrice.value as number,
    deliveryDate: order.info.deliveryDate.value,
    notes: order?.info.notes.value,
    generateOrderNumber: false,
  });

  const onFormChange = (handlerState: FormHandlerState) => {
    const shippingPrice = handlerState.shippingPrice ? Number.parseFloat(handlerState.shippingPrice as string) : handlerState.shippingPrice as unknown as null;
    const newShippingPrice = shippingPrice && shippingPrice > 1000000 ? 1000000 : shippingPrice;
    const orderNumber = handlerState.generateOrderNumber ? '' : handlerState.orderNumber as string;
    const newInfo = order.info
      .setShippingAddress({
        ...order.info.shippingAddress,
        value: handlerState.shippingAddress as string,
      })
      .setShippingPrice({
        ...order.info.shippingPrice,
        value: newShippingPrice,
      })
      .setDeliveryDate({
        ...order.info.deliveryDate,
        value: handlerState.deliveryDate ? handlerState.deliveryDate as string : '',
      })
      .setNotes({
        ...order.info.notes,
        value: handlerState.notes as string,
      })
      .setOrderNumber({
        ...order.info.orderNumber,
        value: orderNumber,
        disabled: (handlerState.generateOrderNumber || order.receiver.type === 'provider' || order.id) as boolean,
      });

    setState({
      ...handlerState,
      shippingPrice: newShippingPrice as number,
      orderNumber,
    });

    let newOrder = order.setInfo(newInfo);

    if (!handlerState.generateOrderNumber && !handlerState.orderNumber && !order.info.orderNumber.disabled) {
      newOrder = newOrder.addError(OrderError.ORDER_NUMBER_REQUIRED, 'required');
    } else if (order.errors[OrderError.ORDER_NUMBER_REQUIRED]) {
      newOrder = newOrder.clearError(OrderError.ORDER_NUMBER_REQUIRED);
    }

    onChange(newOrder);
  };

  const onStatusChange = (status: OrderStatusValueObject) => {
    const newStatus = new OrderStatusValueObject({
      id: status.id,
      name: status.name,
      color: status.color,
    });

    const newInfo = order.info.setStatus({
      ...order.info.status,
      value: newStatus,
    });

    const newOrder = order.setInfo(newInfo);
    onChange(newOrder);
  };

  return (
    <FormHandlerComponent onChange={onFormChange} initialState={state}>
      <div className='flex flex-col gap-4 mt-4'>
        <OrderStatus
          order={order}
          editable={!!editable}
          useFetchStatuses={useFetchStatuses}
          onStatusChange={onStatusChange}
        />
        {OrderNumber({
          order,
          state,
        })}
        {ShippingAddress({
          order,
          editable: !!editable,
        })}
        {ShippingPrice({
          order,
        })}
        {DeliveryDate({
          order,
          editable: !!editable,
          onChange,
        })}
        <div className="mt-4">
          <p className="text-sm font-medium">{textService.summary.title}:</p>
          <div className='flex-1'>
            <OrderSummaryAppComponent summary={order.info.summary}/>
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm font-medium">{textService.orders.notes}:</p>
          <TextAreaInput
            name="notes"
            placeholder={textService.orders.notesPlaceholder}
            disabled={order.info.notes.disabled || !editable}
            rows={8}
            className='mt-2 !h-[150px]'
          />
        </div>
      </div>
    </FormHandlerComponent>
  );
}
