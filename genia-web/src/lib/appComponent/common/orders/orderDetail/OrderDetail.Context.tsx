import {
  createContext, PropsWithChildren,
  useMemo,
} from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { OrderEntity } from '#domain/aggregates/order/Order.Entity';
import { OrderStatusValueObject } from '#domain/aggregates/order/OrderStatus.ValueObject';

export interface OrderDetailContextType {
  order: OrderEntity;
  editable?: boolean;
  onAddProduct? : {
    disabled: boolean;
    onClick: () => void;
  }
  useFetchCatalog: DropDownSearchFetch;
  useFetchReceiver: DropDownSearchFetch;
  useFetchUsers?: DropDownSearchFetch;
  useFetchStatuses: () => OrderStatusValueObject[];
  useFetchInventory?: DropDownSearchFetch ;
  onChange: (order: OrderEntity) => void;
}

export const defaultFetch: DropDownSearchFetch = () => ({
  isLoading: false,
  options: [],
  refetch: () => {},
});

const defaultStatusFetch = () => [] as OrderStatusValueObject[];

const defaultContextValue: OrderDetailContextType = {
  order: OrderEntity.empty('client'),
  editable: false,
  useFetchCatalog: defaultFetch,
  useFetchReceiver: defaultFetch,
  useFetchUsers: defaultFetch,
  useFetchStatuses: defaultStatusFetch,
  useFetchInventory: defaultFetch,
  onChange: () => {},
};

export const OrderDetailContext = createContext<OrderDetailContextType>(defaultContextValue);

export function OrderDetailContextProvider(props: PropsWithChildren<Omit<OrderDetailContextType, 'onChange'> & {onChange: (order: OrderEntity) => void}>) {
  const {
    children,
    editable,
    useFetchCatalog,
    useFetchReceiver,
    useFetchStatuses,
    onChange,
    useFetchInventory,
    onAddProduct,
    useFetchUsers,
  } = props;

  const contextValue: OrderDetailContextType = useMemo(() => ({
    order: props.order,
    editable,
    useFetchCatalog,
    useFetchReceiver,
    useFetchStatuses,
    useFetchInventory,
    onChange,
    onAddProduct,
    useFetchUsers,
  }), [JSON.stringify(props.order), editable, useFetchCatalog, useFetchReceiver, useFetchStatuses, useFetchInventory, onAddProduct, useFetchUsers]);

  return (
    <OrderDetailContext.Provider value={contextValue}>
      {children}
    </OrderDetailContext.Provider>
  );
}
