import { IconImporter } from '@pitsdepot/storybook';
import { useCallback, useContext, useMemo } from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { OrderDetailContext } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import { OrderDetailProductDropDownProvider } from '#appComponent/common/orders/orderDetail/OrderDetailProductDropdown.Provider';
import { OrderItemInventoryRelationAppComponent } from '#appComponent/common/orders/orderDetail/OrderItemInventoryRelation.AppComponent';
import { OrderItemDiscountAppComponent } from '#appComponent/common/orders/orderItem/OrderItemDiscount.AppComponent';
import { OrderItemFormAppComponent } from '#appComponent/common/orders/orderItem/OrderItemForm.AppComponent';
import { OrderSummaryAppComponent } from '#appComponent/common/orders/orderSummary/OrderSummary.AppComponent';
import TextService from '#composition/textService/Text.Service';
import { OrderItemEntity } from '#domain/aggregates/order/OrderItem.Entity';

const textService = TextService.getText();

function renderItem(
  item: OrderItemEntity,
  index: number,
  items: OrderItemEntity[],
  onItemsChange: (item: OrderItemEntity, index: number) => void,
  onItemDelete: (index: number) => void,
  useFetchCatalog: DropDownSearchFetch,
) {
  return (
    <div key={`${item.id}-${index}` || index} className={`flex flex-col gap-4 pb-4${index === items.length - 1 ? '' : ' border-b border-gray-200'}`}>
      <div className="flex gap-4 items-center">
        <OrderDetailProductDropDownProvider useFetch={useFetchCatalog}>
          <OrderItemFormAppComponent item={item} onChange={(newItem) => onItemsChange(newItem, index)} />
          { (!item.name.disabled && items.length > 1)
              && <div className='flex flex-col gap-4 text-negative'>
                  {/* Don't remove this div we use it as a placeholder */}
                <div></div>
                <IconImporter
                  onClick={() => onItemDelete(index)}
                  size={24}
                  name='x'
                  className='cursor-pointer'/>
              </div>
            }
        </OrderDetailProductDropDownProvider>
      </div>
      <div className="flex flex-wrap justify-between gap-2">
        <div className='max-w-[250px]'>
          <OrderItemInventoryRelationAppComponent orderItem={item}/>
        </div>
        {item.discounts.applicable.length > 0 && <div className='w-[250px]'>
          <p className='text-xs font-medium'>{textService.orders.itemDiscounts}</p>
          <div className='mt-2'>
            <OrderItemDiscountAppComponent discounts={item.discounts} />
          </div>
          </div>}
        <div className='min-w-[200px]'>
          <p className='text-xs font-medium'>{textService.orders.itemSummary}</p>
          <OrderSummaryAppComponent summary={item.summary}/>
        </div>
      </div>
    </div>
  );
}

export function OrderItemsAppComponent() {
  const {
    order,
    useFetchCatalog,
    onChange,
  } = useContext(OrderDetailContext);

  const onItemsChange = useCallback((
    item: OrderItemEntity,
    index: number,
  ) => {
    const newItems = order.orderItems.map((oldItem, i) => {
      if (i === index) {
        return item;
      }
      return oldItem;
    });

    const newOrder = order.setOrderItems(newItems);
    onChange(newOrder);
  }, [JSON.stringify(order), onChange]);

  const onItemDelete = useCallback((index: number) => {
    const newItems = order.orderItems.filter((_, i) => i !== index);
    const newOrder = order.setOrderItems(newItems);
    onChange(newOrder);
  }, [JSON.stringify(order), onChange]);

  const items = useMemo(() => order?.orderItems || [], [JSON.stringify(order?.orderItems)]);

  return useMemo(
    () => items.map((item, index) => renderItem(item, index, items, onItemsChange, onItemDelete, useFetchCatalog)),
    [JSON.stringify(items), onItemsChange, onItemDelete, useFetchCatalog],
  );
}
