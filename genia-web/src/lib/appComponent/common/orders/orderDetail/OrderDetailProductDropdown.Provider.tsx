import { useState } from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import {
  ProductDropDownContext,
  ProductDropDownContextType,
  ProductDropDownProduct,
} from '#appComponent/common/orders/productDropDown/ProductDropDown.Context';

interface ProductDropdownProviderProps {
  children: React.ReactNode;
  useFetch: DropDownSearchFetch;
  onSelect?: (product: ProductDropDownProduct) => void;
}

export const OrderDetailProductDropDownProvider = ({
  children,
  useFetch,
  onSelect,
}: ProductDropdownProviderProps) => {
  const [selectedProduct, setSelectedProduct] = useState<ProductDropDownProduct | null>(null);
  const [disabled, setDisabled] = useState(false);

  const contextValue: ProductDropDownContextType = {
    selectedProduct,
    disabled,
    setDisabled,
    setSelectedProduct,
    onSelect,
    useFetch,
  };

  return (
    <ProductDropDownContext.Provider value={contextValue}>
      {children}
    </ProductDropDownContext.Provider>
  );
};
