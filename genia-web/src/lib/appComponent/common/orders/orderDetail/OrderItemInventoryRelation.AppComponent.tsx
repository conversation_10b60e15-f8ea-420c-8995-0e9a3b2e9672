import { Avatar, IconImporter } from '@pitsdepot/storybook';
import { useCallback, useContext } from 'react';

import {
  DropDownSearchAppComponent,
  DropDownSearchAppComponentOption,
  DropDownSearchFetch,
} from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import { OrderDetailContext, OrderDetailContextType } from '#appComponent/common/orders/orderDetail/OrderDetail.Context';
import TextService from '#composition/textService/Text.Service';
import { OrderItemEntity, OrderItemInventoryRelation } from '#domain/aggregates/order/OrderItem.Entity';

export interface OrderItemRelatedInventoryAppComponentProps {
  orderItem: OrderItemEntity
}

const text = TextService.getText();

const CustomResults = (value: OrderItemInventoryRelation) => {
  const item = value;
  const { name, sku, image } = item;

  return (
    <div className="flex items-center">
      <div className="flex items-center justify-center m-2">
        <Avatar
          src={image || ''}
          name={name}
          size={28}
        />
      </div>
      <div className='flex flex-col'>
        <div className='text-xsm line-clamp-1'>{name}</div>
        <div className='text-xs text-gray-500'>{text.product.sku}: {sku}</div>
      </div>
    </div>
  );
};

function inventoryInput(
  useFetchInventory: DropDownSearchFetch,
  relation: OrderItemInventoryRelation,
  item: OrderItemEntity,
  context: OrderDetailContextType,
) {
  const { order, onChange } = context;

  const currenOrderItem = order.orderItems.find((i) => i.id === item.id) as OrderItemEntity;
  const currentInventory = currenOrderItem?.inventoryRelations.find((i) => JSON.stringify(i) === JSON.stringify(relation)) as OrderItemInventoryRelation;

  function onOptionSelect(selected: DropDownSearchAppComponentOption<OrderItemInventoryRelation>) {
    const newRelations = currenOrderItem?.inventoryRelations.map((r) => {
      if (JSON.stringify(r) === JSON.stringify(currentInventory)) {
        return {
          ...currentInventory,
          id: selected.id,
          name: selected.name,
          sku: selected.sku,
          image: selected.image,
          quantity: 1,
          total: currenOrderItem.quantity.value,
        };
      }
      return r;
    });

    const newOrderItem = currenOrderItem?.setInventoryRelations(newRelations);

    const orderItems = order.orderItems.map((i) => {
      if (i.id === item.id) {
        return newOrderItem;
      }
      return i;
    });

    const newOrder = order.setOrderItems(orderItems);
    onChange(newOrder);
  }

  const onRelationDelete = () => {
    const newRelations = currenOrderItem?.inventoryRelations.filter((r) => r !== currentInventory);
    const newOrderItem = currenOrderItem?.setInventoryRelations(newRelations);
    const orderItems = order.orderItems.map((i) => {
      if (i.id === item.id) {
        return newOrderItem;
      }
      return i;
    });
    const newOrder = order.setOrderItems(orderItems);
    onChange(newOrder);
  };

  return (
    <div className='ml-1 mt-2 relative top-0 flex-1 flex gap-2 items-center text-xsm flex-1 w-[calc(100%-24px)]' >
      <DropDownSearchAppComponent
        selected={relation}
        useFetch={useFetchInventory}
        className='text-sm flex-1  w-[calc(100%-60px)]'
        renderer={CustomResults}
        onSelect={onOptionSelect}
    />
      <div className="flex items-center gap-1 mt-0">
        <span className="font-semibold">x {relation.total}</span>
      </div>
      <div>
        <IconImporter
          name='x'
          size={16}
          className="text-gray-500 cursor-pointer hover:text-red-500"
          onClick={onRelationDelete}/>
      </div>
    </div>

  );
}

function inventoryDisplay(item: OrderItemInventoryRelation) {
  return (
    <div className='ml-1 mt-2 top-0 flex-1'
      style={{
        minWidth: 'calc(100% - 24px)',
      }}>
      <div className='text-xsm ml-1 flex items-center gap-1 flex-1'>
        <div className='flex items-center flex-1 min-w-[150px]'>
          <Avatar
            size= {24}
            name={item.name}
            src={item.image}
            className='rounded-full mr-2 shrink-0'
        />
          <div className="flex items-center gap-1 mt-0 flex-1">
            <span className="w-[calc(100%-60px)] line-clamp-1"> {item.name}
              <span className='italic'>{item.sku}</span>
            </span>
            <span className="font-semibold flex flex-no-wrap">x {item.total}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function InventoryRenderer({ component }: { item: OrderItemInventoryRelation, component: React.ReactNode }) {
  return (
    <div className="relative flex items-center w-full">
      <div className="flex items-center gap-2 ml-6 relative z-10 flex-1 min-w-[150px]">
        {component}
      </div>
      <div className="absolute top-0 left-0 h-full w-full flex items-center justify-start">
        <div className="h-4 w-[10px] -mt-3 border-l-2 border-dashed border-gray-400" />
        <div className="h-2 w-[20px] -ml-2 border-b-2 border-dashed border-gray-400" />
      </div>
    </div>
  );
}

export function OrderItemInventoryRelationAppComponent(props: OrderItemRelatedInventoryAppComponentProps) {
  const { orderItem } = props;
  const context = useContext(OrderDetailContext);

  const {
    order, onChange, useFetchInventory, editable,
  } = context;

  const inventoryItems = orderItem.inventoryRelations;

  const component = useCallback(() => {
    const onClick = () => {
      const newOrderItem = orderItem.addInventoryRelation({
        id: '',
        name: '',
        sku: '',
        image: '',
        quantity: 0,
        total: 0,
      });

      const orderItems = order.orderItems.map((item) => {
        if (item.id === orderItem.id) {
          return newOrderItem;
        }
        return item;
      });

      onChange(order.setOrderItems(orderItems));
    };

    const inventoryItemsComponents = inventoryItems.length > 0
      ? inventoryItems.map((inventoryItem) => (
        <div key={inventoryItem.id}>
          <InventoryRenderer item={inventoryItem}
            component={editable && useFetchInventory
              ? inventoryInput(useFetchInventory, inventoryItem, orderItem, context) : inventoryDisplay(inventoryItem)}/>
        </div>
      ))
      : (
        <div className='ml-2 mt-2 text-xsm flex flex-col gap-1'>
          {text.orders.noInventoryRelations}
        </div>
      );

    return (
      <div>
        {inventoryItemsComponents}
        {editable && useFetchInventory && orderItem.id && (
          <div className='ml-2 mt-2'>
            <span onClick={onClick} className='text-toscaBlue cursor-pointer underline underline-offset-4'>{text.common.add}</span>
          </div>
        )}
      </div>
    );
  }, [order, orderItem, editable, useFetchInventory]);

  return (
    <div>
      <p className='ml mt-1 text-xs font-medium'>{TextService.getText().inventory.automaticUpdate}</p>
      {component()}
    </div>
  );
}
