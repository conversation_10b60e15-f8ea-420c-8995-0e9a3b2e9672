import { createContext } from 'react';

import { DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';

export interface ProductDropDownProduct {
  id: string;
  name: string;
  productId: string;
  image?: string;
}

export interface ProductDropDownContextType {
  selectedProduct?: ProductDropDownProduct | null;
  setSelectedProduct: (product: ProductDropDownProduct | null) => void;
  disabled?: boolean;
  setDisabled: (disabled: boolean) => void;
  onSelect?: (product: ProductDropDownProduct) => void;
  useFetch: DropDownSearchFetch;
}

export const ProductDropDownContext = createContext<ProductDropDownContextType | undefined>(undefined);
