import { AutocompleteInput, Avatar, OptionsDropdownProps } from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import { useDebounce } from '#appComponent/common/hooks/useDebounce.Hook';
import { ProductDropDownProduct } from '#appComponent/common/orders/productDropDown/ProductDropDown.Context';
import { InventoryItem } from '#application/inventory/Inventory.Type';
import ApplicationRegistry from '#composition/Application.Registry';

interface AutocompleteAppComponentProps {
  onSelect?: (product: ProductDropDownProduct) => void;
  selectedProduct?: ProductDropDownProduct | null;
  onAutocompleteChange?: (value: string) => void;
  name: 'name' | 'productId';
}

interface InventoryOption extends InventoryItem {
  src: string;
  renderer: (value: unknown) => React.ReactNode;
}

export function AutocompleteAppComponent(
  {
    onSelect, selectedProduct, onAutocompleteChange, name,
  }: AutocompleteAppComponentProps,
) {
  const [value, setValue] = useState(selectedProduct?.[name] || '');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<OptionsDropdownProps | null>(selectedProduct || null);

  const { debouncedValue } = useDebounce(value, { delay: 1000 });

  const { items = [] } = ApplicationRegistry.InventoryService.useGetInventory({
    limit: 10,
    offset: 0,
    searchTerm: debouncedValue,
    orderBy: 'name',
    order: 'asc',
  });

  useEffect(() => {
    const isSelectedValueMatching = selectedOption ? value === selectedOption[name as keyof OptionsDropdownProps] : false;
    const hasFocus = document.activeElement === document.querySelector('input[name="product"]');

    if (debouncedValue && value.length > 0 && items.length > 0 && !isSelectedValueMatching && hasFocus) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [debouncedValue, value, items, selectedOption, name]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setValue(newValue);
    setSelectedOption(null);

    onAutocompleteChange?.(newValue);
  };

  const handleSelect = (option: InventoryOption) => {
    const newOption = option[name as keyof InventoryOption];

    setValue(newOption as string);

    onSelect?.({
      id: option.id,
      name: option.name || '',
      productId: option.sku || '',
      image: option.src || '',
    });

    setIsOpen(false);
  };

  const handleBlur = () => {
    // Cerrar inmediatamente las sugerencias cuando el input pierde el foco
    setIsOpen(false);
  };

  const options = items?.map((item) => ({
    ...item,
    productNumber: item.sku || `SEED-${item.id.toString().padStart(8, '0')}`,
    src: item.inventoryMedia?.[0]?.url || '',
    renderer: (optionValue: unknown) => {
      const option = optionValue as OptionsDropdownProps & { productNumber?: string };

      return (
        <div className={
          'flex items-center gap-3 p-2 hover:bg-gray-100 cursor-pointer transition-colors duration-200 ease-in-out'
        }>
          <Avatar
            size={32}
            src={option.src}
            name={option.name}
        />
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-900">{option.name}</span>
            <span className="text-xs text-gray-500">{option.productNumber}</span>
          </div>
        </div>
      );
    },
  }));

  return (
    <div>
      <AutocompleteInput
        dropdownSuggestionsProps={{
          options,
          isOpen,
          showAvatar: true,
          onSelect: handleSelect as (option: OptionsDropdownProps) => void,
        }}
        inputProps={{
          name: 'product',
          value,
          placeholder: 'Escribe un producto...',
          onChange: handleInputChange,
          onBlur: handleBlur,
          error: value.length < 1,
        }}
      />
    </div>
  );
}
