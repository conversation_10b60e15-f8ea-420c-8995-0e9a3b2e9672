import {
  Avatar,
} from '@pitsdepot/storybook';
import {
  useContext,
  useMemo,
} from 'react';

import { DropDownSearchAppComponent, DropDownSearchFetch } from '#appComponent/common/components/dropDownSearch/DropDownSearch.AppComponent';
import TextService from '#composition/textService/Text.Service';

import { ProductDropDownContext, ProductDropDownProduct } from './ProductDropDown.Context';

export interface ProductDropdownAppComponentProps {
  selectedProduct?: ProductDropDownProduct | null;
  onSelect?: (product: ProductDropDownProduct) => void;
  useFetch?: DropDownSearchFetch;
  disabled?: boolean;
  className?: string;
}

const computeProps = (
  props: ProductDropdownAppComponentProps,
  contextValue: React.ContextType<typeof ProductDropDownContext>,
): Required<ProductDropdownAppComponentProps> => {
  const computedProps = {
    selectedProduct: props.selectedProduct || null,
    onSelect: props.onSelect || contextValue?.onSelect,
    useFetch: props.useFetch || contextValue?.useFetch,
    disabled: props.disabled,
    className: props.className || '',
  };

  if (!computedProps.useFetch) {
    throw new Error('ProductDropdownAppComponent must receive useFetch prop or be used within a ProductDropDown.Provider');
  }

  return computedProps as Required<ProductDropdownAppComponentProps>;
};

const CustomResults = (value: unknown) => {
  const product = value as ProductDropDownProduct;
  const { name } = product;
  const text = TextService.getText();

  return (
    <div className="flex items-center">
      <div className="flex items-center justify-center m-2">
        <Avatar
          src={product.image || ''}
          name={name}
          size={28}
        />
      </div>
      <div className='flex flex-col'>
        <div className='text-xsm line-clamp-1'>{name}</div>
        <div className='text-xs text-gray-500'>{text.product.number}: {product.productId}</div>
      </div>
    </div>
  );
};

export function ProductDropdownAppComponent(props: ProductDropdownAppComponentProps) {
  const contextValue = useContext(ProductDropDownContext);
  const {
    selectedProduct,
    onSelect,
    useFetch,
    disabled,
    className,
  } = useMemo(() => computeProps(props, contextValue), [props, contextValue]);

  return (
    <DropDownSearchAppComponent
      useFetch={useFetch}
      className={className}
      selected={selectedProduct}
      disabled={disabled}
      renderer={CustomResults}
      onSelect={onSelect}
    />
  );
}
