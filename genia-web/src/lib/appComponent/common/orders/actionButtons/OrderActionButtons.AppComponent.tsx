import { IconImporter } from '@pitsdepot/storybook';
import { memo } from 'react';

import TextService from '#composition/textService/Text.Service';

interface OrderActionButtonsProps {
  type: 'edit' | 'save';
  onClick: (currentType: 'edit' | 'save') => void;
  isSaving: boolean;
  disabled?: boolean;
}

const textService = TextService.getText();

function PureOrderActionButtonsAppComponent({
  type,
  onClick,
  isSaving,
  disabled,
}: OrderActionButtonsProps) {
  const getButtonText = () => {
    if (isSaving) {
      return `${textService.common.saving}...`;
    }
    if (type === 'edit') {
      return textService.common.edit;
    }
    return textService.common.save;
  };

  const getIconClasses = () => {
    if (isSaving) {
      return 'text-grey-900 bg-primary rounded-full p-1 animate-spin';
    }

    if (type === 'edit') {
      return 'text-black bg-transparent rounded-full p-1 hover:bg-primary hover:text-grey-900';
    }

    return 'text-grey-900 bg-primary rounded-full p-1';
  };

  const handleButtonClick = () => {
    if (disabled) return;
    onClick(type);
  };

  return (
    <div
      className={`flex items-center gap-2 group hover:cursor-pointer hover:bg-primary hover:text-grey-900
        hover:rounded-full hover:p-2 transition-all ease-in-out ${
    disabled ? 'opacity-70 cursor-not-allowed' : ''
    }`}
      onClick={handleButtonClick}
    >
      <p className='hidden group-hover:block pl-1'>{getButtonText()}</p>
      {isSaving ? (
        <IconImporter
          data-testid="savingIndicator"
          size={24}
          name='circleNotch'
          className='text-grey-900 bg-primary rounded-full p-1 animate-spin'
        />
      ) : (
        <IconImporter
          data-testid="orderActionButton"
          size={24}
          name={type === 'edit' ? 'pencil' : 'check'}
          className={getIconClasses()}
        />
      )}
    </div>
  );
}

export const OrderActionButtonsAppComponent = memo(PureOrderActionButtonsAppComponent, (prevProps, nextProps) => (
  prevProps.type === nextProps.type
    && prevProps.isSaving === nextProps.isSaving
    && prevProps.disabled === nextProps.disabled
    && prevProps.onClick === nextProps.onClick
));
