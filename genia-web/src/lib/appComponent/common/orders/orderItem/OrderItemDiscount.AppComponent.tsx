import { Discount, DiscountBlockWrapper } from '@pitsdepot/storybook';

import { OrderItemDiscounts } from '#domain/aggregates/order/OrderItem.Entity';
import { OrderItemDiscount } from '#domain/aggregates/order/OrderItemDiscount.Entity';

function mapToInternalDiscount(
  discount: OrderItemDiscount,
): Discount {
  const required = discount.discountCategory === 'store' ? { requiredAmount: discount.required } : { requiredQuantity: discount.required };

  return {
    id: discount.id,
    discountValue: discount.discountValue,
    discountType: discount.discountType,
    priceAfterDiscount: discount.priceAfterDiscount,
    ...required,
  };
}

export function OrderItemDiscountAppComponent(
  props: {discounts: OrderItemDiscounts},
) {
  const { applied, applicable, loading } = props.discounts;

  const applicableDiscount = applicable.map((discount) => (mapToInternalDiscount(discount)));
  const appliedDiscount: Discount | undefined = applied ? mapToInternalDiscount(applied) : undefined;

  return (
    <DiscountBlockWrapper
      discounts={applicableDiscount}
      applied={appliedDiscount}
      isLoading={loading}
    />
  );
}
