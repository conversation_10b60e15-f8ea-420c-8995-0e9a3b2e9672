import {
  FormInputType, FormLabel, InputComponent,
  InputPrice,
} from '@pitsdepot/storybook';
import { useCallback, useEffect, useState } from 'react';

import { AutocompleteAppComponent } from '#appComponent/common/orders/productDropDown/ProductAutocomplete.AppComponent';
import { ProductDropdownAppComponent } from '#appComponent/common/orders/productDropDown/ProductDropdown.AppComponent';
import { ProductDropDownProduct } from '#appComponent/common/orders/productDropDown/ProductDropDown.Context';
import { OrdersTextMap } from '#composition/textService/Orders.TextMap';
import TextService from '#composition/textService/Text.Service';
import { OrderItemEntity } from '#domain/aggregates/order/OrderItem.Entity';

export interface OrderItemFormAppComponentProps {
  item: OrderItemEntity;
  onChange?: (item: OrderItemEntity) => void;
}

interface InventorySelectProductData {
  id: string;
  name: string;
  productId: string;
  image?: string;
  quantity?: string;
  unitPrice?: string;
  unitPriceAfterDiscount?: string;
  unitPriceAfterDiscountAndTaxes?: string;
}

export function OrderItemFormAppComponent(props: OrderItemFormAppComponentProps) {
  const { item, onChange } = props;
  const [name, setName] = useState(item.name?.value || '');
  const [productId, setProductId] = useState(item.productNumber?.value || '');
  const [quantity, setQuantity] = useState(item.quantity?.value?.toString() || '1');
  const [unitPrice, setUnitPrice] = useState(item.unitPrice?.value?.toString() || '0');
  const [unitPriceAfterDiscount, setUnitPriceAfterDiscount] = useState(item.unitPriceAfterDiscount?.value?.toString() || '0');
  const [unitPriceAfterDiscountAndTaxes, setUnitPriceAfterDiscountAndTaxes] = useState(item.unitPriceAfterDiscountAndTaxes?.value?.toString() || '0');
  const [image, setImage] = useState(item.image?.value || '');

  const [isManualItem, setIsManualItem] = useState(item.productNumber.disabled === false);
  const text = TextService.getText();

  useEffect(() => {
    setName(item.name?.value || '');
    setProductId(item.productNumber?.value || '');
    setQuantity(item.quantity?.value?.toString() || '1');
    setUnitPrice(item.unitPrice?.value?.toString() || '0');
    setUnitPriceAfterDiscount(item.unitPriceAfterDiscount?.value?.toString() || '0');
    setUnitPriceAfterDiscountAndTaxes(item.unitPriceAfterDiscountAndTaxes?.value?.toString() || '0');
    setImage(item.image?.value || '');
    setIsManualItem(item.productNumber.disabled === false);
  }, [JSON.stringify(item)]);

  const handleNameChange = useCallback((value: string) => {
    setName(value);
    if (onChange) {
      const newItem = item.setName({
        ...item.name,
        value,
      });
      onChange(newItem);
    }
  }, [item, onChange]);

  const handleProductIdChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setProductId(value);
    if (onChange) {
      const newItem = item.setProductNumber({
        ...item.productNumber,
        value,
      });
      onChange(newItem);
    }
  }, [item, onChange]);

  const handleQuantityChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const numberQuantity = value ? parseInt(value, 10) : 1;
    let newQuantity = value;
    if (numberQuantity < 0 || !numberQuantity) {
      newQuantity = '1';
    }

    setQuantity(newQuantity);
    if (onChange) {
      const newItem = item.setQuantity({
        ...item.quantity,
        value: Number.parseInt(newQuantity, 10),
      });
      onChange(newItem);
    }
  }, [item, onChange]);

  const handleUnitPriceChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setUnitPrice(value);
    if (onChange) {
      const newItem = item.setUnitPrice({
        ...item.unitPrice,
        value: Number.parseFloat(value),
      });
      onChange(newItem);
    }
  }, [item, onChange]);

  const handleUnitPriceAfterDiscountChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setUnitPriceAfterDiscount(value);
    if (onChange) {
      const newItem = item.setUnitPriceAfterDiscount({
        ...item.unitPriceAfterDiscount,
        value: Number.parseFloat(value),
      });
      onChange(newItem);
    }
  }, [item, onChange]);

  const onProductChange = useCallback((product: ProductDropDownProduct) => {
    setName(product.name);
    setProductId(product.productId);
    setImage(product.image || '');

    if (onChange) {
      // Verificamos si el item tiene providerCompanyId para establecer la cantidad inicial
      const hasProviderCompanyId = item.productNumber.disabled;
      const initialQuantity = hasProviderCompanyId ? 1 : 0;

      const newItem = item
        .setId(product.id)
        .setName({
          ...item.name,
          value: product.name,
        })
        .setProductNumber({
          ...item.productNumber,
          value: product.productId,
        })
        .setImage({
          ...item.image,
          value: product.image || '',
        })
        .setQuantity({
          ...item.quantity,
          value: initialQuantity,
        });
      onChange(newItem);
    }
  }, [item, onChange]);

  const onAutocompleteChange = useCallback((value: string) => {
    handleNameChange(value);
  }, [handleNameChange]);

  const onInventorySelect = useCallback((product: InventorySelectProductData) => {
    if (onChange) {
      const newItem = item
        .setInventoryRelations([{
          id: product.id,
          name: product.name,
          sku: product.productId,
          image: product.image || '',
          quantity: product.quantity ? Number.parseInt(product.quantity as string, 10) : 1,
          total: product.unitPriceAfterDiscountAndTaxes ? Number.parseInt(product.unitPriceAfterDiscountAndTaxes as string, 10) : 1,
        }])
        .setId(product.productId)
        .setName({
          ...item.name,
          value: product.name as string,
        })
        .setProductNumber({
          ...item.productNumber,
          value: product.productId as string,
        });

      onChange(newItem);
    }
  }, [item, onChange]);

  const selectedProduct: ProductDropDownProduct | null = item.id && item.name ? {
    id: item.id,
    name,
    productId,
    image,
  } : null;

  return (
    <form className="flex gap-2 items-start justify-start flex-wrap">
      <div className={'flex flex-col gap-2 flex-[4] min-w-[250px]'}>
        <FormLabel title={text.product.product}/>
        {isManualItem ? <div className='flex flex-col gap-2'>
          <AutocompleteAppComponent
            onAutocompleteChange={onAutocompleteChange}
            onSelect={onInventorySelect}
            selectedProduct={selectedProduct}
            name = 'name'
          />
          {!name && <p className='text-negative text-xs'>{text.validation.nameRequired}</p>}
        </div> : <ProductDropdownAppComponent key={item.id}
          className='text-sm'
          selectedProduct={selectedProduct}
          onSelect={onProductChange}
          disabled={item.name.disabled}/>}
      </div>
      <div className={'flex flex-col gap-2 flex-[3] min-w-[150px]'}>
        <FormLabel title={text.product.number}/>
        <InputComponent
          name="productId"
          label={text.product.number}
          disabled={item.productNumber.disabled}
          value={productId}
          onChange={handleProductIdChange}
          placeholder={text.product.number}
        />
        {item.productNumber.error && !item.productNumber.disabled
          && <p className='text-negative text-xs'>{text.orders[item.productNumber.error as keyof OrdersTextMap]}</p>}
      </div>
      <div className={'flex flex-col gap-2 flex-1 min-w-[100px]'}>
        <FormLabel title={text.orders.quantity}/>
        <InputComponent
          name="quantity"
          inputType={FormInputType.Number}
          min={1}
          value={quantity}
          onChange={handleQuantityChange}
          onFocus= {(e: React.FocusEvent<HTMLInputElement>) => e.target.select()}
          disabled={item.quantity.disabled}
        />
        {item.quantity.error && !item.quantity.disabled && <p className='text-negative text-xs'>{text.orders[item.quantity.error as keyof OrdersTextMap]}</p>}
      </div>
      <div className={'flex flex-col gap-2 flex-1 min-w-[100px]'}>
        <FormLabel title={text.orders.unitPriceShort}/>
        <InputPrice
          name="unitPrice"
          disabled={item.unitPrice.disabled}
          value={Number.parseFloat(unitPrice) || 0}
          onChange={handleUnitPriceChange}
        />
        {item.unitPrice.error && !item.unitPrice.disabled && <p className='text-negative text-xs'>{text.orders[item.unitPrice.error as keyof OrdersTextMap]}</p>}
      </div>
      <div className={'flex flex-col gap-2 flex-[2] min-w-[100px]'}>
        <FormLabel title={text.orders.discountPriceShort}/>
        <InputPrice
          name="unitPriceAfterDiscount"
          disabled={item.unitPriceAfterDiscount.disabled}
          value={Number.parseFloat(unitPriceAfterDiscount) || 0}
          onChange={handleUnitPriceAfterDiscountChange}
        />
        {item.unitPriceAfterDiscount.error && !item.unitPriceAfterDiscount.disabled && (
          <p className='text-negative text-xs'>
            {text.orders[item.unitPriceAfterDiscount.error as keyof OrdersTextMap]}
          </p>
        )}
      </div>
      <div className={'flex flex-col gap-2 flex-[2] min-w-[100px]'}>
        <FormLabel title={text.orders.unitaryTotal}/>
        <InputPrice
          name="unitPriceAfterDiscountAndTaxes"
          disabled={item.unitPriceAfterDiscountAndTaxes.disabled}
          value={Number.parseFloat(unitPriceAfterDiscountAndTaxes) || 0}
        />
      </div>
    </form>
  );
}
