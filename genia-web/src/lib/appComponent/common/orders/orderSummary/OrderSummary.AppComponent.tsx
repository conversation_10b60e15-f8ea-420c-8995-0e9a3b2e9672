import { ThreeDotsLoader } from '@pitsdepot/storybook';
import { memo, useCallback } from 'react';

import { PriceCell } from '#appComponent/table/TableCells.Component';
import TextService from '#composition/textService/Text.Service';
import { OrderSummaryValueObject } from '#domain/aggregates/order/OrderSummary.ValueObject';

export interface OrderSummaryAppComponentProps {
  summary: OrderSummaryValueObject;
}

const text = TextService.getText();

function PureOrderSummaryAppComponent(props: OrderSummaryAppComponentProps) {
  const { summary } = props;

  const itemOrLoading = useCallback((summaryItem: {hidden?: boolean, value: number, amount?: number}, title: string, className?: string) => {
    if (summaryItem.hidden) return null;

    return (
      <div key={title} className='w-full flex justify-between mt-2'>
        <p className={className || 'text-xs font-regular'}>{title}: </p>
        <div className='text-xs font-medium'>

          {summary.loading
            ? <ThreeDotsLoader/>
            : PriceCell(summaryItem.amount || summaryItem.value || 0)}
        </div>
      </div>

    );
  }, [summary.loading]);

  return (
    <div className='flex flex-col w-full'>
      {itemOrLoading(summary.subtotalBeforeDiscount, text.summary.subTotalBeforeDiscount)}
      {itemOrLoading(summary.totalDiscount, text.summary.totalDiscount)}
      {itemOrLoading(summary.subtotal, text.summary.subtotal)}
      {itemOrLoading(summary.shippingPrice, text.shipping.price)}
      <div className='w-full flex-col justify-between mt-2'>
        <p className='text-xs font-regular'>{text.summary.taxes}: </p>
        <div className='flex flex-col ml-2'>
          {summary.taxes.map((tax) => (
            itemOrLoading(tax, `${tax.name}(${tax.value}%)`)
          ))}
        </div>
      </div>
      {itemOrLoading(summary.total, text.summary.total, 'text-sm font-medium')}
    </div>
  );
}

export const OrderSummaryAppComponent = memo(
  PureOrderSummaryAppComponent,
  (prevProps, nextProps) => JSON.stringify(prevProps.summary) === JSON.stringify(nextProps.summary),
);
