import { But<PERSON>, IconImporter } from '@pitsdepot/storybook';
import {
  ReactNode,
} from 'react';

import AttributesForm from '#appComponent/common/AttributesForm.Component';
import { PROPERTIES } from '#application/catalog/modules/Catalog.Constants';
import { CATEGORIES_VALUES_EMPTY, CATEGORIES_VALUES_NO_PRESENT } from '#application/constants/texts/Attributes.Constants';
import {
  LABELS,
  TOOLTIPS,
} from '#application/constants/texts/InventoryForm.Constants';
import { CategoryAttributes } from '#application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';
import { validateArrayAttributes } from '#infrastructure/implementation/application/utils/transformAttributesData';

import { Notification } from './Notification.Component';

const { product } = TextService.getText();

interface CreateUpdateItemProps {
  children: ReactNode;
  aditionalChildren?: ReactNode;
  addButtonLabel: string;
  title: string;
  checkValidations?: () => Promise<boolean>;
  editMode: boolean;
  isLoading: boolean;
  setEditMode: (value: boolean) => void;
  attributeFormInputs: CategoryAttributes[];
  setNewAttributesForm: (props: CategoryAttributes[]) => void;
  setInputEmptyError: ((value: boolean) => void) | undefined;
  setIsloading: (value: boolean) => void;
  inputEmptyError: boolean;
  newAttributesForm: CategoryAttributes[];
  onSave: () => void;
}

const CreateUpdateItem = ({
  children,
  aditionalChildren,
  title,
  addButtonLabel,
  editMode,
  isLoading,
  setEditMode,
  attributeFormInputs,
  setNewAttributesForm,
  setInputEmptyError,
  setIsloading,
  inputEmptyError,
  checkValidations,
  newAttributesForm,
  onSave,
}: CreateUpdateItemProps) => {
  const onCheckValidations = () => {
    if (inputEmptyError) {
      Notification({ message: CATEGORIES_VALUES_EMPTY, type: MSG_ERROR_TYPES.ERROR });
      setIsloading(false);
      return true;
    }

    if (newAttributesForm?.length) {
      const validateAttribute = validateArrayAttributes(newAttributesForm, setIsloading);
      if (validateAttribute) {
        Notification({ message: CATEGORIES_VALUES_NO_PRESENT, type: MSG_ERROR_TYPES.ERROR });
        setIsloading(false);

        return true;
      }
    }

    if (checkValidations) {
      return checkValidations();
    }

    return false;
  };

  const onHandleSave = async () => {
    setIsloading(true);

    const errorValidations = await onCheckValidations();

    if (errorValidations) return;

    if (onSave) {
      onSave();
    }
  };

  return (
    <div className='w-full'>
      <div className='flex justify-between items-center mb-4'>
        <h1 className='font-semibold text-xl my-4'>
          {title}
        </h1>
        {editMode && (
          <Button
            data-testid='button-save'
            onClick={onHandleSave}
            className='self-end'
            size='small'
            disabled={isLoading}
          >
            {addButtonLabel}

          </Button>
        )}
      </div>

      <div className='flex bg-white rounded-2xl p-8 max-h-min shadow-lg'>
        <div className='rounded-2xl max-h-min w-full'>

          <div className='flex justify-between items-center'>
            <h2 className='text-lg my-4'>{PROPERTIES}</h2>

            {!editMode && <IconImporter
              data-testid = 'toggleEditionMode'
              size={24}
              name='pencilSimple'
              className="pd-text-dark-500 hover:pd-text-dark-700 pd-cursor-pointer pd-transition-all pd-ease-in-out border border-dark-400 rounded-full p-1"
              onClick={() => setEditMode(true)}
            />}
          </div>

          <div className='flex flex-wrap'>
            <div className='w-3/4 pr-7'>
              { children }
            </div>

            <div className='w-1/4'>
              <AttributesForm
                attributeFormInputs={attributeFormInputs}
                edit={editMode || false}
                onChange={(props) => setNewAttributesForm(props)}
                setError={setInputEmptyError}
                labelProps= {{
                  title: LABELS.ATTRIBUTES,
                  emptyHierarchyMessage: product.noAttributes,
                  tooltip: {
                    content: TOOLTIPS.ATTRIBUTES,
                  },
                }}
              />
            </div>

            { aditionalChildren && aditionalChildren }
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateUpdateItem;
