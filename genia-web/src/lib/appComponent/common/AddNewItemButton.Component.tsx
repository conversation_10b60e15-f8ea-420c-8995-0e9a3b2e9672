import { Button, IconImporter } from '@pitsdepot/storybook';
import { useCallback, useMemo } from 'react';

import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';

interface HeaderButtonProps {
  path: string;
  label: string;
  className?: string;
}

export const AddNewItemButton = ({ path, label, className }: HeaderButtonProps) => {
  const goToPath = CorePath.useGoToPath();

  const handleClick = useCallback(() => {
    goToPath(path);
  }, [goToPath, path]);

  const buttonContent = useMemo(
    () => (
      <div className={`flex items-center gap-2 ${className || ''}`}>
        <IconImporter name="plus" size={24} />
        <span>{label}</span>
      </div>
    ),
    [label],
  );

  return (
    <Button size='small' onClick={handleClick}>
      {buttonContent}
    </Button>
  );
};
