import { Badge, BadgeType, IconImporter } from '@pitsdepot/storybook';
import {
  useCallback,
  useMemo,
} from 'react';

import MediaAppComponent from '#appComponent/common/components/media/Media.AppComponent';
import { useMediaToRemove } from '#appComponent/common/media/MediaToRemove.Context';
import TextService from '#composition/textService/Text.Service';
import { CatalogMediaItem } from '#domain/aggregates/catalog/CatalogInventory.ValueObject';

interface CustomImageViewerProps{
  onChange: (files: (File | { id: string; url: string; name: string })[]) => void;
  catalogMedia?: CatalogMediaItem[];
  readId?: string;
  editMode?: boolean;
  entity?: string;
  entityId?: string;
}

const maxFiles = 5;
const maxMBFileSize = 2;

const CustomImageViewer = (props: CustomImageViewerProps) => {
  const {
    catalogMedia, readId, onChange, editMode, entity, entityId,
  } = props || {};

  const { media } = TextService.getText();

  const { addImageToRemove } = useMediaToRemove();

  const handleMediaChange = useCallback((files: (File | { id: string; url: string; name: string })[]) => {
    onChange(files);
  }, [onChange]);

  const initialImages = useMemo(() => catalogMedia?.map((cMedia) => ({
    id: cMedia.id,
    url: cMedia.url,
    name: 'Catalog Image',
    processing: cMedia.processing,
  })) || [], [catalogMedia]);

  const hasProcessingImages = useMemo(() => initialImages.some((img) => img.processing), [initialImages]);

  const handleImageRemove = useCallback(async (id: string) => {
    const imageExists = initialImages.some((img) => img.id === id);

    if (imageExists && entity && entityId) {
      addImageToRemove(id);
    }
  }, [initialImages, entity, entityId, addImageToRemove]);

  if (!editMode && !catalogMedia?.length) {
    return (
      <div>
        <div className={'h-full w-auto rounded-lg mt-2'}>
          <div className='flex flex-col items-center p-6 gap-2'>
            <div className='max-w-52 max-h-52 flex flex-col justify-center items-center '>
              <IconImporter name='image' className='w-24 h-24' />
              <div className='text-sm text-gray-500'>{media.noImages}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const canEdit = editMode || !readId;

  return (
    <div className="flex flex-col gap-4">
      <MediaAppComponent
        onChange={handleMediaChange}
        initialImages={initialImages}
        maxFiles={maxFiles}
        maxMBFileSize={maxMBFileSize}
        addImage={canEdit}
        showRemoveButton={canEdit}
        onImageRemove={handleImageRemove}
        banner={canEdit ? {
          title: `Agrega hasta ${maxFiles} imágenes de tu producto`,
          description: `
            Sube imágenes minimo de 500px x 500px, máximo ${maxMBFileSize}MB.
            No agregues bordes, logos ni marcas de agua. No incluyas datos de contacto, banners ni textos promocionales.
          `,
        } : undefined}
      />

      {hasProcessingImages && (
        <Badge badgeType={BadgeType.Warning}>
          {media.processingTitle}
          {media.processingMessage}
        </Badge>
      )}
    </div>
  );
};

export default CustomImageViewer;
