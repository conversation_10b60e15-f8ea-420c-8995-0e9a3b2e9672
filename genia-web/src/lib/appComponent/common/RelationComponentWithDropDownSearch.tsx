import {
  ComponentModes, OptionsDropdownProps, RelatedItem, RelationsComponent, SearchBox,
} from '@pitsdepot/storybook';
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import noImage from '#/assets/no-image.png';
import { useDropdownSearch } from '#appComponent/common/hooks/useDropdownSearch.Hook';
import { GetTableDataProps, GetTableDataResponse } from '#appComponent/table/Table.Type';
import { CustomAxiosResponse } from '#application/client/modules/ClientForm.Module';
import { ClassicDropdownInputSearch } from '#application/deprecated/ClassicDropdownInputSearch.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import { NotificableError } from '#infrastructure/implementation/application/utils/Error.Util';

import { CustomDropdownOptionRenderer, CustomDropdownOptionRendererProps } from './CustomDropdownOptionRenderer';
import { Notification } from './Notification.Component';

interface BaseItem {
  id: string;
  catalog_media?: { url: string }[];
}

interface RelationComponentWithDropDownSearchProps<T extends BaseItem = BaseItem> {
  id?: string;
  useGetHook(props: GetTableDataProps): GetTableDataResponse<T>;
  title?: string;
  initialValue?: OptionsDropdownProps[];
  useAssignHook(payload: string[]): Promise<CustomAxiosResponse>;
  useUnAssignHook(payload: string[]): Promise<CustomAxiosResponse>;
  setNewValues?: (values: OptionsDropdownProps[]) => void;
}

const itemsPerPage = 6;

export function RelationComponentWithDropDownSearch<T extends BaseItem = BaseItem>(props: RelationComponentWithDropDownSearchProps<T>) {
  const {
    id: relationId, useGetHook, title = 'Relación', initialValue, useAssignHook, useUnAssignHook, setNewValues,
  } = props;

  const [modeState, setModeState] = useState<ComponentModes>(relationId ? 'showMode' : 'disabledMode');
  const [selectedItems, setSelectedItems] = useState<(CustomDropdownOptionRendererProps & { catalog_media?: { url: string }[] })[]>(initialValue || []);
  const [imageErrorMap, setImageErrorMap] = useState<Record<string, boolean>>({});

  const {
    filteredOptions,
    handleInputChange,
    handleKeyDown,
    handleDropdownToggle,
    loadMoreOptions,
    resetSearch,
  } = useDropdownSearch<T>({
    itemsPerPage,
    getItems: (params) => useGetHook(params) as { items: T[], totalNumberOfItems: number },
    selectedItemIds: selectedItems.map((item) => item.id),
    getId: (item) => item.id,
  });

  useEffect(() => {
    if (initialValue) {
      setSelectedItems(initialValue);
    }
  }, [initialValue]);

  useEffect(() => {
    if (relationId) {
      setModeState('showMode');
    }
  }, [relationId]);

  const handleDeleteItem = useCallback((id: string) => {
    setSelectedItems((prev) => prev.filter((item) => item.id !== id));
  }, []);

  const handleChangeValue = useCallback((element: OptionsDropdownProps) => {
    setSelectedItems((prev) => [...prev, element]);
    resetSearch();
  }, [resetSearch]);

  const toggleMode = useCallback(async () => {
    if (modeState === 'editMode') {
      const assignPayload = selectedItems
        .filter((item) => !initialValue?.some((initial) => initial.id === item.id))
        .map((item) => item.id);

      const unAssignPayload = initialValue
        ?.filter((initial) => !selectedItems.some((item) => item.id === initial.id))
        .map((initial) => initial.id) || [];

      try {
        if (assignPayload.length > 0) await useAssignHook(assignPayload);
        if (unAssignPayload.length > 0) await useUnAssignHook(unAssignPayload);

        Notification({ message: 'Cambios guardados exitosamente', type: MSG_ERROR_TYPES.SUCCESS });
        setNewValues?.(selectedItems);
      } catch (error) {
        const message = error instanceof NotificableError ? error.message : 'Error al guardar los cambios';
        const type = error instanceof NotificableError ? error.type : MSG_ERROR_TYPES.ERROR;
        Notification({ message, type });
      }
    }
    setModeState((prev) => (prev === 'editMode' ? 'showMode' : 'editMode'));
  }, [modeState, selectedItems, initialValue, useAssignHook, useUnAssignHook, setNewValues]);

  const customSearchBox = (
    <SearchBox
      placeholder="Buscar productos"
      pressEnterLabel="Enter"
      onValueChange={handleInputChange}
      onKeyDown={handleKeyDown}
    />
  );

  const handleImageError = useCallback((id: string) => {
    setImageErrorMap((prev) => ({ ...prev, [id]: true }));
  }, []);

  const optionsWithImage = useMemo(
    () => filteredOptions?.map((item) => {
      const { id } = item;
      const imageUrl = imageErrorMap[id]
        ? noImage
        : item.catalog_media?.[0]?.url || noImage;
      return {
        ...item,
        src: imageUrl,
        onImageError: () => handleImageError(id),
      };
    }),
    [filteredOptions, imageErrorMap, handleImageError],
  );

  const selectedItemsMapped = useMemo(
    () => selectedItems.map((item) => ({
      id: item.id,
      title: item.name || '',
      name: item.name || '',
      readId: item.readId || '',
      src: item?.catalog_media?.[0]?.url || '',
      onRemoveItem: () => handleDeleteItem(item.id),
      renderer: CustomDropdownOptionRenderer,
    })),
    [selectedItems, handleDeleteItem],
  );

  return (
    <RelationsComponent
      className="!w-1/4"
      relatedItems={selectedItemsMapped as RelatedItem[]}
      header={{ title: `Relación de ${title}` }}
      noItemsLabel={`${relationId ? `Por favor agrega ${title}` : `Agrega un descuento para relacionar ${title}`}`}
      mode={modeState}
      togleModeButton={toggleMode}
    >
      <ClassicDropdownInputSearch
        wrapperClassName="px-2 h-[38px] rounded-xl relative bg-white z-50 border w-full"
        contentClassName="flex justify-between items-center py-[9px] text-xsm"
        containerClassName=" "
        placeholder="Seleccionar"
        allOptions={optionsWithImage}
        onChangeValue={handleChangeValue}
        customInputSearch={customSearchBox}
        disabled={modeState === 'showMode'}
        customRenderer={CustomDropdownOptionRenderer}
        onDropdownToggle={handleDropdownToggle}
        onLoadMore={loadMoreOptions}
      />
    </RelationsComponent>
  );
}
