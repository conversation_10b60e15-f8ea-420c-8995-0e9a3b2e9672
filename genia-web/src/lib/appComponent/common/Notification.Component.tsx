import { toast } from 'react-toastify';

import NotificationPropTypes from '#application/types/Notification.Type';
import 'react-toastify/dist/ReactToastify.css';

const notifications:Record<number, boolean> = {};

export const Notification = ({
  message,
  type,
}: NotificationPropTypes): void => {
  const hashString = (str: string): number => {
    let hash = 0;
    for (let i = 0; i < str.length; i += 1) {
      const char = str.charCodeAt(i);
      // eslint-disable-next-line no-bitwise
      hash = ((hash << 5) - hash) + char;
      // eslint-disable-next-line no-bitwise
      hash &= hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  };

  const id = hashString(message + type);

  if (notifications[id]) {
    return;
  }

  notifications[id] = true;
  toast(message, { type });
  setTimeout(() => {
    delete notifications[id];
  }, 100);
};
