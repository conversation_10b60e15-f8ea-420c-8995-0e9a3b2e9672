import { Avatar, OptionsDropdownProps } from '@pitsdepot/storybook';

import { getRandomFadedColor } from '#infrastructure/implementation/application/utils/getRandomFadedColor';

export interface CustomDropdownOptionRendererProps extends OptionsDropdownProps {
  readId?: string;
  sku?: string;
  onImageError?: () => void;
}

export const CustomDropdownOptionRenderer = (item: CustomDropdownOptionRendererProps) => {
  const {
    id, src, name, readId, sku, onImageError,
  } = item;

  const bgColor = getRandomFadedColor(id);

  return (
    <div className='flex items-center gap-2 py-2'>
      <div className='w-9 h-9'>
        <Avatar
          initialsBackground={bgColor}
          size={36}
          src={src}
          name={name}
          onImageError={onImageError}
        />
      </div>
      <div className='flex flex-col text-sm'>
        <div className='font-medium line-clamp-1'>{name}</div>
        <div className='text-xxsm font-light line-clamp-1'>{readId || sku}</div>
      </div>
    </div>
  );
};
