import { IconImporter } from '@pitsdepot/storybook';
import { useRef } from 'react';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';

const text = TextService.getText();

const CustomImageInputWithOutFunctionality = ({ disabled }: { disabled?: boolean }) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const handleClick = () => {
    Notification({ message: text.common.temporarilyUnavailable, type: MSG_ERROR_TYPES.ERROR });
  };

  return (
  // TODO: Add image preview and implement image upload
    <div>
      <input type="file"
        className='hidden'
        ref={fileInputRef}
        disabled={disabled || false}
            />
      <div className={`h-full w-auto border rounded-lg mt-2 ${disabled ? 'cursor-not-allowed bg-lightGray' : 'bg-white cursor-pointer'}`}>
        <div className='flex flex-col items-center p-6 gap-2'>
          <div className='max-w-52 max-h-52 flex justify-center items-center '>
            <IconImporter name='image' size={100} weight='thin' />
          </div>
          {!disabled && <p
            className='text-toscaBlue cursor-pointer'
            onClick={() => (disabled ? null : handleClick())}>Click to upload <span className='text-dark-600'>an image or drag a file</span></p>}
        </div>
      </div>
    </div>
  );
};

export default CustomImageInputWithOutFunctionality;
