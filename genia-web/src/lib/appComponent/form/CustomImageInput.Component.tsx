import { useCallback } from 'react';

import MediaAppComponent from '#appComponent/common/components/media/Media.AppComponent';

interface CustomImageInputProps {
  onChange: (files: (File | { id: string; url: string; name: string })[]) => void;
  initialImages: { id: string; url: string; name: string }[];
  maxFiles?: number;
  maxMBFileSize?: number;
}

const CustomImageInput = (props: CustomImageInputProps) => {
  const {
    onChange, initialImages = [], maxFiles = 5, maxMBFileSize = 2,
  } = props;
  const handleMediaChange = useCallback((files: (File | { id: string; url: string; name: string })[]) => {
    onChange(files);
  }, []);

  return (
    <div className='w-full'>
      <MediaAppComponent
        onChange={handleMediaChange}
        initialImages={initialImages}
        maxFiles={maxFiles}
        maxMBFileSize={maxMBFileSize}
      />
    </div>
  );
};

export default CustomImageInput;
