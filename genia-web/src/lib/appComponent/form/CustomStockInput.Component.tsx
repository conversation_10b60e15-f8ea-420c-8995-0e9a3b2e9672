import { useEffect, useState } from 'react';

import { CustomStockInputProps } from '#appComponent/form/Form.Type';

export const CustomStockInput = ({
  onChange,
  value = 0,
  name,
  checkboxPosition,
  checkBoxLabel,
  inputClassName,
  onCheckboxChange,
  placeholder,
  nonStock = false,
  initialCheck = true,
  disabled,
  ...props
}: CustomStockInputProps) => {
  const [inputValue, setInputValue] = useState<number | ''>(value || '');
  const [isChecked, setIsChecked] = useState<boolean>(initialCheck);

  useEffect(() => {
    setInputValue(value || 0);
  }, [value]);

  useEffect(() => {
    setIsChecked(initialCheck);
  }, [initialCheck]);

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
    if (onCheckboxChange) onCheckboxChange(!isChecked);
    if (onChange) onChange({ target: { value: !isChecked, name: 'hasStockValidation' } } as unknown as React.ChangeEvent<HTMLInputElement>);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (newValue === '' || !Number.isNaN(Number(newValue))) {
      setInputValue(newValue === '' ? '' : Number(newValue));
    }
    if (onChange) onChange(e);
  };

  return (
    <div className={`${!nonStock ? 'flex flex-col gap-2 mt-2' : 'flex justify-between items-center'}`}>
      {!nonStock ? <input
        type='number'
        className={`${checkboxPosition === 'top'
        // eslint-disable-next-line max-len
          ? 'order-2' : 'order-1'} mt-2 w-full h-[38px] border rounded-lg p-2 selection:bg-fadedBlue focus-visible:outline-1 focus-visible:outline-positive text-xsm disabled:text-dark-600 disabled:cursor-not-allowed ${inputClassName}`}
        onChange={handleInputChange}
        value={inputValue}
        name={name}
        placeholder={placeholder}
        min={0}
        {...props}
      /> : <p className='text-xl text-dark-500 font-medium'>{inputValue}</p>}
      <div className={`${checkboxPosition === 'top' ? 'order-1' : 'order-2'} flex justify-end`}>
        <label htmlFor={`${name}-checkbox`} className="flex gap-1 text-xsm text-dark-500 items-center">
          {checkBoxLabel || 'Disable'}
          <input
            id={`${name}-checkbox`}
            type="checkbox"
            className="w-4 h-4 text-darkBlue focus:ring-darkBlue disabled:cursor-not-allowed"
            onChange={handleCheckboxChange}
            checked={isChecked}
            disabled={disabled || false}
          />
        </label>
      </div>
    </div>
  );
};
