export type CustomInputWithCheckboxProps = {
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  value: string | null;
  name: string;
  checkboxPosition?: string;
  checkBoxLabel?: string;
  switchLabel?: string;
  inputClassName?: string;
  onCheckboxChange?: (isChecked: boolean) => void;
  placeholder?: string;
  classNameContainer?: string;
  className?: string;
  disabled?: boolean;
};

export type CustomStockInputProps = Omit<CustomInputWithCheckboxProps, 'value'> & {
  value: number | null;
  initialCheck?: boolean;
  nonStock?: boolean;
};
