import { useEffect, useState } from 'react';

import { CustomInputWithCheckboxProps } from '#appComponent/form/Form.Type';

const CustomSkuInput = ({
  onChange,
  value,
  name,
  checkBoxLabel,
  inputClassName,
  onCheckboxChange,
  placeholder,
  disabled,
  ...props
}: CustomInputWithCheckboxProps) => {
  const [skuInputValue, setSkuInputValue] = useState<string>(value || '');
  const [skuValue, setSkuValue] = useState<string | null>(value);
  const [isChecked, setIsChecked] = useState<boolean>(Boolean(value));
  const [inputInit, setInputInit] = useState<boolean>(false);

  useEffect(() => {
    setSkuValue(value);
    if (!inputInit && value) {
      setInputInit(true);
      setSkuInputValue(value);
    }
  }, [value]);

  const handleCheckboxChange = () => {
    const nextCheckValue = !isChecked;
    setIsChecked(nextCheckValue);
    const sku = nextCheckValue ? null : skuInputValue;
    setSkuValue(sku);
    if (onCheckboxChange) onCheckboxChange(!isChecked);
    if (onChange) onChange({ target: { value: sku, name } } as React.ChangeEvent<HTMLInputElement>);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSkuInputValue(e.target.value.trim());
    if (onChange) onChange({ target: { value: e.target.value.trim(), name } } as React.ChangeEvent<HTMLInputElement>);
  };

  return (
    <div className="flex flex-col gap-2 mt-2">
      <input
        disabled={disabled || isChecked}
        type='text'
        // eslint-disable-next-line max-len
        className={`w-full mt-2 h-[38px] text-xsm border rounded-lg p-2 selection:bg-fadedBlue focus-visible:outline-1 focus-visible:outline-positive disabled:text-dark-600 disabled:cursor-not-allowed ${inputClassName}`}
        onChange={handleInputChange}
        value={skuValue || ''}
        name={name}
        placeholder={placeholder}
        {...props}
      />
      <div className="flex justify-end">
        <label htmlFor={`${name}-checkbox`} className="flex gap-1 text-xsm text-dark-500 items-center">
          {checkBoxLabel || 'Disable'}
          <input
            id={`${name}-checkbox`}
            type="checkbox"
            className="w-4 h-4 text-darkBlue focus:ring-darkBlue"
            onChange={handleCheckboxChange}
            checked={isChecked}
            disabled={disabled || false}
          />
        </label>
      </div>
    </div>
  );
};

export default CustomSkuInput;
