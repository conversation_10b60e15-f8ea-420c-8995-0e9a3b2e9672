import { useEffect, useState } from 'react';

interface CustomCheckBoxProps {
  onCheckBoxChange: (isChecked: boolean) => void;
  isChecked: boolean;
  disabled?: boolean;
  label?: string;
}

const CustomCheckBox = (props: CustomCheckBoxProps) => {
  const {
    onCheckBoxChange, isChecked, disabled, label = 'Activar',
  } = props;

  const [internalChecked, setInternalChecked] = useState(isChecked || false);

  useEffect(() => {
    setInternalChecked(isChecked);
  }, [isChecked]);

  const handleCheckBoxChange = () => {
    const nextCheckValue = !internalChecked;
    setInternalChecked(nextCheckValue);
    if (onCheckBoxChange) onCheckBoxChange(nextCheckValue);
  };
  return (
    <div className='flex items-center flex-1 justify-end gap-1'>
      <p className='text-dark-500 text-xxsm leading-none'>{label}</p>
      <input
        type="checkbox"
        onChange={handleCheckBoxChange}
        checked={internalChecked}
        disabled={disabled || false}
      />
    </div>
  );
};

export default CustomCheckBox;
