import { useEffect, useState } from 'react';

import { CustomStockInputProps } from '#appComponent/form/Form.Type';

const CustomEnableDisableInput = ({
  onChange,
  onCheckboxChange,
  initialCheck = false,
  classNameContainer = 'flex flex-col gap-2 mt-2',
  className = 'flex justify-end items-center',
  disabled,
}: CustomStockInputProps) => {
  const [isChecked, setIsChecked] = useState<boolean>(initialCheck);

  useEffect(() => {
    setIsChecked(initialCheck);
  }, [initialCheck]);

  const handleSwitchChange = () => {
    setIsChecked(!isChecked);
    if (onCheckboxChange) onCheckboxChange(!isChecked);
    if (onChange) onChange({ target: { value: !isChecked, name: 'active' } } as unknown as React.ChangeEvent<HTMLInputElement>);
  };

  return (
    <div className={classNameContainer}>
      <div className={className}>
        <span className='mr-4 text-xs text-dark-500'>{isChecked ? 'Activo' : 'Inactivo'}</span>
        <label
          // eslint-disable-next-line max-len
          className={`relative inline-flex items-center h-6 rounded-full w-11 bg-gray-300 border-2 border-gray-300 transition duration-200 ease-in-out ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
          <input
            type="checkbox"
            className="sr-only"
            checked={isChecked || false}
            onChange={handleSwitchChange}
            disabled={disabled || false}
          />
          <span
            className={`rounded-full shadow-inner absolute left-0 transform transition duration-200 ease-in-out h-5 w-5 
            ${isChecked ? 'translate-x-5 bg-toscaBlue' : 'translate-x-0 bg-gray-100'}`}
          />
        </label>
      </div>
    </div>
  );
};

export default CustomEnableDisableInput;
