import {
  FormComponent,
  FormInput,
} from '@pitsdepot/storybook';
import { useEffect } from 'react';

import { CatalogDiscountItem, StoreDiscountItem } from '#application/deprecated/DashboardPages.Type';

interface AddNewDiscountProps {
  setNewDiscount: (discount: Partial<CatalogDiscountItem | StoreDiscountItem>) => void;
  formState: Partial<CatalogDiscountItem | StoreDiscountItem>;
  inputs: FormInput[];
}

const AddNewDiscount = (props: AddNewDiscountProps) => {
  const {
    setNewDiscount, formState, inputs,
  } = props;

  useEffect(() => {
    if (setNewDiscount) {
      setNewDiscount(formState);
    }
  }, [formState]);

  return (
    <FormComponent
      inputs={inputs}
      orientation='horizontal'
      columnsNumber={2}
    />
  );
};

export default AddNewDiscount;
