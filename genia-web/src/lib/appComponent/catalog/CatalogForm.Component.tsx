import { FormComponent } from '@pitsdepot/storybook';
import { useEffect } from 'react';

import { CatalogItem } from '#application/catalog/Catalog.Type';
import { NewFormCatalogProps } from '#application/deprecated/DashboardPages.Type';
import { useCatalogInputs } from '#infrastructure/implementation/application/hooks/useCatalogInputs.Hook';

interface CatalogFormProps {
  editMode: boolean;
  defaultInputs:Partial<CatalogItem>;
  handleChildStateChange: (state: NewFormCatalogProps) => void;
  formValidation?: (error: boolean) => void;
  dataErrorName?: string;
}

const CatalogForm = ({
  editMode,
  defaultInputs,
  handleChildStateChange,
  formValidation,
  dataErrorName,
}: CatalogFormProps) => {
  const {
    inputs, formState, isIdAutoGenerated, isTaxEnable, images,
  } = useCatalogInputs(defaultInputs, editMode);

  useEffect(() => {
    handleChildStateChange({
      ...formState, isIdAutoGenerated, isTaxEnable, images,
    } as NewFormCatalogProps);
  }, [JSON.stringify(formState), isIdAutoGenerated, isTaxEnable, images]);

  return (
    <FormComponent
      inputs={inputs}
      orientation='horizontal'
      columnsNumber={2}
      formValidation={formValidation}
      dataErrorName={dataErrorName}
    />
  );
};

export default CatalogForm;
