import {
  Badge,
  BadgeType,
  IconImporter,
  IconName,
  Popover,
  RouteLink,
  TextAlign,
  Tooltip,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { Link } from 'react-router-dom';

import { Notification } from '#/lib/appComponent/common/Notification.Component';
import { Client } from '#application/client/Client.Type';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

const {
  common, inventory, product, clients, providers, results,
} = TextService.getText();

const QUOTE_BTN_LABEL = 'Cotizar';
const QUOTE_WSP_NUMBER = '525575246342';

interface ProductProps {
  name: string;
  imgUrl: string;
  id: string;
}

export interface CategoryProps {
  key: string;
  values: string[];
  categories?: CategoryProps[];
}

const extractKeysAndValues = (categories: CategoryProps[], level = 0): { key: string; level: number; values: string[] }[] => {
  const result: { key: string; level: number; values: string[] }[] = [];

  const traverse = (category: CategoryProps, currentLevel: number) => {
    result.push({
      key: category.key,
      level: currentLevel,
      values: category.values || [],
    });

    if (category.categories) {
      category.categories.forEach((cat) => traverse(cat, currentLevel + 1));
    }
  };

  categories.forEach((cat) => traverse(cat, level));
  return result;
};

const getColorByValue = (value: string) => {
  const colors: { [key: string]: string } = {
    product: Theme.colors.primary,
    service: Theme.colors.orange,
    amount: Theme.colors.toscaBlue,
    percentage: Theme.colors.positive,
    bundle: Theme.colors.toscaBlue,
  };

  return colors[value] || Theme.colors.toscaBlue;
};

export const InventoryProductCell = ({ name, imgUrl, id }: ProductProps) => {
  const route = ApplicationRegistry.PathService.inventory.viewInventory(id);

  return (
    <div className='flex gap-4 hover:cursor-pointer'>
      <img src={imgUrl} alt={name} className='w-11 h-11' />
      <RouteLink to={route}
        className='self-center w-full line-clamp-2 !text-dark-600 hover:underline'
        as={Link}
        size='sm'>{name}</RouteLink>
    </div>
  );
};

export const CatalogProductCell = ({ name, imgUrl, id }: ProductProps) => {
  const route = ApplicationRegistry.PathService.catalog.viewCatalog(id);

  return (
    <div className='flex gap-4 hover:cursor-pointer'>
      <img src={imgUrl} alt={name} className='w-11 h-11' />
      <RouteLink to={route}
        className='self-center w-full line-clamp-2 !text-dark-600 hover:underline'
        as={Link}
        size='sm'>{name}</RouteLink>
    </div>
  );
};

export const DescriptionProductCell = (description: string) => (
  !description
    ? <p className='text-xxsm font-light'>N/D</p>
    : <p title={description} className='self-center text-xxsm font-light line-clamp-3 cursor-context-menu'>{description}</p>
);

export const GenericCellWithRedirect = ({ name, path }: {name: string, path: string}) => (
  <RouteLink
    to={path}
    color={Theme.colors.dark[600]}
    as={Link}
    className='hover:underline line-clamp-3'
    size='sm'
  >
    {name}
  </RouteLink>
);

export const StockCell = ({ value, validation }: { value: number, validation: boolean}) => {
  if (validation) {
    return (
      <Tooltip content={inventory.stockValidationDisabled}>
        <IconImporter name='warning' color={Theme.colors.dark[400]} size={24} />
      </Tooltip>
    );
  }
  if (value <= 5) {
    return (
      <div className='flex gap-2 items-center'>
        <span>{value}</span>
        <Tooltip
          content={value === 0 ? inventory.stockEmptyTooltip : inventory.stockDangerTooltip}
          bgColor={Theme.colors.fadedRed}
        >
          <Badge badgeType={BadgeType.Danger} />
        </Tooltip>
      </div>
    );
  }

  if (value <= 10) {
    return (
      <div className='flex gap-2 items-center'>
        <span>{value}</span>
        <Tooltip
          content={inventory.stockWarningTooltip}
          bgColor={Theme.colors.fadedYellow}
        >
          <Badge badgeType={BadgeType.Warning} />
        </Tooltip>
      </div>);
  }
  return <span>{value}</span>;
};

export const AttributesCell = (value?: CategoryProps[]) => {
  const keysAndValues = extractKeysAndValues(value || []);
  const levelColors = {
    0: Theme.colors.fadedGreen,
    1: Theme.colors.fadedRed,
    2: Theme.colors.fadedViolet,
    3: Theme.colors.fadedPeach,
  };

  const allBadges = keysAndValues.flatMap(({ key, level, values }, i) => {
    const filteredValues = values.filter((val) => val && val.trim() !== '');

    if (filteredValues.length === 0) {
      return [{
        content: key,
        color: levelColors[level as keyof typeof levelColors] || levelColors[3],
        key: `key-${key}-${level}-${i}`,
      }];
    }

    return [
      {
        content: key,
        color: levelColors[level as keyof typeof levelColors] || levelColors[3],
        key: `key-${key}-${level}-${i}`,
      },
      ...filteredValues.map((val, index) => ({
        content: val,
        color: Theme.colors.fadedYellow,
        key: `val-${key}-${index}-${i}`,
      })),
    ];
  });

  const MAX_VISIBLE_BADGES = 4;
  const initialBadges = allBadges.slice(0, MAX_VISIBLE_BADGES);
  const remainingBadges = allBadges.slice(MAX_VISIBLE_BADGES);
  const hasMoreBadges = remainingBadges.length > 0;

  if (!value || value.length === 0) {
    return <p className='text-dark-500 italic font-regular text-xs'>{product.noAttributes}</p>;
  }

  const renderAllBadges = (
    <div className='flex gap-1 flex-wrap' style={{ width: 'fit-content', maxWidth: '100%' }}>
      {remainingBadges.map((badge) => (
        <Badge key={badge.key} className='my-2 !w-fit whitespace-nowrap' color={badge.color} typeof='default'>
          {badge.content}
        </Badge>
      ))}
    </div>
  );

  return (
    <div className='flex flex-col gap-1 items-start'>
      <div className='flex gap-2 flex-wrap self-start'>
        {initialBadges.map((badge) => (
          <Badge
            key={badge.key}
            typeof='default'
            color={badge.color}
            className='font-regular !text-[10px] !w-fit'
          >
            {badge.content}
          </Badge>
        ))}
      </div>
      {hasMoreBadges && (
        <Popover
          content={renderAllBadges}
          position="bottom"
          maxHeight="230px"
          hasScroll={true}
          maxWidth= '240px'
        >
          <span
            className="text-xxsm text-toscaBlue cursor-pointer self-start mt-1"
          >
            {`${common.loadMore} (${remainingBadges.length})`}
          </span>
        </Popover>
      )}
    </div>
  );
};

export const StatusCell = (value: boolean) => (
  <Badge badgeType={value !== null ? BadgeType.Disabled : BadgeType.Active} className='!w-fit mr-auto' />
);

export const RequiresStockCell = (value: boolean) => (
  <Badge badgeType={value ? BadgeType.Active : BadgeType.Disabled} className='!w-fit mx-auto' />
);

export const PriceCell = (value: number, className: string = '') => {
  const formattedValue = value.toLocaleString('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return <p className={`text-start mr-4 font-medium ${className}`}>{formattedValue}</p>;
};

export const RedirectButtonWithLogo = ({
  children, url, iconName, className,
}: { children?: React.ReactNode, url?: string, iconName?: IconName, className?: string }) => (
  <a
    className={
      `flex gap-1 bg-[#25d366] text-xxsm rounded-xl text-white px-2 py-1 
      justify-center items-center cursor-pointer transition-all ease-in-out 
      border border-[#25d366] hover:bg-white hover:text-[#25d366] ${className}`
    }
    href={url || ''}
    target='_blank'
    rel="noopener noreferrer"
  >
    <IconImporter size={20} name={iconName || 'whatsApp'} />
    {children}
  </a>
);

export const PriceCellWithButton = ({
  price, name, readId, description,
}: {price: number, name: string, readId: string, description: string}) => {
  const WSPMESSAGE = encodeURIComponent(`Hola, estoy interesado en cotizar: \nProducto: ${name} \nCódigo ${readId} \nDescripción: ${description}\nMuchas gracias.`);

  return (
    price === 0
      ? <RedirectButtonWithLogo
          url={`https://api.whatsapp.com/send?phone=+${QUOTE_WSP_NUMBER}&text=${WSPMESSAGE}`}
      >
        {QUOTE_BTN_LABEL}
      </RedirectButtonWithLogo>
      : <div className='text-end mr-4'>{`$${price}`}</div>
  );
};

export const ProductTypeCell = (value: string) => (
  <Badge
    badgeType={BadgeType.Outline}
    color={getColorByValue(value)}
    className='capitalize !w-fit mx-auto !text-xxsm'
  >{value}</Badge>
);

export const TotalItemsProvidedCell = (value: number) => (
  value === 0 || !value ? <p className='text-dark-500 italic'>{providers.noItemsProvided}</p> : <p>{results.items} {value}</p>
);

export const TributaryIdCell = (value: string) => (
  !value ? <p className='text-dark-500 italic'>{clients.noTributaryId}</p> : <p>{value}</p>
);

export const CatalogDiscountProductCell = (value: {name: string}, align?: TextAlign) => {
  const { name } = value || {};

  const justifyAlign: {
    center?: string;
    left?: string;
    right?: string;
    justify?: string;
    char?: string;
  } = {
    center: 'justify-center',
    left: 'justify-start',
    right: 'justify-end',
    justify: 'justify-between',
    char: 'justify-around',
  };

  return (
    <div className={`flex gap-4 ${align ? justifyAlign[align] : ''}`}>
      <p className='self-center line-clamp-2'>{name}</p>
    </div>
  );
};

export const GenericCell = (value: string | number, align?: TextAlign) => (
  <div className={`flex gap-4 ${align ? `justify-${align}` : ''}`}>
    <p className='self-center line-clamp-2 truncate text-xs'>{value}</p>
  </div>
);

export const StoreDiscountsClientsCell = (discountClient: Pick<Client, 'name' | 'id'>[]) => {
  // TODO: Implement Avatar component when available
  const visibleClients = discountClient.slice(0, 3);
  const hasMoreClients = discountClient.length > 3;
  return (
    <div className='flex flex-row gap-2 align-left'>
      <div className='flex gap-2 flex-wrap'>
        {visibleClients.map((client) => {
          const { name, id } = client;
          return (
            <Badge
              key={id}
              className='!w-fit'
            >
              {name}
            </Badge>
          );
        })}
      </div>
      {hasMoreClients && (
        <div
          onClick={() => Notification({ message: common.temporarilyUnavailable, type: MSG_ERROR_TYPES.WARNING })}
          className="text-sm text-toscaBlue cursor-pointer mx-auto"
        >
          {common.loadMore}
        </div>
      )}
    </div>
  );
};

export const DiscountValueCell = ({ value, type }: {value: number, type: string}) => (
  <p className='text-end mr-4'>{`${type === 'amount' ? '$' : ''}${value}${type === 'percentage' ? '%' : ''}`}</p>
);

export const AnchorCell = ({ value, url, type = 'underline' }: { value: string, url: string, type?: 'underline' | 'background' }) => (
  <RouteLink
    to={url}
    className={`self-center w-full line-clamp-2 !text-dark-600 ${type === 'underline' ? 'hover:underline' : '!font-normal'}`}
    as={Link}
    size='sm'
  >
    {value}
  </RouteLink>
);
