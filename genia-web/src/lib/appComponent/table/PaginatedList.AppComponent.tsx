import {
  Column,
  FilterGroup,
  FilterProps,
  Pagination, Row, Table, Title,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import { UrlParams } from '#composition/Common.Type';
import TextService from '#composition/textService/Text.Service';
import { tableTotalPages } from '#infrastructure/implementation/application/utils/TableTotalPages';

import { GetTableDataProps, GetTableDataResponse, SearchFilter } from '../../application/deprecated/DashboardPages.Type';
import { MSG_ERROR_TYPES } from '../../application/types/Notification.Type';
import { usePagination } from '../../infrastructure/implementation/application/hooks/usePagination.Hook';
import { Notification } from '../common/Notification.Component';

// Obteniendo textos del servicio
const { common } = TextService.getText();

export interface PaginatedListAppComponentProps<T, Q extends string = string> {
  useGetHook(props: GetTableDataProps): GetTableDataResponse<T>;
  mapper(items: T[]): Row[];
  title: string;
  actions? : {[key: string]: (id: string | number) => void};
  columns: Column[];
  searchTitle?: string;
  filters?: FilterGroup<Q>[];
  headerButton?: React.ReactNode;
  topRightButton?: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  id?: string;
  orderBy?: string;
  setUrlParams?: (params: UrlParams) => void;
  filtersFromUrl?: SearchFilter;
}

export function PaginatedListAppComponent<T, Q extends string = string>(props: PaginatedListAppComponentProps<T, Q>) {
  const {
    useGetHook,
    mapper,
    actions,
    columns,
    title,
    searchTitle,
    filters,
    headerButton,
    topRightButton,
    className = '',
    showHeader = true,
    id,
    orderBy,
    setUrlParams,
    filtersFromUrl,
  } = props;

  const itemsPerPage = 10;
  const [selectedFilters, setSelectedFilters] = useState <SearchFilter>({});
  const [filterProps, setFilterProps] = useState<FilterProps<Q> | undefined>();

  const [rows, setRows] = useState<Row[]>([]);

  const {
    currentPage, offset, handlePageChange, setCurrentPage,
  } = usePagination(itemsPerPage);

  const defaultOrderBy = orderBy || 'createdAt';
  const order = 'desc';
  const [searchInput, setSearchInput] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const {
    loading, error, items, totalNumberOfItems, refetch,
  } = useGetHook({
    limit: itemsPerPage, offset, orderBy: defaultOrderBy, order, searchTerm: searchValue, filters: selectedFilters, id,
  });

  useEffect(() => {
    if (refetch) {
      refetch();
    }
  }, []);

  useEffect(() => {
    if (filtersFromUrl) {
      setSelectedFilters(filtersFromUrl);
    }
  }, [JSON.stringify(filtersFromUrl)]);

  useEffect(() => {
    if (items) setRows(mapper(items));
  }, [JSON.stringify(items)]);

  useEffect(() => {
    if (error) {
      Notification({ message: common.loadingErrorNotification, type: MSG_ERROR_TYPES.ERROR });
    }
  }, [error]);

  useEffect(() => {
    if (searchValue) {
      setCurrentPage(1);
    }
  }, [searchValue]);

  useEffect(() => {
    if (filters?.length) {
      setFilterProps({
        filters,
        onApply: (newFilters) => {
          const newSelection = newFilters.reduce((map, { key, options }) => ({
            ...map, [key]: options.filter((o) => o.selected).map((o) => o.id),
          }), {});

          setUrlParams?.(newSelection);
          setSelectedFilters(newSelection);
        },
      });
    }
  }, [JSON.stringify(filters)]);

  const handleInputChange = (value: string) => {
    setSearchInput(value);
    if (value.trim() === '') {
      setSearchValue('');
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const trimmedInput = searchInput.trim();

      if (trimmedInput.length < 1) {
        Notification({ message: common.minimumSearchLength, type: MSG_ERROR_TYPES.WARNING });
        return;
      }
      setSearchValue(trimmedInput);
    }
  };

  const searchBox = {
    placeholder: searchTitle,
    value: searchInput,
    onValueChange: handleInputChange,
    onKeyDown: handleKeyDown,
    className: '!w-96',
    searchValue,
    pressEnterLabel: 'Enter para buscar',
  };

  const isError = !loading && error && <p>{common.loadingErrorMsg}</p>;
  const noMatchesFound = rows?.length === 0 && searchValue !== '' && <div className='flex flex-col gap-4 items-center'>
    <p className='text-2xl text-center'>{common.noMatchesFoundHeader}<span className='italic font-light'>{`"${searchValue}".`}</span></p>
    <p className='text-dark-500'>{common.noMatchesFoundBody}</p>
  </div>;

  return (
    <div className={`flex flex-col w-full pt-0 gap-7 ${className}`}>
      {showHeader && (
        <div className='flex justify-between'>
          <Title as='h2'>{title}</Title>
          { headerButton }
        </div>
      )}

      {isError}
      {!error && (
        <div className='flex flex-col gap-4 bg-white rounded-2xl p-8 shadow-sm'>
          <Table
            columns={columns}
            rows={rows}
            totalItems={totalNumberOfItems}
            actions={actions}
            { ...(searchTitle && { searchBox }) }
            totalItemsLabel={title}
            actionsLabel={common.actions}
            selectTable = {!!actions}
            isLoading={loading}
            filter={filterProps}
            boxShadow='none'
            classname='!p-0'
            topRightButton={topRightButton}
          />
          {noMatchesFound}
          {rows?.length > 0 && <Pagination
            initialPage={currentPage}
            totalPages={tableTotalPages(totalNumberOfItems, itemsPerPage)}
            onPageChange={handlePageChange}
            isLoading={loading}
            className='pt-2'
          />}
        </div>
      )}
    </div>
  );
}
