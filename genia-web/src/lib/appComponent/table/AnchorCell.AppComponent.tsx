import { RouteLink } from '@pitsdepot/storybook';

import ReactDomRouteLinkRegistry from '#composition/ReactDomRouteLink.Registry';

export const AnchorCell = ({ value, url, type = 'underline' }: { value: string, url: string, type?: 'underline' | 'background' }) => {
  const linkAs = ReactDomRouteLinkRegistry.getLinkAs();

  return (
    <RouteLink
      to={url}
      className={`self-center w-full line-clamp-2 !text-dark-600 ${type === 'underline' ? 'hover:underline' : '!font-normal'}`}
      as={linkAs}
      size='sm'
    >
      {value}
    </RouteLink>
  );
};
