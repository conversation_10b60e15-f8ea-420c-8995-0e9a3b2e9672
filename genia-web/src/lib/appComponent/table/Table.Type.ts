export type SearchFilter = {
  [key: string]: string[];
};

export interface GetTableDataProps<T extends SearchFilter = SearchFilter> {
  limit: number;
  offset: number;
  orderBy: string;
  order: string;
  searchTerm?: string;
  filters?: T;
  id?: string;
  providerId?: string;
}

export interface GetTableDataResponse<T> {
  totalNumberOfItems: number;
  items: T[];
  loading: boolean;
  error: Error | string | null;
  refetch: () => void;
}
