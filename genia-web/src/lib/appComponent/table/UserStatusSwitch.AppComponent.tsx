import { Button, IconImporter } from '@pitsdepot/storybook';
import { useCallback, useState } from 'react';

import CustomEnableDisableInput from '#appComponent/form/CustomEnableDisableInput.Component';
import TextService from '#composition/textService/Text.Service';

interface UserStatusSwitchProps {
  userId: string;
  currentState: 'ACTIVE' | 'DISABLED';
  isLoading?: boolean;
  onStatusChange: (userId: string, newState: 'ACTIVE' | 'DISABLED') => void;
}

export function UserStatusSwitchAppComponent({
  userId,
  currentState,
  isLoading = false,
  onStatusChange,
}: UserStatusSwitchProps) {
  const [currentStatus, setCurrentStatus] = useState(currentState === 'ACTIVE');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<boolean | null>(null);
  const text = TextService.getText();

  const handleStatusChange = useCallback((newStatus: boolean) => {
    // Mostrar modal de confirmacion
    setPendingStatus(newStatus);
    setShowConfirmModal(true);
  }, []);

  const handleConfirmStatusChange = useCallback(() => {
    if (pendingStatus === null) return;

    setShowConfirmModal(false);
    const newState: 'ACTIVE' | 'DISABLED' = pendingStatus ? 'ACTIVE' : 'DISABLED';

    setCurrentStatus(pendingStatus);
    onStatusChange(userId, newState);
    setPendingStatus(null);
  }, [userId, pendingStatus, onStatusChange]);

  const handleCancelStatusChange = useCallback(() => {
    setShowConfirmModal(false);
    setPendingStatus(null);
  }, []);

  return (
    <>
      <div className="flex items-center justify-center">
        <CustomEnableDisableInput
          name="userStatus"
          value={null}
          initialCheck={currentStatus}
          onCheckboxChange={handleStatusChange}
          disabled={isLoading}
          classNameContainer="flex items-center"
          className="flex items-center"
        />
        {isLoading && (
          <span className="ml-1 text-xs text-gray-500">...</span>
        )}
      </div>

      {/* Modal de confirmacion */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <IconImporter
                name="warning"
                size={24}
                className="text-yellow-500 mr-3"
              />
              <h3 className="text-lg font-semibold text-gray-900">
                {text.user.confirmStatusChange}
              </h3>
            </div>

            <p className="text-gray-600 mb-6">
              {text.user.confirmStatusChangeMessage}{' '}
              <span className="font-semibold text-gray-900">
                {pendingStatus ? text.user.activate : text.user.deactivate}
              </span>
              {' '}este usuario?
            </p>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outlined"
                onClick={handleCancelStatusChange}
                className="px-4 py-2"
              >
                {text.user.cancel}
              </Button>
              <Button
                variant="primary"
                onClick={handleConfirmStatusChange}
                className="px-4 py-2"
              >
                {text.user.confirm}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
