import {
  Bad<PERSON>, Button, DropdownSimple, IconImporter, OptionsDropdownProps,
} from '@pitsdepot/storybook';
import { useCallback, useState } from 'react';

import { UserRole } from '#domain/aggregates/user/User.Entity';
import TextService from '#composition/textService/Text.Service';

interface UserRoleDropdownProps {
  isLoading: boolean;
  currentRole: UserRole;
  onRoleChange: (newRole: UserRole) => void;
}

const roleOptions: OptionsDropdownProps[] = [
  {
    id: UserRole.ADMIN,
    name: 'Administrador',
  },
  {
    id: UserRole.USER,
    name: 'Usuario',
  },
];

const getRoleDisplayName = (role: UserRole): string => {
  const option = roleOptions.find((opt) => opt.id === role);
  return option?.name || role;
};

const getRoleColor = (role: UserRole): string => {
  if (role === UserRole.ADMIN) {
    return 'primary';
  }
  return 'secondary';
};

export function UserRoleDropdownAppComponent({
  isLoading,
  currentRole,
  onRoleChange,
}: UserRoleDropdownProps) {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingRole, setPendingRole] = useState<UserRole | null>(null);
  const text = TextService.getText();

  const onRoleChangeHandler = useCallback((newSelected: OptionsDropdownProps) => {
    const newRole = newSelected.id as UserRole;
    if (newRole === currentRole) return;
    setPendingRole(newRole);
    setShowConfirmModal(true);
  }, [currentRole]);

  const handleConfirmRoleChange = useCallback(() => {
    if (!pendingRole) return;
    onRoleChange(pendingRole);
    setShowConfirmModal(false);
    setPendingRole(null);
  }, [pendingRole, onRoleChange]);

  const handleCancelRoleChange = useCallback(() => {
    setShowConfirmModal(false);
    setPendingRole(null);
  }, []);

  return (
    <>
      <div className="flex items-center justify-center">
        <DropdownSimple
          options={roleOptions}
          showAvatar={false}
          setSelectedOption={onRoleChangeHandler}
          disabled={isLoading}
        >
          <Badge
            color={getRoleColor(currentRole)}
            className={`!text-sm ${!isLoading ? 'cursor-pointer' : 'cursor-not-allowed'}`}
          >
            {getRoleDisplayName(currentRole)}
            {!isLoading && (
              <IconImporter
                size={16}
                name='caretDown'
              />
            )}
            {isLoading && (
              <span className="ml-1 text-xs">...</span>
            )}
          </Badge>
        </DropdownSimple>
      </div>

      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <IconImporter
                name="warning"
                size={24}
                className="text-yellow-500 mr-3"
              />
              <h3 className="text-lg font-semibold text-gray-900">
                {text.user.confirmRoleChange}
              </h3>
            </div>

            <p className="text-gray-600 mb-6">
              {text.user.confirmRoleChangeMessage}{' '}
              <span className="font-semibold text-gray-900">
                {pendingRole ? getRoleDisplayName(pendingRole) : ''}
              </span>
              ?
            </p>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outlined"
                onClick={handleCancelRoleChange}
                className="px-4 py-2"
              >
                {text.user.cancel}
              </Button>
              <Button
                variant="primary"
                onClick={handleConfirmRoleChange}
                className="px-4 py-2"
              >
                {text.user.confirm}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
