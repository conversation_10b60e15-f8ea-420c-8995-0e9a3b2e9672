import { Avatar, IconImporter } from '@pitsdepot/storybook';

// import { AddProviderInventory } from './ProvidersForm.Component';

import { getRandomFadedColor } from '#infrastructure/implementation/application/utils/getRandomFadedColor';

import { ProviderByInventoryItem } from '../../application/deprecated/DashboardPages.Type';

import { providersFormConstants } from './ProvidersForm.constant';

export const ProvidersFormMainContentComponent = ({
  initialProviders,
  onRemoveProvider,
  setEditionMode,
  setEditProvider,
}: {
  initialProviders?: ProviderByInventoryItem[];
  onRemoveProvider?: (id: string) => void;
  setEditionMode?: (value: boolean) => void;
  setEditProvider?: (value: ProviderByInventoryItem) => void;
}) => {
  const providerContent = () => (initialProviders?.length ? initialProviders?.map((provider) => {
    const { providerInventories } = provider;

    const { currentDiscount = 0, currentPurchasePrice = 0 } = providerInventories[0];

    const getPrice = () => currentPurchasePrice - currentPurchasePrice * (currentDiscount / 100);

    const onEdit = (providerSelected: ProviderByInventoryItem) => {
      setEditProvider?.(providerSelected);
      setEditionMode?.(true);
    };

    const onHandleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      onRemoveProvider?.(provider.id || '');
    };

    const bgColor = getRandomFadedColor(provider.id || '');

    return (<article
      key={provider.id}
      className="flex items-center justify-between p-3 rounded-lg shadow-sm my-2 hover:bg-slate-50 cursor-pointer transition-colors duration-150"
      onClick={() => onEdit(provider)}
    >
      <div className="flex items-center gap-3 flex-1 min-w-0 pr-4">
        <div className="flex-shrink-0">
          <Avatar
            initialsBackground={bgColor}
            size={40}
            name={provider.name}
          />
        </div>
        <div className="flex flex-col min-w-0">
          <div
            className="font-semibold text-slate-800 truncate"
            title={provider.name}
          >
            {provider.name}
          </div>
          {(currentPurchasePrice || currentDiscount) && (
            <div className="flex items-baseline gap-2 text-sm mt-0.5">
              {currentPurchasePrice && (
                <span className="font-medium">
                  ${currentPurchasePrice}
                </span>
              )}
              {currentDiscount && (
                <span className="text-xs font-semibold px-2 py-0.5 rounded-full bg-red-100 ">
                  {`-${currentDiscount}%`}
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center gap-4 flex-shrink-0">
        <div className="text-right">
          <div className="text-xs ">
            {providersFormConstants.PURCHASE_PRICE}
          </div>
          <div className="font-bold  text-sm">
            ${getPrice().toFixed(2)}
          </div>
        </div>

        <button
          type="button"
          onClick={onHandleRemove}
          className="p-2 rounded-md text-slate-500 dark:text-slate-400 dark:hover:text-red-500 hover:bg-slate-100 transition-colors duration-150"
          aria-label={`Eliminar ${provider.name}`}
        >
          <IconImporter
            size={18}
            name={'trash'}
            className=""
          />
        </button>
      </div>
    </article>);
  }) : <div className='w-full h-[100px] flex justify-center items-center'>{providersFormConstants.NO_PROVIDER_FOUND}</div>);
  return (
    <article>
      {providerContent()}
    </article>
  );
};
