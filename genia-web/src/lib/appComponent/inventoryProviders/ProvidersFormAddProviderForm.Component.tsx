import {
  Button,
  DropdownWithSearch,
  FormComponent,
  IconImporter,
  OptionsDropdownProps,
  TooltipProps,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import { Provider } from '#application/provider/Provider.Type';

import {
  ProviderByInventoryItem,
} from '../../application/deprecated/DashboardPages.Type';
import { useInventoryProvidersFormInputs } from '../../infrastructure/implementation/application/hooks/useInventoryProvidersFormInputs';

import { providersFormConstants } from './ProvidersForm.constant';

export const inventoryProviderAdditionInitialState = {
  price: 0,

  discount: 0,
};

interface ProviderTooltips {
  priceTooltip: TooltipProps;

  discountTooltip: TooltipProps;
}

interface ProvidersFormAddProviderFormComponentProps {
  allProviders: Provider[] | undefined;

  onSaveProviders?: (
    provider?: ProviderByInventoryItem,
    editProvider?: ProviderByInventoryItem | null
  ) => void;

  setEditionMode?: (value: boolean) => void;

  editProvider?: ProviderByInventoryItem | null;

  provider?: ProviderByInventoryItem[];

  tooltips?: ProviderTooltips;
}

export const ProvidersFormAddProviderFormComponent = ({
  allProviders,
  onSaveProviders,
  setEditionMode,
  editProvider,
  provider,
}: ProvidersFormAddProviderFormComponentProps) => {
  const [selectedOption, setSelectedOption] = useState<Provider | null>(null);

  const [hasError, setHasError] = useState(false);

  const [defaultInputs, setDefaultInputs] = useState(
    inventoryProviderAdditionInitialState,
  );

  const { inputs, formState } = useInventoryProvidersFormInputs(
    defaultInputs,
    Boolean(selectedOption),
  );

  useEffect(() => {
    if (editProvider) {
      const selected = allProviders?.find(
        (prov) => prov.id === editProvider.id,
      );

      setSelectedOption(selected as Provider);

      setDefaultInputs({
        price: Number(
          editProvider.providerInventories?.[0].currentPurchasePrice,
        ),

        discount: Number(editProvider.providerInventories?.[0].currentDiscount),
      });
    } else {
      setSelectedOption(null);
    }
  }, [editProvider]);

  const formErrors = (localError: boolean) => setHasError(localError);

  const getFinalPurchasePrice = (
    objectPrice: number = 0,
    objectDiscount: number = 0,
  ) => {
    const price = Number(objectPrice);

    const discount = Number(objectDiscount);

    return Math.max(price - price * (discount / 100), 0).toFixed(2);
  };

  const onSaveChanges = async () => {
    const newProvider = {
      id: selectedOption?.id,

      name: selectedOption?.name,

      providerInventories: [
        {
          currentPurchasePrice: Number(formState?.price),

          currentDiscount: Number(formState?.discount),
        },
      ],
    };

    onSaveProviders?.(newProvider, editProvider);

    setEditionMode?.(false);
  };

  const onSelectOption = (option: OptionsDropdownProps) => {
    const selectedProvider = allProviders?.find((prov) => prov.id === option.id)
      || null;

    setSelectedOption(selectedProvider);
  };

  const buttonText = editProvider ? providersFormConstants.SAVE : providersFormConstants.ADD;
  const purchasePrice = `$ ${getFinalPurchasePrice(
    Number(formState?.price || 0),
    Number(formState?.discount || 0),
  )}`;

  return (
    <article className='flex flex-col gap-4'>
      <div>
        <div className="h-[99px] relative mt-4 mb-4 z-10">
          <DropdownWithSearch
            options={allProviders as OptionsDropdownProps[]}
            setSelectedOption={(option: OptionsDropdownProps) => onSelectOption(option)}
            itemsSelected={provider as OptionsDropdownProps[]}
          >
            <div className="px-[24px] py-[16px] rounded-xl min-w-[323px] relative bg-white z-50 border">
              <div className="flex justify-between border-b px-[12px] py-[16px]">
                <span
                  className={
                    selectedOption?.name ? 'text-black' : 'text-gray-400'
                  }
                >
                  {selectedOption?.name || 'Select an option'}
                </span>

                <IconImporter
                  size={18}
                  name={'caretDown'}
                  className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out pd-mr-1"
                />
              </div>
            </div>
          </DropdownWithSearch>
        </div>

        <div className="flex gap-[10px] h-[90px]">
          <FormComponent
            inputs={inputs}
            orientation="horizontal"
            columnsNumber={2}
            formValidation={formErrors}
          />
        </div>

        <div className="text-xs text-black-400">
          <span>{providersFormConstants.PURCHASE_PRICE}</span>

          <span className="font-bold ml-1 text-black-700">
            {purchasePrice}
          </span>
        </div>
      </div>

      <Button
        data-testid="button-save"
        onClick={onSaveChanges}
        className="!pd-w-full pd-flex pd-align-center pd-justify-center"
        disabled={Boolean(!selectedOption || !formState.price || hasError)}
      >
        <div className='flex items-center gap-2'>
          <IconImporter
            size={24}
            name={editProvider ? 'floppyDisk' : 'plus'}
          />
          <span>{buttonText}</span>
        </div>

      </Button>
    </article>
  );
};
