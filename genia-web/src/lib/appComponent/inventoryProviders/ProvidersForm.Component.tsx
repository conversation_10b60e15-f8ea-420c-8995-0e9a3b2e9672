import { IconImporter, IconName, Title } from '@pitsdepot/storybook';
import { useState } from 'react';

import { ProvidersFormAddProviderFormComponent } from '#appComponent/inventoryProviders/ProvidersFormAddProviderForm.Component';
import { ProvidersFormMainContentComponent } from '#appComponent/inventoryProviders/ProvidersFormMainContent.Component';
import { ProviderByInventoryItem } from '#application/deprecated/DashboardPages.Type';
import { Provider } from '#application/provider/Provider.Type';

export interface ProvidersFormProps {
  initialProviders?: ProviderByInventoryItem[];
  allProviders?: Provider[] | undefined;
  onSaveProviders?: (provider?: ProviderByInventoryItem, editProvider?: ProviderByInventoryItem | null) => Promise<void>;
  onRemoveProvider?: (id: string) => void;
}

export interface ProvidersFormHeaderComponentProps {
  title: string;
  icon: IconName;
  onClick: () => void;
}

export const ProvidersFormHeaderComponent = ({ title, icon, onClick }: ProvidersFormHeaderComponentProps) => (
  <article className='flex items-center justify-between text-[1.25rem]'>
    <Title as='h1' size="lg">
      {title}
    </Title>
    <IconImporter
      size={24}
      name={icon}
      className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out pd-cursor-pointer"
      onClick={onClick}
    />
  </article>
);

export const ProvidersFormComponent: React.FC<ProvidersFormProps> = ({
  initialProviders,
  allProviders,
  onSaveProviders,
  onRemoveProvider,
}: ProvidersFormProps) => {
  const [editionMode, setEditionMode] = useState(false);
  const [editProvider, setEditProvider] = useState<ProviderByInventoryItem | null>(null);

  return (
    <section data-testid="providers-form-container" className='bg-white relative p-5 rounded-2xl flex-1 shadow-md flex flex-col gap-2 max-w-[500px]'>

      <ProvidersFormHeaderComponent
        {...{ setEditionMode }}
        title={editionMode ? 'Agregar Proveedor' : 'Proveedores'}
        icon={editionMode ? 'x' : 'plus'}
        onClick={() => {
          setEditProvider(null);
          setEditionMode(!editionMode);
        } }
      />

      {!editionMode ? <ProvidersFormMainContentComponent {...{
        initialProviders, onRemoveProvider, setEditionMode, setEditProvider,
      }}/>
        : <ProvidersFormAddProviderFormComponent
            allProviders={allProviders}
            provider={initialProviders}
            onSaveProviders={onSaveProviders}
            setEditionMode={setEditionMode}
            editProvider={editProvider}
        />}

    </section>
  );
};
