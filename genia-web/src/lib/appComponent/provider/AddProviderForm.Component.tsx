import {
  Button, FormLabel, IconImporter, InputComponent, Title,
} from '@pitsdepot/storybook';

import { Provider } from '#application/provider/Provider.Type';
import TextService from '#composition/textService/Text.Service';

interface FormData {
  tributaryId: string;
  providerData: Partial<Provider> & { inviteMode?: boolean };
  inviteMode?: boolean;
}

interface FormState {
  isLoading: boolean;
  canSubmitForm: boolean;
}

interface FormActions {
  onTributaryIdChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onProviderDataChange: (field: string, value: string) => void;
  onGoToPreviousStep: () => void;
  onSubmit: () => void;
}

interface AddProviderFormProps {
  formData: FormData;
  formState: FormState;
  actions: FormActions;
}

export function AddProviderForm({
  formData,
  formState,
  actions,
}: AddProviderFormProps) {
  const textService = TextService.getText();

  const { tributaryId, providerData, inviteMode } = formData;
  const { isLoading, canSubmitForm } = formState;
  const {
    onTributaryIdChange,
    onProviderDataChange,
    onGoToPreviousStep,
    onSubmit,
  } = actions;

  return (
    <div className='bg-white rounded-2xl w-3/4 p-8 max-h-min shadow-lg flex flex-col gap-6'>
      <div className='flex flex-col gap-2'>
        <Title as='h3'>
          {inviteMode
            ? textService.providers.stepLabelContactInfo
            : textService.providers.stepTitleProviderDetails
          }
        </Title>
        <p className='text-gray-600'>
          {inviteMode
            ? textService.providers.inviteContactDescription
            : textService.providers.stepDescriptionProviderDetails
          }
        </p>
      </div>

      <div className='flex flex-col gap-6'>
        <div className='bg-gray-50 p-4 rounded-lg border'>
          <h4 className='text-sm font-semibold text-gray-700 mb-3'>{textService.providers.tributaryIdSectionTitle}</h4>
          <div className='flex flex-col gap-1'>
            <FormLabel title={textService.providers.labelTributaryId} />
            <InputComponent
              name="tributaryId"
              value={tributaryId}
              onChange={onTributaryIdChange}
              placeholder={textService.providers.placeholderTributaryId}
              disabled
            />
          </div>
        </div>

        {inviteMode ? (
          <div className='border-t pt-4'>
            <h4 className='text-sm font-semibold text-gray-700 mb-3'>{textService.providers.contactInfoTitle}</h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='flex flex-col gap-1'>
                <FormLabel title={textService.providers.labelNotificationEmail} />
                <InputComponent
                  name="notificationEmail"
                  value={providerData.notificationEmail || ''}
                  onChange={(e) => onProviderDataChange('notificationEmail', (e.target as HTMLInputElement).value)}
                  placeholder={textService.providers.placeholderNotificationEmail}
                  isRequired
                />
              </div>

              <div className='flex flex-col gap-1'>
                <FormLabel title={textService.providers.labelPhone} />
                <InputComponent
                  name="phone"
                  value={providerData.phone || ''}
                  onChange={(e) => onProviderDataChange('phone', (e.target as HTMLInputElement).value)}
                  placeholder={textService.providers.placeholderPhone}
                  isRequired
                />
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className='border-t pt-4'>
              <h4 className='text-sm font-semibold text-gray-700 mb-3'>{textService.providers.companyInfoTitle}</h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex flex-col gap-1'>
                  <FormLabel title={textService.providers.labelName} />
                  <InputComponent
                    name="name"
                    value={providerData.name || ''}
                    onChange={(e) => onProviderDataChange('name', (e.target as HTMLInputElement).value)}
                    placeholder={textService.providers.placeholderName}
                    isRequired
                  />
                </div>

                <div className='flex flex-col gap-1'>
                  <FormLabel title={textService.providers.labelManagerName} />
                  <InputComponent
                    name="managerName"
                    value={providerData.managerName || ''}
                    onChange={(e) => onProviderDataChange('managerName', (e.target as HTMLInputElement).value)}
                    placeholder={textService.providers.placeholderManagerName}
                    isRequired
                  />
                </div>
              </div>
            </div>

            <div className='border-t pt-4'>
              <h4 className='text-sm font-semibold text-gray-700 mb-3'>{textService.providers.contactInfoTitle}</h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex flex-col gap-1'>
                  <FormLabel title={textService.providers.labelPhone} />
                  <InputComponent
                    name="phone"
                    value={providerData.phone || ''}
                    onChange={(e) => onProviderDataChange('phone', (e.target as HTMLInputElement).value)}
                    placeholder={textService.providers.placeholderPhone}
                    isRequired
                  />
                </div>

                <div className='flex flex-col gap-1'>
                  <FormLabel title={textService.providers.labelNotificationEmail} />
                  <InputComponent
                    name="notificationEmail"
                    value={providerData.notificationEmail || ''}
                    onChange={(e) => onProviderDataChange('notificationEmail', (e.target as HTMLInputElement).value)}
                    placeholder={textService.providers.placeholderNotificationEmail}
                    isRequired
                  />
                </div>
              </div>
            </div>

            <div className='border-t pt-4'>
              <h4 className='text-sm font-semibold text-gray-700 mb-3'>{textService.providers.fiscalAddressTitle}</h4>
              <div className='grid grid-cols-1 gap-4'>
                <div className='flex flex-col gap-1'>
                  <FormLabel title={textService.providers.labelAddress} />
                  <InputComponent
                    name="address"
                    value={providerData.address || ''}
                    onChange={(e) => onProviderDataChange('address', (e.target as HTMLInputElement).value)}
                    placeholder={textService.providers.placeholderAddress}
                    isRequired
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {inviteMode && (
          <div className='text-grey-700 text-sm bg-primary/10 p-4 rounded-lg border border-dark-300'>
            <div className='font-semibold mb-2'>
              {textService.providers.providerInvitationTitle}
            </div>
            <div>
              {textService.providers.providerInvitationDescription}
            </div>
          </div>
        )}

        {!inviteMode && (
          <div className='text-green-700 text-sm bg-green-50 p-4 rounded-lg border border-green-200'>
            <div className='font-semibold mb-2'>
              {textService.providers.newProviderTitle}
            </div>
            <div>
              {textService.providers.newProviderDescription}
            </div>
          </div>
        )}
      </div>

      <div className='flex justify-between'>
        <Button
          onClick={onGoToPreviousStep}
          disabled={isLoading}
        >
          <div className='flex gap-2 items-center'>
            <IconImporter size={16} name='caretLeft' />
            <span>{textService.providers.backButtonLabel}</span>
          </div>
        </Button>

        <Button
          onClick={onSubmit}
          disabled={isLoading || !canSubmitForm}
        >
          <div className='flex gap-2 items-center'>
            <IconImporter size={24} name='plus' />
            <span>
              {inviteMode
                ? textService.providers.inviteBtnLabel
                : textService.providers.createProviderLabel
              }
            </span>
          </div>
        </Button>
      </div>
    </div>
  );
}
