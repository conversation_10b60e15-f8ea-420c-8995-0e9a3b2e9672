import {
  Button, FormLabel, IconImporter, InputComponent, Title,
} from '@pitsdepot/storybook';

import TextService from '#composition/textService/Text.Service';

interface FormState {
  tributaryId: string;
  isValidating: boolean;
  isLoading: boolean;
}

interface ValidationState {
  error: string | null;
  result: {
    exists: boolean;
  } | null;
  providerExists: boolean;
}

interface FormActions {
  onTributaryIdChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onConnectProvider: () => void;
  onCreateWithoutInvite: () => void;
  onInviteProvider: () => void;
}

interface SearchProviderTributaryIdProps {
  formState: FormState;
  validation: ValidationState;
  actions: FormActions;
}

export function SearchProviderTributaryIdComponent({
  formState,
  validation,
  actions,
}: SearchProviderTributaryIdProps) {
  const textService = TextService.getText();
  const providersText = textService.providers;

  const { tributaryId, isValidating, isLoading } = formState;
  const { error: validationError, result: validationResult, providerExists } = validation;
  const {
    onTributaryIdChange,
    onConnectProvider,
    onCreateWithoutInvite,
    onInviteProvider,
  } = actions;

  return (
    <div className='bg-white rounded-2xl w-3/4 p-8 max-h-min shadow-lg flex flex-col gap-6'>
      <div className='flex flex-col gap-2'>
        <Title as='h3'>{providersText.stepTitleTributaryId}</Title>
        <p className='text-gray-600'>{providersText.stepDescriptionTributaryId}</p>
      </div>

      <div className='flex flex-col gap-4'>
        <div className='bg-primary/10 p-4 rounded-lg border border-primary/30'>
          <p className='text-sm text-gray-700 mb-2'>
            <strong>{providersText.whatIsTributaryIdTitle}</strong>
          </p>
          <p className='text-sm text-gray-700'>
            {providersText.whatIsTributaryIdDescription}
          </p>
        </div>

        <div className='flex flex-col gap-1'>
          <FormLabel title={providersText.labelTributaryId} />
          <InputComponent
            name="tributaryId"
            value={tributaryId}
            onChange={onTributaryIdChange}
            placeholder={providersText.placeholderTributaryId}
            isRequired={true}
          />
        </div>

        {isValidating && (
          <div className='flex items-center gap-2 text-primary'>
            <IconImporter name='clock' size={16} className='animate-spin' />
            <span>{providersText.validatingTributaryId}</span>
          </div>
        )}

        {validationError && (
          <div className='text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200'>
            {validationError}
          </div>
        )}

        {validationResult && !isValidating && (
          <div className={`text-sm p-3 rounded-lg border ${
            providerExists
              ? 'text-green-700 bg-green-50 border-green-200'
              : 'text-gray-700 bg-primary/10 border-primary/30'
          }`}>
            {providerExists
              ? providersText.providerFoundMessage
              : providersText.providerNotFoundMessage
            }
          </div>
        )}
      </div>

      <div className='flex justify-end gap-3'>
        {validationResult && !isValidating && (
          <>
            {providerExists ? (
              <Button
                onClick={onConnectProvider}
                disabled={isLoading}
              >
                <div className='flex gap-2 items-center'>
                  <span>{providersText.connectBtnLabel}</span>
                </div>
              </Button>
            ) : (
              <>
                <Button
                  onClick={onCreateWithoutInvite}
                  disabled={isLoading}
                >
                  <div className='flex gap-2 items-center'>
                    <IconImporter size={16} name='plus' />
                    <span>{providersText.createWithoutInviteLabel}</span>
                  </div>
                </Button>
                <Button
                  onClick={onInviteProvider}
                  disabled={isLoading}
                >
                  <div className='flex gap-2 items-center'>
                    <span>{providersText.inviteBtnLabel}</span>
                  </div>
                </Button>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}
