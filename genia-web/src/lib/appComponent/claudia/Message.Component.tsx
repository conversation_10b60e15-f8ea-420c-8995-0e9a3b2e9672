'use client';

import { UseChatHelpers } from '@ai-sdk/react';
import { IconImporter } from '@pitsdepot/storybook';
import type { UIMessage } from 'ai';
import equal from 'fast-deep-equal';
import { AnimatePresence, motion } from 'framer-motion';
import React, { memo } from 'react';

import { Markdown } from '#appComponent/claudia/Markdown.Component';

import { MessageReasoning } from './MessageReasoning.Component';
import { PreviewAttachment } from './PreviewAttatchment.Component';

const PurePreviewMessage = ({
  message,
  isLoading,
}: {
  chatId: string;
  message: UIMessage;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
}) => (
  <AnimatePresence>
    <motion.div
      data-testid={`message-${message.role}`}
      className="w-full mx-auto max-w-3xl px-4 group/message"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      data-role={message.role}
    >
      <div
        className=
          'flex gap-4 group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl w-full'
      >
        {message.role === 'assistant' && (
          <div className="size-8 flex text-primary items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-gray-700">
            <div className="translate-y-px">
              <IconImporter name="brain" size={14} />
            </div>
          </div>
        )}

        <div className="flex flex-col gap-4 w-full">
          {message.experimental_attachments && (
            <div
              data-testid={'message-attachments'}
              className="flex flex-row justify-end gap-2"
            >
              {message.experimental_attachments.map((attachment) => (
                <PreviewAttachment
                  key={attachment.url}
                  attachment={attachment}
                />
              ))}
            </div>
          )}

          {message.parts?.map((part, index): React.JSX.Element | undefined => {
            const { type } = part;
            const key = `message-${message.id}-part-${index}`;

            if (type === 'reasoning') {
              return (
                <MessageReasoning
                  key={key}
                  isLoading={isLoading}
                  reasoning={part.reasoning}
                />
              );
            }

            if (type === 'text') {
              const alignment = message.role === 'user' ? 'end' : 'start';
              const backgroundColor = message.role === 'user' ? 'bg-gray-700' : '';
              const textColor = message.role === 'user' ? 'text-white' : 'text-muted-foreground';

              return (
                <div key={key} className={`flex flex-row gap-2 w-full justify-${alignment}`}>
                  <div
                    data-testid="message-content"
                    className={`flex flex-col gap-4 ${backgroundColor} ${textColor} px-3 py-2 rounded-xl`}
                  >
                    <Markdown>{part.text}</Markdown>
                  </div>
                </div>
              );
            }

            // if (type === 'tool-invocation') {
            //   const { toolInvocation } = part;
            //   const { toolName, toolCallId, state } = toolInvocation;

            //   if (state === 'call') {
            //     const { args } = toolInvocation;

            //     return (
            //       <div
            //         key={toolCallId}
            //         className={cx({
            //           skeleton: ['getWeather'].includes(toolName),
            //         })}
            //       >
            //         {toolName === 'getWeather' ? (
            //           <Weather />
            //         ) : toolName === 'createDocument' ? (
            //           <DocumentPreview isReadonly={isReadonly} args={args} />
            //         ) : toolName === 'updateDocument' ? (
            //           <DocumentToolCall
            //             type="update"
            //             args={args}
            //             isReadonly={isReadonly}
            //           />
            //         ) : toolName === 'requestSuggestions' ? (
            //           <DocumentToolCall
            //             type="request-suggestions"
            //             args={args}
            //             isReadonly={isReadonly}
            //           />
            //         ) : null}
            //       </div>
            //     );
            //   }

            //   if (state === 'result') {
            //     const { result } = toolInvocation;

            //     return (
            //       <div key={toolCallId}>
            //         {toolName === 'getWeather' ? (
            //           <Weather weatherAtLocation={result} />
            //         ) : toolName === 'createDocument' ? (
            //           <DocumentPreview
            //             isReadonly={isReadonly}
            //             result={result}
            //           />
            //         ) : toolName === 'updateDocument' ? (
            //           <DocumentToolResult
            //             type="update"
            //             result={result}
            //             isReadonly={isReadonly}
            //           />
            //         ) : toolName === 'requestSuggestions' ? (
            //           <DocumentToolResult
            //             type="request-suggestions"
            //             result={result}
            //             isReadonly={isReadonly}
            //           />
            //         ) : (
            //           <pre>{JSON.stringify(result, null, 2)}</pre>
            //         )}
            //       </div>
            //     );
            //   }
            // }

            return undefined;
          })}

        </div>
      </div>
    </motion.div>
  </AnimatePresence>
);

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className=
          // eslint-disable-next-line max-len
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl'
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <IconImporter name='brain' size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hmm...
          </div>
        </div>
      </div>
    </motion.div>
  );
};
