import { UseChatHelpers } from '@ai-sdk/react';
import { Button } from '@pitsdepot/storybook';
import { motion } from 'framer-motion';
import { memo } from 'react';

interface SuggestedActionsProps {
  chatId: string;
  append: UseChatHelpers['append'];
}

function PureSuggestedActions({ append }: SuggestedActionsProps) {
  const suggestedActions = [
    {
      title: 'Que pieza tenemos en inventario',
      label: 'para el SKU-000001?',
      action: 'Que pieza tenemos en inventario para el SKU-000001?',
    },
    {
      title: 'Ayudame a crear una cotizacion',
      label: 'para el los frenos del mazda 3',
      action: 'Ayudame a crear una cotizacion para el los frenos del mazda 3',
    },
    {
      title: 'Ayudame a crear un email',
      label: 'para nuestro cliente Acme Corporation',
      action: 'Ayudame a crear un email para nuestro cliente Acme Corporation',
    },
    {
      title: 'Cual es el estado de las ventas',
      label: 'de este mes?',
      action: '¿cual es el estado de las ventas de este mes?',
    },
  ];

  return (
    <div
      data-testid="suggested-actions"
      className="grid sm:grid-cols-2 gap-2 w-full"
    >
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className='flex'
        >
          <Button
            variant='outlined'
            onClick={async () => {
              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="flex flex-col text-left rounded-xl px-4 py-3.5 text-sm flex-1 gap-1  w-full h-auto justify-start items-start"
          >
            <span className="font-medium">{suggestedAction.title}</span>
            <span className="text-muted-foreground">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
