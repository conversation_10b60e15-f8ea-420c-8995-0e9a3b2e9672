import { IconImporter } from '@pitsdepot/storybook';
import { motion } from 'framer-motion';

export const Overview = () => (
  <motion.div
    key="overview"
    className="max-w-3xl mx-auto md:mt-20"
    initial={{ opacity: 0, scale: 0.98 }}
    animate={{ opacity: 1, scale: 1 }}
    exit={{ opacity: 0, scale: 0.98 }}
    transition={{ delay: 0.5 }}
  >
    <div className="rounded-xl p-6 flex flex-col gap-8 leading-relaxed text-center max-w-xl">
      <p className="flex flex-row justify-center gap-4 items-center">
        <IconImporter name="chat" size={32} />
      </p>
      <p>
        Hola! Soy <span className='text-secondary'>Claud.ia</span> de{' '}
        <span className="font-semibold">Suplif.ai.</span>{' '}
        Estoy aquí para ayudarte en todo lo que necesites{' '}
        y darte consejos para hacer crecer nuestra compañia.
      </p>
    </div>
  </motion.div>
);
