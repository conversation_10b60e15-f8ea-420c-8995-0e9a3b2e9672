import {
  Badge,
  BadgeType,
  BaseSelectInput,
  Button,
  FormErrorComponent,
  FormInputType,
  FormLabel,
  InputComponent,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  TextAreaInput,
} from '@pitsdepot/storybook';
import Theme from '@pitsdepot/storybook/theme';
import { useCallback } from 'react';

import { UseInventoryHistory } from '#application/inventory/hooks/UseInventoryHistory.Hook';
import TextService from '#composition/textService/Text.Service';

function InventoryHistoryFormComponent() {
  const {
    formState,
    isValid,
    setQuantity,
    setMovementType,
    setReason,
    saveHistoryMovement,
    isSaving,
    errors,
  } = UseInventoryHistory();

  const { inventory } = TextService.getText();

  const handleSetQuantity = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setQuantity(value);
  }, [setQuantity]);

  const handleSetMovementType = useCallback((value: string) => {
    setMovementType(value as 'inbound' | 'outbound');
  }, [setMovementType]);

  const handleSetReason = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    setReason(value);
  }, [setReason]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    saveHistoryMovement();
  }, [saveHistoryMovement]);

  return (
    <form>
      <div className='flex flex-col gap-4'>
        <div className='flex gap-4'>
          <div className='flex flex-col gap-1 flex-1'>
            <FormLabel title='Cantidad'/>
            <InputComponent
              name='quantity'
              min={0}
              inputType={FormInputType.Number}
              placeholder='Ej: 100'
              value={formState.quantity}
              onChange={handleSetQuantity}
              className='flex-1'
              error={Boolean(errors?.quantity)}
            />
            <FormErrorComponent>
              {errors?.quantity || ''}
            </FormErrorComponent>
          </div>
          <div className='flex flex-col gap-1 flex-1 h-full'>
            <FormLabel title='Tipo de movimiento'/>
            <BaseSelectInput value={formState.type} onValueChange={handleSetMovementType}>
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder='Tipo de movimiento' />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="inbound">
                    <Badge color={`${Theme.colors.fadedGreen}`} className='hover:cursor-pointer'>Agregar al Stock</Badge>
                  </SelectItem>
                  <SelectItem value="outbound">
                    <Badge color={`${Theme.colors.fadedRed}`} className='hover:cursor-pointer'>Eliminar del Stock</Badge>
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </BaseSelectInput>
            <FormErrorComponent>
              {errors?.type || ''}
            </FormErrorComponent>
          </div>
        </div>
        <div className='flex flex-col gap-1'>
          <FormLabel title='Razón del movimiento'/>
          <TextAreaInput
            name='reason'
            placeholder={inventory.reasonPlaceholder}
            value={formState.reason}
            onChange={handleSetReason}
            error={Boolean(errors?.reason)}
            />
          <FormErrorComponent>
            {errors?.reason || ''}
          </FormErrorComponent>
        </div>
        <Button
          className='!w-full'
          onClick={handleSubmit}
          disabled={!isValid || isSaving}
        >
          {inventory.save}
        </Button>
        <Badge badgeType={BadgeType.Warning}>
          {inventory.historyModificationWarning}
        </Badge>
      </div>
    </form>
  );
}

export default InventoryHistoryFormComponent;
