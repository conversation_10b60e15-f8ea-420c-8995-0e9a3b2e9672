import { useCallback, useContext, useMemo } from 'react';

import { useDropdownSearch } from '#appComponent/common/hooks/useDropdownSearch.Hook';
import { formatDateToString } from '#application/common/utils/FormatDateToString.Util';
import ApplicationRegistry from '#composition/Application.Registry';

function InventoryistoryComponent() {
  const { inventoryId } = useContext(ApplicationRegistry.InventoryContext);

  const {
    filteredOptions: accumulatedItems,
    loadMoreOptions: loadMore,
  } = useDropdownSearch({
    itemsPerPage: 10,
    getItems: useCallback(({ limit, offset }) => ApplicationRegistry.InventoryService.useGetInventoryHistory({
      limit,
      offset,
      orderBy: 'createdAt',
      order: 'desc',
      filters: inventoryId ? { inventoryId: [inventoryId] } : undefined,
    }), [inventoryId]),
    selectedItemIds: [],
    getId: (item) => item.id,
  });
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight) {
      loadMore();
    }
  }, [loadMore]);

  const renderedItems = useMemo(() => (
    accumulatedItems?.map(({
      id, movementType, user, quantity, measurementUnit, reason, createdAt,
    }) => (
      <div key={id} className="flex gap-2">
        <div className="flex flex-col items-center">
          <div className="h-5 w-5 rounded-full bg-dark-200 flex justify-center items-center">
            <div className={`h-2 w-2 ${movementType === 'inbound' ? 'bg-positive' : 'bg-negative'} rounded-full`}></div>
          </div>
          <div className="flex-1 w-px bg-dark-300"></div>
        </div>
        <div className="flex-1 flex flex-col gap-1">
          <div className="text-xsm">
            <span className="font-semibold">{user?.email || 'Anónimo'}</span> ha {movementType === 'inbound' ? 'añadido ' : 'removido '}
            <span className="font-semibold">{quantity} {measurementUnit}</span>
          </div>
          <p className="text-xsm text-gray-600">Movimiento debido a <span className='font-semibold'>{reason}</span></p>
          <div className="text-xxsm text-dark-500">{formatDateToString(createdAt)}</div>
        </div>
      </div>
    ))
  ), [accumulatedItems]);

  return (
    <div className='overflow-y-auto max-h-[300px]' onScroll={handleScroll}>
      {(!accumulatedItems || accumulatedItems?.length === 0) && (
        <div>No hay movimientos</div>
      )}
      <div className="space-y-4">
        {renderedItems}
      </div>
    </div>
  );
}

export default InventoryistoryComponent;
