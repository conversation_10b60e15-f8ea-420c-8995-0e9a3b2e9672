# 🧼 Clean Architecture + Composition Layer (Vite + React)

## 📋 Table of Contents

1. [🎯 Overview](#-overview)
2. [📦 Project Structure](#-project-structure)
3. [🔄 Dependency Flow](#-dependency-flow)
4. [✅ Applied Principles](#-applied-principles)
5. [🔧 Implementation Examples](#-implementation-examples)
6. [🧪 Testing](#-testing)
7. [🚀 Development Flow](#-development-flow)

---

## 🎯 Overview

This project implements **Clean Architecture** with a custom composition layer for React + Vite applications. The architecture ensures:

- ✅ **Clear separation of concerns**
- ✅ **Dependency inversion**
- ✅ **Independent testability**
- ✅ **Scalability and maintainability**

---

## 📦 Project Structure

```
src/
├── composition/                  # 🔀 Composition Layer (transversal)
│   └── ApplicationRegistry.ts   # Assembles services/repos from infrastructure
│
├── domain/                      # 🏛️ Pure business logic (no dependencies)
│   └── users/
│       ├── User.ts              # Domain entity
│       └── UserAggregate.ts     # Business rules and domain logic
│
├── application/                 # 🚀 Application logic orchestration
│   ├── common/                 # Contracts and interfaces (e.g., LoggerService)
│   └── users/
│       └── useCases/
│           └── updateProfile.UseCase.ts  # Manipulates domain aggregates
│
├── infrastructure/              # 🔧 External implementations
│   └── implementations/
│       ├── services/           # Logger, Storage, etc. implementations
│       └── repositories/       # Concrete repositories (e.g., API, memory)
│
├── ui/                          # 🎨 React presentation layer
│   ├── components/             # Visual components
│   ├── hooks/                  # Hooks that invoke use cases
│   └── pages/                  # Application views
│
├── main.tsx                     # Vite app entry point
└── vite.config.ts               # Configuration and aliases
```

---

## 🔄 Dependency Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                        DEPENDENCY FLOW                         │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                🔀 COMPOSITION LAYER (Transversal)                ║
║    ┌─────────────────────────────────────────────────────────┐   ║
║    │  Wires up all dependencies across layers                │   ║
║    │  • Application ←→ Infrastructure                        │   ║
║    │  • Infrastructure ←→ Contracts                          │   ║
║    └─────────────────────────────────────────────────────────┘   ║
╚═══════════════════════════════════════════════════════════════════╝
                                    │
                                    │ provides dependencies
                                    ▼
    ┌─────────────────┐                        ┌─────────────────┐
    │   🎨 UI Layer   │ ──────────────────────▶│ 🚀 Application  │
    │   (React UI)    │    invokes use cases   │ (Application    │$$
    └─────────────────┘                        │    Logic)       │
                                               └─────────────────┘
                                                         │
                                                         │ depends on
                                                         ▼
                                               ┌─────────────────┐
                                               │ 📋 Interfaces/  │
                                               │   Contracts     │
                                               └─────────────────┘
                                                         ▲
                                                         │ implements
                                                         │
                                               ┌─────────────────┐
                                               │ 🔧 Infra.       │
                                               └─────────────────┘
                                                         
                                                         
                                               ┌─────────────────┐
                                               │ 🏛️ Domain Layer │
                                               │   (Entities &   │
                                               │ Business Rules) │
                                               └─────────────────┘
                                                         ▲
                                                         │ accessed by
                                                         │ (never depends on other layers)
                                                         │
                                               ┌─────────────────┐
                                               │ 🚀 Application  │
                                               │ (manipulates    │
                                               │  aggregates)    │
                                               └─────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        EXECUTION FLOW                          │
└─────────────────────────────────────────────────────────────────┘

1. UI Hook invokes Use Case
2. Use Case gets dependencies from Registry
3. Registry resolves concrete implementations
4. Use Case executes application logic (orchestration)
5. Application manipulates domain aggregates and entities
6. Domain contains business rules and entities (no external dependencies)
7. Infrastructure interacts with external services and implements application contracts
```

---

## ✅ Applied Principles

### 1. **Separation of Concerns**
- **`domain/`**: Pure business logic and entities, **no external dependencies** (lowest layer)
- **`application/`**: Application logic orchestration, manipulates domain aggregates
- **`infrastructure/`**: Real implementations (HTTP, DB, etc.)
- **`composition/`**: **Transversal layer** - dependency configuration and resolution across all layers
- **`ui/`**: Presentation decoupled from business logic

### 2. **Dependency Inversion**
- Use cases consume **interfaces**, not implementations
- Implementations are resolved in **`composition/`** (transversal layer)
- **Domain layer** has no dependencies on other layers (can be accessed by all)
- Application layer manipulates domain aggregates and entities
- Independent testing with mocks

### 3. **Transversal Composition**
- The composition layer cuts across all other layers
- It's responsible for wiring up dependencies between layers
- Provides a centralized registry for all services and repositories
- Enables easy swapping of implementations (e.g., dev vs prod)

### 4. **Single Responsibility**
- Each layer has a specific responsibility
- Small, focused components

### 5. **Testability**
- Injected dependencies allow isolated testing
- Easy to create and maintain mocks

---

## 🔧 Implementation Examples

### ApplicationRegistry (Composition Layer)

```ts
// composition/ApplicationRegistry.ts
import { InMemoryUserRepository } from '#infrastructure/implementations/repositories/InMemoryUserRepository';
import { ConsoleLoggerService } from '#infrastructure/implementations/services/ConsoleLoggerService';

const ApplicationRegistry = {
  UserRepository: InMemoryUserRepository(),
  LoggerService: ConsoleLoggerService(),
};

export default ApplicationRegistry;
```

### Use Case (Application Layer)

```ts
// application/users/useCases/updateProfile.UseCase.ts
import ApplicationRegistry from '#composition/ApplicationRegistry';
import { UserAggregate } from '#domain/users/UserAggregate';

export async function updateProfileUseCase(id: string, data: any) {
  const { UserRepository, LoggerService } = ApplicationRegistry;

  LoggerService.log('Updating profile...');
  
  // Application logic: orchestrate and manipulate domain aggregates
  const userAggregate = UserAggregate.fromRepository(await UserRepository.findById(id));
  userAggregate.updateProfile(data); // Business logic executed in domain
  
  const user = await UserRepository.update(id, userAggregate.toData());
  return user;
}
```

### UI Hook (Presentation Layer)

```ts
// ui/hooks/useUpdateProfile.ts
import { useState } from 'react';
import { updateProfileUseCase } from '#application/users/useCases/updateProfile.UseCase';

export function useUpdateProfile() {
  const [user, setUser] = useState(null);

  const update = async (id: string, input: any) => {
    const result = await updateProfileUseCase(id, input);
    setUser(result);
  };

  return { user, update };
}
```

---

## 🧪 Testing

### Independent Testing with Mocks

```ts
const MockRegistry = {
  LoggerService: { log: jest.fn() },
  UserRepository: {
    update: jest.fn().mockResolvedValue({ id: '1', name: 'Test' }),
  },
};

test('updateProfileUseCase updates correctly', async () => {
  const result = await updateProfileUseCase('1', { name: 'Test' }, MockRegistry);
  expect(result.name).toBe('Test');
});
```

### Testing Strategies by Layer

| Layer | Strategy | Tools |
|------|----------|-------|
| **Domain** | Entity and business rule testing | Jest, Vitest |
| **Application** | Use case testing with mocks | Jest + Mocks |
| **Infrastructure** | Integration testing | Jest + MSW |
| **UI** | Component and hook testing | React Testing Library |

---

## 🚀 Development Flow

### 1. **Define Domain Entities and Business Rules**
```ts
// domain/users/User.ts
export interface User {
  id: string;
  name: string;
  email: string;
}

// domain/users/UserAggregate.ts
export class UserAggregate {
  // Business rules and domain logic (no external dependencies)
  updateProfile(data: any): void {
    // Business validation and rules
  }
}
```

### 2. **Create Interfaces/Contracts**
```ts
// application/common/LoggerService.ts
export interface LoggerService {
  log(message: string): void;
}
```

### 3. **Implement Use Cases**
```ts
// application/users/useCases/createUser.UseCase.ts
export async function createUserUseCase(userData: CreateUserInput) {
  // Business logic
}
```

### 4. **Implement Infrastructure**
```ts
// infrastructure/implementations/services/ConsoleLoggerService.ts
export const ConsoleLoggerService = (): LoggerService => ({
  log: (message: string) => console.log(message)
});
```

### 5. **Configure Composition**
```ts
// composition/ApplicationRegistry.ts
export default {
  LoggerService: ConsoleLoggerService(),
  // ... other services
};
```

### 6. **Create UI Hooks**
```ts
// ui/hooks/useCreateUser.ts
export function useCreateUser() {
  // Hook that consumes the use case
}
```

### 7. **Implement Components**
```tsx
// ui/components/UserForm.tsx
export function UserForm() {
  const { createUser } = useCreateUser();
  // React component
}
```

---

## 🔄 Architecture Benefits

- **🔒 Low Coupling**: Each layer is independent
- **🧪 Testability**: Easy testing with mocks
- **🔧 Maintainability**: Isolated changes per layer
- **📈 Scalability**: Easy to add new features
- **🔄 Flexibility**: Change implementations without affecting logic
- **📚 Readability**: Clear and well-organized code

---

## 📝 Naming Conventions

- **Use Cases**: `{action}{Entity}.UseCase.ts`
- **Interfaces**: `{ServiceName}Service.ts`
- **Implementations**: `{Technology}{ServiceName}Service.ts`
- **Hooks**: `use{Action}{Entity}.ts`
- **Components**: `{ComponentName}.tsx`

---

## 🛠️ Tools and Configuration

- **Vite**: Bundler and dev server
- **React**: UI framework
- **TypeScript**: Static typing
- **Jest/Vitest**: Testing
- **Path Aliases**: Clean imports (`#domain`, `#application`, etc.)

---

*This architecture ensures clean, maintainable, and scalable code for enterprise React applications.* 🚀
