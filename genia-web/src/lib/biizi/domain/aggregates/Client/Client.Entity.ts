export default class ClientEntity {
  id: string;

  name: string;

  tributaryId: string;

  clientCompanyId: string | null;

  storeDiscounts: string[];

  contactInformation: {
    billingEmail: string;
    billingPhoneNumber: string;
    billingWhatsapp: string;
    purchasesEmail: string;
    purchasesPhoneNumber: string;
    purchasesWhatsapp: string;
    salesEmail: string;
    salesPhoneNumber: string;
    salesWhatsapp: string;
    shippingAddress: string;
    billingAddress: string;
  };

  createdAt: Date;

  updatedAt: Date;

  constructor(
    id: string,
    name: string,
    tributaryId: string,
    clientCompanyId: string | null,
    storeDiscounts: string[],
    contactInformation: {
      billingEmail: string;
      billingPhoneNumber: string;
      billingWhatsapp: string;
      purchasesEmail: string;
      purchasesPhoneNumber: string;
      purchasesWhatsapp: string;
      salesEmail: string;
      salesPhoneNumber: string;
      salesWhatsapp: string;
      shippingAddress: string;
      billingAddress: string;
    },
    createdAt: Date,
    updatedAt: Date,
  ) {
    this.id = id;
    this.name = name;
    this.tributaryId = tributaryId;
    this.clientCompanyId = clientCompanyId;
    this.storeDiscounts = storeDiscounts;
    this.contactInformation = contactInformation;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
