export interface Integration {
  id: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
  params?: Record<string, string>;
}

export default class IntegrationEntity {
  id: string;

  type: string;

  createdAt: Date;

  updatedAt: Date;

  params?: Record<string, string>;

  constructor(
    id: string,
    type: string,
    createdAt: Date,
    updatedAt: Date,
    params?: Record<string, string>,
  ) {
    this.id = id;
    this.type = type;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.params = params || {};
  }

  setParams(params: Record<string, string>): void {
    this.params = params;
  }

  toJSON(): Integration {
    return {
      id: this.id,
      type: this.type,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      params: this.params,
    };
  }
}
