import {
  useCallback, useContext, useEffect, useState,
} from 'react';

import { AuthContext } from '#infrastructure/AuthState.Context';
import InMemCacheUtil from '#infrastructure/utils/InMemCache.Util';

export type WrappedWriteUseCaseHook<T, U> = () => {
  isLoading: boolean;
  error: Error | null;
  data: U | null;
  apply: (params: T) => void
};

export type WrappedReadUseCaseHook<T, U> = (params?: T) => {
  isLoading: boolean;
  error: Error | null;
  data: U | null;
  refetch: () => void;
};

export function WrapUseCaseHook<T, U>(useCase: (params: T) => U | Promise<U>): WrappedWriteUseCaseHook<T, U> {
  return () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const [data, setData] = useState<U | null>(null);

    const apply = async (params: T) => {
      setIsLoading(true);
      try {
        const result = await useCase(params);

        setData(result);
      } catch (err) {
        setError(err as Error | null);
      } finally {
        setIsLoading(false);
      }
    };

    return {
      isLoading,
      error,
      data,
      apply,
    };
  };
}

export function WrapWriteUseCaseHook<T, U>(
  useCase: (params: T) => U | Promise<U>,
): WrappedWriteUseCaseHook<T, U> {
  return () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const [data, setData] = useState<U | null>(null);
    const { getToken } = useContext(AuthContext);

    const apply = async (params: T) => {
      setIsLoading(true);
      try {
        const token = await getToken();

        InMemCacheUtil.setToken(token || '');

        const result = await useCase(params);
        setData(result);
      } catch (err) {
        setError(err as Error | null);
      } finally {
        setIsLoading(false);
      }
    };

    return {
      isLoading,
      error,
      data,
      apply,
    };
  };
}

export function WrappedReadUseCaseHook<T, U>(
  useCase: (params?: T) => U | Promise<U>,
): WrappedReadUseCaseHook<T, U> {
  return (params?: T) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const [data, setData] = useState<U | null>(null);
    const { getToken } = useContext(AuthContext);

    const fetchData = useCallback(async () => {
      setIsLoading(true);
      try {
        const token = await getToken();
        InMemCacheUtil.setToken(token || '');
        const result = await useCase(params);
        setData(result);
      } catch (err) {
        setError(err as Error | null);
      } finally {
        setIsLoading(false);
      }
    }, [params, getToken, useCase]);

    useEffect(() => {
      fetchData();
    }, [params]);

    const refetch = async () => {
      fetchData();
    };

    return {
      isLoading,
      error,
      data,
      refetch,
    };
  };
}
