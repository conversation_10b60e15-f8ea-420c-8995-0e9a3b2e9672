'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  FormInputType,
  IconImporter,
  InputComponent as Input,
  Label,
  Tabs,
  TabsContent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@pitsdepot/storybook';
import { motion } from 'framer-motion';

import { IntegrationsModule } from '#/lib/biizi/ui/modules/Integrations.Module';
import TextService from '#composition/textService/Text.Service';

export default function SettingsLayout() {
  const { settings } = TextService.getText();

  return (
    <div className="container mx-auto py-8 px-4 lg:px-8 max-w-7xl">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
        >
        <h1 className="text-3xl font-bold tracking-tight">{settings.title}</h1>
        <p className="text-muted-foreground mt-2">
          {settings.subtitle}
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        >
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-white">
            <TabsTrigger value="profile" className="flex flex-1 items-center gap-2">
              <IconImporter name="user" className="h-4 w-4" />
              {settings.profile}
            </TabsTrigger>
            <TabsTrigger value="integrations" className="flex flex-1 items-center gap-2">
              <IconImporter name="link" className="h-4 w-4" />
              {settings.integrations}
            </TabsTrigger>
            {/* <TabsTrigger value="notifications" className="flex flex-1 items-center gap-2">
                <IconImporter name="bell" className="h-4 w-4" />
                {settings.notifications}
              </TabsTrigger>
              <TabsTrigger value="security" className="flex flex-1 items-center gap-2">
                <IconImporter name="shield" className="h-4 w-4" />
                {settings.security}
              </TabsTrigger>
              <TabsTrigger value="appearance" className="flex flex-1 items-center gap-2">
                <IconImporter name="palette" className="h-4 w-4" />
                {settings.appearance}
              </TabsTrigger> */}
          </TabsList>

          <TabsContent value="profile">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
              >
              <Card className="bg-white shadow-md rounded-lg p-6">
                <CardHeader>
                  <CardTitle>{settings.profileTitle}</CardTitle>
                  <CardDescription>
                    {settings.profileDescription}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">{settings.firstName}</Label>
                      <Input name="firstName" id="firstName" placeholder={settings.firstNamePlaceholder} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">{settings.lastName}</Label>
                      <Input name="lastName" id="lastName" placeholder={settings.lastNamePlaceholder} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">{settings.email}</Label>
                    <Input id="email" name="email" inputType={'email' as FormInputType} placeholder={settings.emailPlaceholder} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="company">{settings.company}</Label>
                    <Input name="company" id="company" placeholder={settings.companyPlaceholder} />
                  </div>
                  <Button className="gap-2">
                    <IconImporter name="floppyDisk" className="h-4 w-4" />
                    {settings.saveChanges}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          <TabsContent value="integrations">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
              >
              <Card className="bg-white shadow-md rounded-lg p-6">
                <IntegrationsModule />
              </Card>
            </motion.div>
          </TabsContent>

          {/* <TabsContent value="notifications">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <Card className="bg-white shadow-md rounded-lg p-6">
                  <CardHeader>
                    <CardTitle>{settings.notificationsTitle}</CardTitle>
                    <CardDescription>
                      {settings.notificationsDescription}
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.newConversations}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.newConversationsDescription}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.agentResponses}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.agentResponsesDescription}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.pausedConversations}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.pausedConversationsDescription}
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Button className="gap-2">
                    <IconImporter name="floppyDisk" className="h-4 w-4" />
                    {settings.savePreferences}
                  </Button>
                </CardContent>
              </Card>
              </motion.div>
            </TabsContent> */}

          {/* <TabsContent value="security">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <Card className="bg-white shadow-md rounded-lg p-6">
                  <CardHeader>
                    <CardTitle>{settings.securityTitle}</CardTitle>
                    <CardDescription>
                      {settings.securityDescription}
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">{settings.currentPassword}</Label>
                    <Input name="currentPassword" id="currentPassword" inputType={"password" as FormInputType} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">{settings.newPassword}</Label>
                    <Input name="newPassword" id="newPassword" inputType={"password" as FormInputType} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">{settings.confirmPassword}</Label>
                    <Input name="confirmPassword" id="confirmPassword" inputType={"password" as FormInputType} />
                  </div>
                  <div className="flex items-center justify-between pt-4">
                    <div className="space-y-0.5">
                      <Label>{settings.twoFactorAuth}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.twoFactorAuthDescription}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <Button className="gap-2">
                    <IconImporter name="floppyDisk" className="h-4 w-4" />
                    {settings.updatePassword}
                  </Button>
                </CardContent>
              </Card>
              </motion.div>
            </TabsContent> */}

          {/* <TabsContent value="appearance">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <Card className="bg-white shadow-md rounded-lg p-6">
                  <CardHeader>
                    <CardTitle>{settings.appearanceTitle}</CardTitle>
                    <CardDescription>
                      {settings.appearanceDescription}
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.darkMode}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.darkModeDescription}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.animations}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.animationsDescription}
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>{settings.notificationSounds}</Label>
                      <p className="text-sm text-muted-foreground">
                        {settings.notificationSoundsDescription}
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <Button className="gap-2">
                    <IconImporter name="floppyDisk" className="h-4 w-4" />
                    {settings.savePreferences}
                  </Button>
                </CardContent>
              </Card>
              </motion.div>
            </TabsContent> */}
        </Tabs>
      </motion.div>
    </div>
  );
}
