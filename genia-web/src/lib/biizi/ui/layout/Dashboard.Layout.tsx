import {
  <PERSON>, CardContent, CardHeader, CardTitle, IconImporter,
} from '@pitsdepot/storybook';
import { motion } from 'framer-motion';

import TextService from '#composition/textService/Text.Service';

const stats = [
  {
    titleKey: 'activeAgents',
    value: '4',
    change: '+12%',
    icon: 'brain' as const,
    color: 'text-blue-600',
  },
  {
    titleKey: 'conversations',
    value: '247',
    change: '+23%',
    icon: 'chat' as const,
    color: 'text-green-600',
  },
  {
    titleKey: 'clients',
    value: '89',
    change: '+8%',
    icon: 'users' as const,
    color: 'text-purple-600',
  },
  {
    titleKey: 'conversionRate',
    value: '68%',
    change: '+5%',
    icon: 'trendUp' as const,
    color: 'text-red-600',
  },
];

export default function DashboardLayout() {
  const { dashboard } = TextService.getText();

  return (
    <div className="container mx-auto py-8 px-4 lg:px-8 max-w-7xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{dashboard.title}</h1>
          <p className="text-muted-foreground mt-2">{dashboard.subtitle}</p>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.titleKey}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}>

            <Card>
              <CardHeader className="flex !flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="!text-sm !font-medium !text-gray-600">
                  {dashboard[stat.titleKey as keyof typeof dashboard]}
                </CardTitle>
                <IconImporter name={stat.icon} className={`w-4 h-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-green-600 mt-1">
                  {stat.change} {dashboard.sinceLastMonth}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
