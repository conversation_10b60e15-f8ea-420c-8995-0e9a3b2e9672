import {
  createContext, ReactNode,
  useState,
} from 'react';

import { ClientType } from '#/lib/application/client/Client.Type';

// Interfaces para los datos del cliente
interface ClientFormData {
  profilePhoto?: string;
  clientType: ClientType;
  firstName: string;
  lastName: string;
  companyName?: string;
  contactPerson?: string;
  tributaryId?: string;
  clientAddress: string;
  email: string;
  phone: string;
  isWhatsApp: boolean;
  whatsappNumber: string;
  location: string;
  // Contactos alternativos
  billingEmail: string;
  billingPhone: string;
  billingWhatsApp: string;
  purchasesEmail: string;
  purchasesPhone: string;
  purchasesWhatsApp: string;
  propertyType: string;
  budget: string;
  preferredContact: string;
  tags: string[];
  notes: string;
}

interface ClientPayload {
  profilePhoto?: string;
  clientType: ClientType;
  firstName: string;
  lastName: string;
  companyName?: string;
  contactPerson?: string;
  tributaryId?: string;
  clientAddress: string;
  email: string;
  phone: string;
  isWhatsApp: boolean;
  whatsappNumber: string;
  location: string;
  alternativeContacts: {
    billing: {
      email: string;
      phone: string;
      whatsapp: string;
    };
    purchases: {
      email: string;
      phone: string;
      whatsapp: string;
    };
  };
  propertyType: string;
  budget: string;
  preferredContact: string;
  tags: string[];
  notes: string;
}

interface ClientFormContextType {
  formData: ClientFormData;
  updateField: <K extends keyof ClientFormData>(field: K, value: ClientFormData[K]) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  getPayload: () => ClientPayload;
  resetForm: () => void;
}

const initialFormData: ClientFormData = {
  profilePhoto: undefined,
  clientType: ClientType.CLIENT,
  firstName: '',
  lastName: '',
  companyName: '',
  contactPerson: '',
  tributaryId: '',
  clientAddress: '',
  email: '',
  phone: '',
  isWhatsApp: false,
  whatsappNumber: '',
  location: '',
  billingEmail: '',
  billingPhone: '',
  billingWhatsApp: '',
  purchasesEmail: '',
  purchasesPhone: '',
  purchasesWhatsApp: '',
  propertyType: '',
  budget: '',
  preferredContact: 'WhatsApp',
  tags: [],
  notes: '',
};

export const ClientFormContext = createContext<ClientFormContextType | undefined>(undefined);

export function ClientFormProvider({ children }: { children: ReactNode }) {
  const [formData, setFormData] = useState<ClientFormData>(initialFormData);

  const updateField = <K extends keyof ClientFormData>(field: K, value: ClientFormData[K]) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addTag = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tag.trim()],
      }));
    }
  };

  const removeTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }));
  };

  const getPayload = (): ClientPayload => ({
    profilePhoto: formData.profilePhoto,
    clientType: formData.clientType,
    firstName: formData.firstName,
    lastName: formData.lastName,
    companyName: formData.companyName,
    contactPerson: formData.contactPerson,
    tributaryId: formData.tributaryId,
    clientAddress: formData.clientAddress,
    email: formData.email,
    phone: formData.phone,
    isWhatsApp: formData.isWhatsApp,
    whatsappNumber: formData.whatsappNumber,
    location: formData.location,
    alternativeContacts: {
      billing: {
        email: formData.billingEmail,
        phone: formData.billingPhone,
        whatsapp: formData.billingWhatsApp,
      },
      purchases: {
        email: formData.purchasesEmail,
        phone: formData.purchasesPhone,
        whatsapp: formData.purchasesWhatsApp,
      },
    },
    propertyType: formData.propertyType,
    budget: formData.budget,
    preferredContact: formData.preferredContact,
    tags: formData.tags,
    notes: formData.notes,
  });

  const resetForm = () => {
    setFormData(initialFormData);
  };

  return (
    <ClientFormContext.Provider value={{
      formData,
      updateField,
      addTag,
      removeTag,
      getPayload,
      resetForm,
    }}>
      {children}
    </ClientFormContext.Provider>
  );
}
