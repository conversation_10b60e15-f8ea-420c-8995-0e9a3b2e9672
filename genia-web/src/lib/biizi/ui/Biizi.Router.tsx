import {
  Navigate,
  Route,
  Routes,
} from 'react-router-dom';

import BiiziPage from '#/lib/biizi/ui/Biizi.Page';
import DashboardLayout from '#/lib/biizi/ui/layout/Dashboard.Layout';
import SettingsLayout from '#/lib/biizi/ui/layout/Settings.Layout';

export function BiiziRouter() {
  return (
    <Routes>
      <Route
        path="/"
        element={
          <BiiziPage />
        }
      >
        <Route index element={<Navigate to="dashboard" replace />} />
        {/* Core App Routes */}
        <Route path="dashboard" element={<DashboardLayout />} />
        <Route path="settings" element={<SettingsLayout />} />
        <Route path="*" element={<Navigate to="dashboard" replace />} />
      </Route>
    </Routes>
  );
}
