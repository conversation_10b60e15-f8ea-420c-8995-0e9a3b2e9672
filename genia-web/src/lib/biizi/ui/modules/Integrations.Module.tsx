import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CircleLoader,
  IconImporter,
  IconName,
} from '@pitsdepot/storybook';
import { useEffect, useState } from 'react';

import IntegrationEntity from '#/lib/biizi/domain/aggregates/integration/Integration.Entity';
import {
  IntegrationComponent,
  IntegrationStatus,
} from '#/lib/biizi/ui/components/Integration.Component';
import {
  useConnectIntegration,
  useDisconnectIntegration,
  useFindIntegrations,
} from '#/lib/biizi/ui/hooks/Integration.Hook';
import TextService from '#composition/textService/Text.Service';

const text = TextService.getText();

interface ModuleIntegration {
  id?: string;
  icon: IconName;
  type: 'whatsapp' | 'gmail';
  color: string;
  description: string;
  status: IntegrationStatus;
  params: Record<string, string>;
  connect?: (params?: Record<string, string>) => void;
  disconnect?: () => void;
}

const integrationsPlaceholder: ModuleIntegration[] = [
  {
    type: 'gmail',
    icon: 'googleLogo',
    description: text.integration.gmailDescription,
    status: IntegrationStatus.DISCONNECTED,
    color: '[rgb(230,90,90)]',
    params: {},
  },
  {
    type: 'whatsapp',
    icon: 'whatsApp',
    description: text.integration.whatsappDescription,
    status: IntegrationStatus.DISCONNECTED,
    color: '[rgb(128,219,76)]',
    params: {},
  },
];

function useIntegrationWrapper(
  integration: ModuleIntegration,
): ModuleIntegration {
  const {
    isLoading, error, apply, data,
  } = useConnectIntegration();

  const {
    isLoading: isDisconnecting,
    error: disconnectError,
    apply: disconnectApply,
    data: disconnectData,
  } = useDisconnectIntegration();

  const [currentIntegration, setCurrentIntegration] = useState<ModuleIntegration>({
    ...integration,
    connect: (params?: Record<string, string>) => {
      apply({ type: integration.type, params });
    },
    disconnect: () => {
      disconnectApply({ id: integration.id || '' });
    },
  });

  useEffect(() => {
    setCurrentIntegration((prev) => ({
      ...prev,
      ...integration,
      params: integration.params,
      connect: (params?: Record<string, string>) => apply({ type: integration.type, params }),
      disconnect: () => disconnectApply({ id: integration.id || '' }),
    }));
  }, [JSON.stringify(integration)]);

  useEffect(() => {
    if (isLoading || isDisconnecting) {
      setCurrentIntegration((prev) => ({
        ...prev,
        status: IntegrationStatus.CONNECTING,
      }));
    }
  }, [isLoading, isDisconnecting]);

  useEffect(() => {
    if (error) {
      setCurrentIntegration((prev) => ({
        ...prev,
        status: IntegrationStatus.ERROR,
      }));
    }
  }, [error]);

  useEffect(() => {
    if (disconnectError) {
      setCurrentIntegration((prev) => ({
        ...prev,
        status: IntegrationStatus.DISCONNECT_ERROR,
      }));
    }
  }, [disconnectError]);

  useEffect(() => {
    if (data) {
      setCurrentIntegration((prev) => ({
        ...prev,
        id: data.id,
        params: data.params || {},
        status: IntegrationStatus.CONNECTED,
        disconnect: () => disconnectApply({ id: data.id || '' }),

      }));
    }
  }, [JSON.stringify(data)]);

  useEffect(() => {
    if (disconnectData && disconnectData.status === 'success') {
      setCurrentIntegration((prev) => ({
        ...prev,
        status: IntegrationStatus.DISCONNECTED,
      }));
    }
  }, [disconnectData]);

  return currentIntegration;
}

function useTransformIntegrations(
  integrations?: IntegrationEntity[] | null,
): ModuleIntegration[] {
  let mergedIntegrations: ModuleIntegration[] = integrationsPlaceholder;

  if (integrations && integrations.length > 0) {
    const integrationsMap = new Map<string, IntegrationEntity>(
      integrations.map((i) => [i.type, i]),
    );

    mergedIntegrations = mergedIntegrations.map((placeholder) => {
      const integration = integrationsMap.get(placeholder.type);
      if (integration) {
        return {
          ...placeholder,
          id: integration.id,
          status: IntegrationStatus.CONNECTED,
          params: integration.params || {},
        };
      }
      return placeholder;
    });
  }

  return mergedIntegrations.map((integration) => useIntegrationWrapper(integration));
}

export function IntegrationsModule() {
  const { data, error, isLoading } = useFindIntegrations();

  const transformedIntegrations = useTransformIntegrations(data);

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-100 border border-red-200 text-red-800 p-4 rounded-lg">
          <div className="flex items-center">
            <IconImporter
              name="exclamationMark"
              size={20}
              className="text-red-600 mr-1 mt-0.5 flex-shrink-0 bg-red-300 rounded-full p-1"
            />

            <p className="text-red-600 text-sm gap-2 items-center">
              {text.integration.integrationLoadError}
            </p>
          </div>
        </div>
      )}
      {isLoading && (
        <div className="bg-secondary border border-secondary rounded-lg p-4">
          <div className="flex items-start">
            <IconImporter
              name="info"
              size={20}
              className="text-white mr-3 mt-0.5 flex-shrink-0"
            />
            <div className="text-white text-sm flex gap-2 items-center">
              {text.integration.loadingIntegrations}{' '}
              <CircleLoader size="sm"></CircleLoader>
            </div>
          </div>
        </div>
      )}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{text.integration.availableIntegrations}</CardTitle>
              <CardDescription className="pt-2">
                {text.integration.availableIntegrationsDescription}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        {transformedIntegrations
          .map((integration) => (
            <CardContent key={integration.type} className="space-y-4">
              <IntegrationComponent
                id={integration.id || ''}
                icon={integration.icon}
                name={integration.type}
                type={integration.type}
                description={integration.description}
                color={integration.color}
                status={integration.status}
                params={integration.params}
                onConnect={(params) => integration.connect?.(params)}
                onDisconnect={integration.disconnect}
              />
            </CardContent>
          ))}
      </Card>
    </div>
  );
}
