import { Button } from '@pitsdepot/storybook';
import { useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { ClientType } from '#/lib/application/client/Client.Type';
import { AddClientForm } from '#/lib/biizi/ui/components/AddClientForm.Component';
import { ClientFormProvider } from '#/lib/biizi/ui/contexts/ClientFormContext';
import { useClientForm, useCreateClient } from '#/lib/biizi/ui/hooks/Client.Hook';
import ApplicationRegistry from '#composition/Application.Registry';

function AddClientContent() {
  const { getPayload, resetForm } = useClientForm();
  const navigate = useNavigate();

  const {
    apply, data, isLoading, error,
  } = useCreateClient();
  const { getToken } = useContext(ApplicationRegistry.AuthContext);

  const handleSaveClick = async () => {
    try {
      const token = await getToken() || '';
      const formPayload = getPayload();
      const apiPayload = {
        name: formPayload.clientType === ClientType.COMPANY
          ? formPayload.companyName || ''
          : `${formPayload.firstName} ${formPayload.lastName}`,
        tributaryId: formPayload.clientType === ClientType.COMPANY
          ? formPayload.tributaryId || ''
          : `${formPayload.firstName}${formPayload.lastName}`.toLowerCase().replace(/\s+/g, ''),
        clientCompanyId: null,
        storeDiscounts: [],
        contactInformation: {
          billingEmail: formPayload.alternativeContacts?.billing?.email || formPayload.email,
          billingPhoneNumber: formPayload.alternativeContacts?.billing?.phone || formPayload.phone,
          billingWhatsapp: formPayload.alternativeContacts?.billing?.whatsapp || (formPayload.isWhatsApp ? formPayload.phone : formPayload.whatsappNumber),
          purchasesEmail: formPayload.alternativeContacts?.purchases?.email || formPayload.email,
          purchasesPhoneNumber: formPayload.alternativeContacts?.purchases?.phone || formPayload.phone,
          purchasesWhatsapp: formPayload.alternativeContacts?.purchases?.whatsapp || (formPayload.isWhatsApp ? formPayload.phone : formPayload.whatsappNumber),
          salesEmail: formPayload.email,
          salesPhoneNumber: formPayload.phone,
          salesWhatsapp: formPayload.isWhatsApp ? formPayload.phone : formPayload.whatsappNumber,
          shippingAddress: formPayload.clientAddress || '',
          billingAddress: formPayload.clientAddress || '',
        },
      };

      await apply({ params: apiPayload, token });
    } catch (err) {
      // Error handling is managed by the useCreateClient hook
    }
  };

  useEffect(() => {
    if (data) {
      resetForm();
      navigate('/app/clients');
    }
  }, [data, navigate, resetForm]);

  return (
    <div className="min-h-screen bg-transparent p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between w-full mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Nuevo Cliente</h1>
            <p className="text-sm text-gray-600">Crea un nuevo cliente en el sistema</p>
          </div>
          <Button
            variant="primary"
            size="small"
            onClick={handleSaveClick}
            disabled={isLoading}
          >
            {isLoading ? 'Guardando...' : 'Guardar cambios'}
          </Button>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error.message}</p>
          </div>
        )}

        {/* Formulario */}
        <AddClientForm />
      </div>
    </div>
  );
}

export function AddClientModule() {
  return (
    <ClientFormProvider>
      <AddClientContent />
    </ClientFormProvider>
  );
}
