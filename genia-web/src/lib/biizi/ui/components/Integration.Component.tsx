import {
  Badge,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  FormInputType,
  IconImporter,
  IconName,
  InputComponent,
  Label,
} from '@pitsdepot/storybook';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';

import TextService from '#composition/textService/Text.Service';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';

export enum IntegrationStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  ERROR = 'error',
  DISCONNECT_ERROR = 'disconnect_error',
}

export interface IntegrationComponentProps {
  id?: string,
  icon: IconName,
  name: string,
  type: 'whatsapp' | 'gmail',
  color: string,
  description: string,
  status: IntegrationStatus,
  params?: Record<string, string>,
  onConnect?: (params?: Record<string, string>) => void,
  onDisconnect?: () => void,
}

const { common, integration: integrationText } = TextService.getText();

// WhatsApp Configuration Component
function WhatsAppConfigurationForm({
  integration,
  onCancel,
  onConnect,
}: {
  integration: IntegrationComponentProps,
  onCancel: () => void,
  onConnect: (params: Record<string, string>) => void,
}) {
  const [whatsappToken, setWhatsappToken] = useState<string>('');
  const [whatsappPhoneId, setWhatsappPhoneId] = useState<string>('');
  const [whatsappVerificationToken, setWhatsAppVerificationToken] = useState<string>('');
  const [showRequiredFields, setShowRequiredFields] = useState<boolean>(false);

  const handleSaveConfiguration = () => {
    if (!whatsappToken || !whatsappPhoneId || !whatsappVerificationToken) {
      setShowRequiredFields(true);
      return;
    }

    onConnect({
      whatsappToken,
      whatsappPhoneId,
      whatsappVerificationToken,
    });
  };

  useEffect(() => {
    if (integration.id) {
      setWhatsappPhoneId(integration.params?.whatsappPhoneId || '');
      setWhatsappToken('AuthenticationTokenPlaceholder');
      setWhatsAppVerificationToken('verificationTokenPlaceholder');
    }
  }, [integration.id]);

  useEffect(() => {
    if (integration.status === IntegrationStatus.DISCONNECTED) {
      setWhatsappPhoneId('');
      setWhatsappToken('');
      setWhatsAppVerificationToken('');
      setShowRequiredFields(false);
    }
  }, [integration.status]);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20, height: 0 }}
      animate={{ opacity: 1, y: 0, height: 'auto' }}
      exit={{ opacity: 0, y: -20, height: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className="overflow-hidden"
    >
      <Card className="mt-4">
        <CardHeader>
          <CardTitle>
            <span>{integrationText.apiConfiguration}</span>
          </CardTitle>
          <CardDescription>
            {integrationText.apiConfigurationDescription}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {integration.status === IntegrationStatus.ERROR && (
            <div className="bg-red-100 border border-red-200 text-red-800 p-4 rounded-lg">
              <div className="flex items-center">
                <IconImporter
                  name="exclamationMark"
                  size={20}
                  className="text-red-600 mr-1 mt-0.5 flex-shrink-0 bg-red-300 rounded-full p-1"
                />
                <p className="text-red-600 text-sm gap-2 items-center">
                  {integrationText.integrationLoadError}
                </p>
              </div>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="whatsappToken" className="flex gap-2">
                {integrationText.whatsappBusinessToken}
                <p className='text-negative text-xs'>{!whatsappToken && showRequiredFields ? common.requiredField : ''}</p>
              </Label>
              <InputComponent
                id="whatsappToken"
                name="whatsappToken"
                placeholder={integrationText.whatsappTokenPlaceholder}
                value={whatsappToken}
                inputType={'password' as FormInputType}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setWhatsappToken(e.currentTarget.value)}
                disabled={integration.status === IntegrationStatus.CONNECTED || integration.status === IntegrationStatus.ERROR }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="whatsappPhoneId" className="flex gap-2">
                {integrationText.whatsappPhoneNumberId}
                <p className='text-negative text-xs'>{!whatsappPhoneId && showRequiredFields ? common.requiredField : ''}</p>
              </Label>
              <InputComponent
                id="whatsappPhoneId"
                name="whatsappPhoneId"
                value={whatsappPhoneId}
                placeholder={integrationText.phoneIdPlaceholder}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setWhatsappPhoneId(e.currentTarget.value)}
                disabled={integration.status === IntegrationStatus.CONNECTED || integration.status === IntegrationStatus.ERROR }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="whatsappVerificationToken" className="flex gap-2">
                {integrationText.whatsappVerificationToken}
                <p className='text-negative text-xs'>{!whatsappVerificationToken && showRequiredFields ? common.requiredField : ''}</p>
              </Label>
              <InputComponent
                id="whatsappVerificationToken"
                name="whatsappVerificationToken"
                value={whatsappVerificationToken}
                placeholder={integrationText.whatsappVerificationToken}
                inputType={'password' as FormInputType}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setWhatsAppVerificationToken(e.currentTarget.value)}
                disabled={integration.status === IntegrationStatus.CONNECTED || integration.status === IntegrationStatus.ERROR }
              />
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between pt-4">
              <div className="space-y-0.5">
                <h3 className="font-medium">{integrationText.whatsappWebhook}</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  {integrationText.whatsappWebhookDescription}
                </p>
                {integration.id ? (
                  <code className="text-xs bg-fadedGreen p-2 rounded">
                    {GetEnv().CLAUDIA_NODE_API}/whatsapp/webhook/{integration.id}
                  </code>
                ) : (
                  <p className="text-xs text-muted-foreground italic p-2 bg-gray-50 rounded">
                    {integrationText.configureParametersToSeeUrl}
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            {integration.status !== IntegrationStatus.CONNECTED && (
              <Button
                className="gap-2"
                onClick={handleSaveConfiguration}
                type="button"
                disabled={integration.status === IntegrationStatus.ERROR}
              >
                <IconImporter name="floppyDisk" className="h-4 w-4" />
                {integrationText.saveConfiguration}
              </Button>
            )}
            <Button
              variant="outlined"
              onClick={onCancel}
            >
              {common.close}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function IntegrationComponent(integration: IntegrationComponentProps) {
  const [showWhatsAppConfig, setShowWhatsAppConfig] = useState(false);

  const handleConnect = () => {
    if (integration.type === 'whatsapp') {
      setShowWhatsAppConfig(true);
    } else {
      integration.onConnect?.();
    }
  };

  const handleCancelWhatsAppConfig = () => {
    setShowWhatsAppConfig(false);
  };

  const handleWhatsAppConnect = (params: Record<string, string>) => {
    integration.onConnect?.(params);
  };
  const getStatusBadge = () => {
    switch (integration.status) {
      case IntegrationStatus.CONNECTED:
        return (
          <Badge className="!bg-green-100 !text-green-800 !border-green-200">
            <IconImporter name="checkCircle" className="h-3 w-3 mr-1" />
            {common.connected}
          </Badge>
        );
      case IntegrationStatus.CONNECTING:
        return (
          <Badge className="!bg-blue-100 !text-blue-800 !border-blue-200">
            <div className="h-3 w-3 mr-1 animate-spin rounded-full border border-blue-600 border-t-transparent" />
            {common.connecting}...
          </Badge>
        );
      case IntegrationStatus.ERROR:
        return (
          <Badge className="!bg-red-100 !text-red-800 !border-red-200">
            <IconImporter name="exclamationMark" className="h-3 w-3 mr-1" />
            {integrationText.integrationError}
          </Badge>
        );
      case IntegrationStatus.DISCONNECT_ERROR:
        return (
          <Badge className="!bg-yellow-100 !text-yellow-800 !border-yellow-200">
            <IconImporter name="warning" className="h-3 w-3 mr-1" />
            {integrationText.disconnectError}
          </Badge>
        );
      default:
        return (
          <Badge className="!bg-gray-50 !text-gray-600 !border-gray-200">
            {common.disconnected}
          </Badge>
        );
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="!p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center bg-${integration.color}`}>
              <IconImporter name={integration.icon} className={'h-6 w-6 text-[rgba(0,0,0,0.5)]'} />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{integration.name}</h3>
              <p className="text-sm text-muted-foreground">
                {integration.description}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {getStatusBadge()}
            <div className="flex gap-2 items-center">
              {integration.type === 'whatsapp' && (
                <IconImporter
                  name={showWhatsAppConfig ? 'eyeSlash' : 'eye'}
                  className="text-md cursor-pointer text-gray-600 hover:text-gray-800 transition-colors"
                  onClick={() => setShowWhatsAppConfig(!showWhatsAppConfig)}
                />
              )}
              {integration.status === IntegrationStatus.CONNECTED || integration.status === IntegrationStatus.DISCONNECT_ERROR ? (
                <Button
                  onClick={() => integration.onDisconnect?.()}
                  variant="outlined"
                >
                  {common.disconnect}
                </Button>
              ) : (
                <Button
                  onClick={handleConnect}
                  disabled={integration.status === IntegrationStatus.CONNECTING}
                  className="gap-2"
                >
                  <IconImporter name="link" className="h-4 w-4" />
                  {integration.status === IntegrationStatus.CONNECTING ? `${common.connecting}...` : common.connect}
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <AnimatePresence>
        {showWhatsAppConfig && integration.type === 'whatsapp' && (
          <WhatsAppConfigurationForm
            integration={integration}
            onCancel={handleCancelWhatsAppConfig}
            onConnect={handleWhatsAppConnect}
          />
        )}
      </AnimatePresence>
    </Card>
  );
}
