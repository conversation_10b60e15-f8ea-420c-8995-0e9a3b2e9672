import type { OptionsDropdownProps } from '@pitsdepot/storybook';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardT<PERSON>le,
  DropdownSimple,
  FormInputType,
  IconImporter,
  InputComponent,
  Label,
  TextAreaInput,
} from '@pitsdepot/storybook';
import { motion } from 'framer-motion';

import { ClientType } from '#/lib/application/client/Client.Type';
import { useClientForm } from '#/lib/biizi/ui/hooks/Client.Hook';
import textService from '#/lib/composition/textService/Text.Service';

export function AddClientForm() {
  const {
    formData, updateField,
  } = useClientForm();

  const text = textService.getText();

  const clientTypeOptions = [
    { id: ClientType.CLIENT, name: text.clients.personOption },
    { id: ClientType.COMPANY, name: text.clients.companyOption },
  ];

  const handleInputChange = <K extends keyof typeof formData>(field: K, value: typeof formData[K]) => {
    update<PERSON><PERSON>(field, value);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.4 }}
      className="max-w-4xl mx-auto space-y-6"
    >
      <Card className='bg-white'>
        <CardHeader >
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.mainInformation}
          </CardTitle>
          <p className="text-sm text-gray-600">{text.clients.mainInformationDescription}</p>
        </CardHeader>
        <CardContent className="space-y-6">

          <div className="grid grid-cols-1 gap-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {text.clients.clientType}
                </Label>
                <DropdownSimple
                  options={clientTypeOptions}
                  setSelectedOption={(option: OptionsDropdownProps) => handleInputChange('clientType', option.id as ClientType)}
                  showAvatar={false}
                >
                  <div
                    className="w-full px-3 py-[6px] border border-gray-300 rounded-md
                    focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer
                    flex items-center justify-between"
                  >
                    <span>{clientTypeOptions.find((type) => type.id === formData.clientType)?.name}</span>
                    <IconImporter name="caretDown" />
                  </div>
                </DropdownSimple>
              </div>

              {formData.clientType === ClientType.COMPANY ? (
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.contactPersonName}
                  </Label>
                  <InputComponent
                    name="contactPerson"
                    placeholder={text.clients.contactPersonName}
                    value={formData.contactPerson || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('contactPerson', e.target.value)}
                  />
                </div>
              ) : (
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.fullName}
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <InputComponent
                        name="firstName"
                        placeholder={text.clients.firstNamePlaceholder}
                        value={formData.firstName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('firstName', e.target.value)}
                      />
                    </div>
                    <div className="space-y-1">
                      <InputComponent
                        name="lastName"
                        placeholder={text.clients.lastNamePlaceholder}
                        value={formData.lastName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('lastName', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {formData.clientType === ClientType.COMPANY && (
              <>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.companyName}
                  </Label>
                  <InputComponent
                    name="companyName"
                    placeholder={text.clients.companyNamePlaceholder}
                    value={formData.companyName || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('companyName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.tributaryIdLabel}
                  </Label>
                  <InputComponent
                    name="tributaryId"
                    placeholder={text.clients.tributaryIdPlaceholder}
                    value={formData.tributaryId || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('tributaryId', e.target.value)}
                  />
                </div>
              </>
            )}
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.clientAddress}
            </Label>
            <InputComponent
              name="clientAddress"
              placeholder={text.clients.clientAddressPlaceholder}
              value={formData.clientAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('clientAddress', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:col-span-2">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {text.clients.email}
              </Label>
              <InputComponent
                name="email"
                inputType={'email' as FormInputType}
                placeholder={text.clients.emailPlaceholder}
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('email', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {text.clients.phone}
              </Label>
              <InputComponent
                name="phone"
                placeholder={text.clients.phonePlaceholder}
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('phone', e.target.value)}
              />
              <div className="flex items-center space-x-2 pt-2">
                <input
                  type="checkbox"
                  id="isWhatsApp"
                  checked={formData.isWhatsApp}
                  onChange={(e) => handleInputChange('isWhatsApp', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Label
                  htmlFor="isWhatsApp"
                  className="text-sm font-medium text-gray-700"
                >
                  {text.clients.isWhatsApp}
                </Label>
              </div>
            </div>
          </div>

          {!formData.isWhatsApp && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.whatsappNumber}
            </Label>
            <InputComponent
              name="whatsappNumber"
              placeholder={text.clients.whatsappPlaceholder}
              value={formData.whatsappNumber}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('whatsappNumber', e.target.value)}
                />
          </div>
          )}

          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.location}
            </Label>
            <InputComponent
              name="location"
              placeholder={text.clients.locationPlaceholder}
              value={formData.location}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('location', e.target.value)}
              />
          </div>
        </CardContent>
      </Card>

      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.alternativeContacts}
          </CardTitle>
          <p className="text-sm text-gray-600">
            {text.clients.alternativeContactsDescription}
          </p>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">{text.clients.billing}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.emailLabel}</Label>
                <InputComponent
                  name="billingEmail"
                  placeholder="<EMAIL>"
                  value={formData.billingEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('billingEmail', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.phoneLabel}</Label>
                <InputComponent
                  name="billingPhone"
                  placeholder="+52 ************"
                  value={formData.billingPhone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('billingPhone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.whatsappLabel}</Label>
                <InputComponent
                  name="billingWhatsApp"
                  placeholder="+52 ************"
                  value={formData.billingWhatsApp}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('billingWhatsApp', e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">{text.clients.purchases}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.emailLabel}</Label>
                <InputComponent
                  name="purchasesEmail"
                  placeholder="<EMAIL>"
                  value={formData.purchasesEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('purchasesEmail', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.phoneLabel}</Label>
                <InputComponent
                  name="purchasesPhone"
                  placeholder="+52 ************"
                  value={formData.purchasesPhone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('purchasesPhone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.whatsappLabel}</Label>
                <InputComponent
                  name="purchasesWhatsApp"
                  placeholder="+52 ************"
                  value={formData.purchasesWhatsApp}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('purchasesWhatsApp', e.target.value)}
                />
              </div>
            </div>
          </div>

        </CardContent>
      </Card>

      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.notes}
          </CardTitle>
          <p className="text-sm text-gray-600">{text.clients.notesDescription}</p>
        </CardHeader>
        <CardContent>
          <TextAreaInput
            name="notes"
            placeholder={text.clients.notesPlaceholder}
            value={formData.notes}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('notes', e.target.value)}
            rows={4}
            className="w-full"
          />
        </CardContent>
      </Card>

    </motion.div>
  );
}
