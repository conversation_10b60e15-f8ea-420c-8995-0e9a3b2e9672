import CreateIntegrationUseCase from '#/lib/biizi/application/Integration/useCases/CreateIntegration.UseCase';
import DeleteIntegrationUseCase from '#/lib/biizi/application/Integration/useCases/DeleteIntegration.UseCase';
import FindIntegrationsUseCase from '#/lib/biizi/application/Integration/useCases/FindIntegrations.UseCase';
import { WrappedReadUseCaseHook, WrapWriteUseCaseHook } from '#/lib/biizi/ui/utils/Hook.Util';

export const useConnectIntegration = WrapWriteUseCaseHook(CreateIntegrationUseCase.apply);
export const useFindIntegrations = WrappedReadUseCaseHook(FindIntegrationsUseCase.apply);
export const useDisconnectIntegration = WrapWriteUseCaseHook(DeleteIntegrationUseCase.apply);
