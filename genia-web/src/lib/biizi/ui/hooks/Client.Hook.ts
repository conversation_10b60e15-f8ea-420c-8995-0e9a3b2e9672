import { useContext } from 'react';

import CreateClientUseCase from '#/lib/biizi/application/Client/useCases/CreateClient.UseCase';
import { ClientFormContext } from '#/lib/biizi/ui/contexts/ClientFormContext';
import { WrapWriteUseCaseHook } from '#/lib/biizi/ui/utils/Hook.Util';

export const useCreateClient = WrapWriteUseCaseHook(CreateClientUseCase.apply);

export function useClientForm() {
  const context = useContext(ClientFormContext);
  if (context === undefined) {
    throw new Error('useClientForm must be used within a ClientFormProvider');
  }
  return context;
}
