import { Client } from '#application/client/Client.Type';
import ClientEntity from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

async function apply({ params, token }: { params: Omit<Client, 'createdAt' | 'updatedAt' | 'id'>, token: string }): Promise<ClientEntity> {
  const newClient = await ApplicationRegistry.ClientRepository.createClient({ ...params, token });

  return newClient;
}

const CreateClientUseCase = {
  apply,
};

export default CreateClientUseCase;
