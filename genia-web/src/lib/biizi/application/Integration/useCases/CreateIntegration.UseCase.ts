import IntegrationEntity from '#/lib/biizi/domain/aggregates/integration/Integration.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

export type CreateIntegrationUseCaseParams = {
  type: string;
  params?: Record<string, string>;
};

async function apply(params: CreateIntegrationUseCaseParams) : Promise<IntegrationEntity> {
  const { authCode } = await ApplicationRegistry.IntegrationConnectionService.connect({
    type: params.type,
  });

  const newIntegration = await ApplicationRegistry.IntegrationRepository.createIntegration({
    ...params,
    token: authCode,
  });

  return newIntegration;
}

const CreateIntegrationUseCase = {
  apply,
};

export default CreateIntegrationUseCase;
