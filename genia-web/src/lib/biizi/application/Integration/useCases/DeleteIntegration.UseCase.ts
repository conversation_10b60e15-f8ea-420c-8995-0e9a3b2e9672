import ApplicationRegistry from '#composition/Application.Registry';

export interface DeleteIntegrationCommand {
  id: string;
}

async function apply(params: DeleteIntegrationCommand) : Promise<{status: string}> {
  await ApplicationRegistry.IntegrationRepository.deleteIntegration(params.id);

  return { status: 'success' };
}

const DeleteIntegrationUseCase = {
  apply,
};

export default DeleteIntegrationUseCase;
