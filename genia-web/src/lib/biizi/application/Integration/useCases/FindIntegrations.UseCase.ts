import IntegrationEntity from '#/lib/biizi/domain/aggregates/integration/Integration.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

async function apply() : Promise<IntegrationEntity[]> {
  const integrations = await ApplicationRegistry.IntegrationRepository.findAllIntegrations();

  return integrations;
}

const FindIntegrationsUseCase = {
  apply,
};

export default FindIntegrationsUseCase;
