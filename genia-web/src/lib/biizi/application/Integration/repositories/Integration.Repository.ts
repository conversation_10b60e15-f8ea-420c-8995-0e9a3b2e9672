import IntegrationEntity, { Integration } from '#/lib/biizi/domain/aggregates/integration/Integration.Entity';

export interface IntegrationRepositoryCreateParams extends Omit<Integration, 'createdAt' | 'updatedAt' | 'id'> {
  type: string;
  token: string;
}

export default interface IntegrationRepository {
  createIntegration(integration: IntegrationRepositoryCreateParams): Promise<IntegrationEntity>;
  deleteIntegration(id: string): Promise<void>;
  findAllIntegrations(): Promise<IntegrationEntity[]>;
}
