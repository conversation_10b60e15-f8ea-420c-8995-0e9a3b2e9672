/* eslint-disable import/no-extraneous-dependencies */
import path from 'path';

import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import EnvironmentPlugin from 'vite-plugin-environment';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), EnvironmentPlugin('all')],
  css: {
    postcss: './postcss.config.js',
  },
  server: {
    port: Number.parseInt(process.env.PORT || '3010', 10),
  },
  resolve: {
    alias: {
      '#application': path.resolve(__dirname, './src/lib/application'),
      '#composition': path.resolve(__dirname, './src/lib/composition'),
      '#domain': path.resolve(__dirname, './src/lib/domain'),
      '#appComponent': path.resolve(__dirname, './src/lib/appComponent'),
      '#infrastructure': path.resolve(__dirname, './src/lib/infrastructure'),
      '#layout': path.resolve(__dirname, './src/lib/layout'),
      '#': path.resolve(__dirname, './src'),
    },
  },
});
